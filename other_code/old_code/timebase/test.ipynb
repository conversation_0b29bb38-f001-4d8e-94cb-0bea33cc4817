{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import datetime\n", "import tbapi"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Timebase URL specification, pattern is \"dxtick://<host>:<port>\"\n", "timebase = 'dxtick://localhost:8011'"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def create_stream_old():\n", "    try:\n", "        # Create timebase connection\n", "        db = tbapi.TickDb.createFromUrl(timebase)\n", "\n", "        # Open in write mode\n", "        db.open(False)\n", "        \n", "        # QQL create stream DDL\n", "        barsQQL = \"\"\"\n", "    CREATE DURABLE STREAM \"bars1min\" 'bars1min' (\n", "        CLASS \"deltix.timebase.api.messages.MarketMessage\" 'Market Message' (\n", "            STATIC \"originalTimestamp\" TIMESTAMP = NULL,\n", "            STATIC \"currencyCode\" 'Currency Code' INTEGER = 999,\n", "            STATIC \"sequenceNumber\" '' INTEGER = NULL,\n", "            STATIC \"sourceId\" '' VARCHAR = NULL\n", "        ) NOT INSTANTIABLE;\n", "        CLASS \"deltix.timebase.api.messages.BarMessage\" 'Bar Message' UNDER \"deltix.timebase.api.messages.MarketMessage\" (\n", "            \"exchangeId\" 'Exchange Code' VARCHAR,\n", "            \"close\" 'Close' FLOAT DECIMAL64,\n", "            \"open\" 'Open' FLOAT DECIMAL64 RELATIVE TO \"close\",\n", "            \"high\" 'High' FLOAT DECIMAL64 RELATIVE TO \"close\",\n", "            \"low\" 'Low' FLOAT DECIMAL64 RELATIVE TO \"close\",\n", "            \"volume\" 'Volume' FLOAT DECIMAL64\n", "        );\n", "    )\n", "    OPTIONS (FIXEDTYPE; PERIODICITY = '1I'; HIGHAVAILABILITY = TRUE)\n", "    COMMENT 'bars1min'\n", "        \"\"\"\n", "\n", "        # execute QQL and check result\n", "        cursor = db.executeQuery(barsQQL)\n", "        try:\n", "            if (cursor.next()):\n", "                message = cursor.getMessage()\n", "                print('Query result: ' + message.messageText)\n", "        finally:\n", "            if (cursor != None):\n", "                cursor.close()\n", "        \n", "    finally:  # database connection should be closed anyway\n", "        if (db.is<PERSON>pen()):\n", "            db.close()\n", "        print(\"Connection \" + timebase + \" closed.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def create_stream():\n", "    \n", "    # Timebase URL specification, pattern is \"dxtick://<host>:<port>\"\n", "    timebase = 'dxtick://localhost:8011'\n", "    try:\n", "        # Create timebase connection\n", "        db = tbapi.TickDb.createFromUrl(timebase)\n", "\n", "        # Open in write mode\n", "        db.open(False)\n", "\n", "        print('Connected to ' + timebase)\n", "        \n", "        createStreamQql(db, 'universal')\n", "        createStreamQql(db, 'bars')\n", "        createStreamQql(db, 'trade_bbo')\n", "    finally:  # database connection should be closed anyway\n", "        if (db.is<PERSON>pen()):\n", "            db.close()\n", "        print(\"Connection \" + timebase + \" closed.\")\n", "            \n", "def createStreamQql(db, streamKey):\n", "    # read QQL from file\n", "    with open(streamKey + '.qql', 'r') as qqlFile:\n", "        qql = qqlFile.read()\n", "\n", "    # execute QQL and check result\n", "    with db.try<PERSON><PERSON><PERSON>(qql) as cursor:\n", "        if (cursor.next()):\n", "            print('Query result: ' + cursor.getMessage().messageText)\n", "    \n", "    # request newly created stream\n", "    stream = db.getStream(streamKey)\n", "    if stream == None:\n", "        raise Exception('Failed to create stream')\n", "    else:\n", "        print(\"Stream \" + streamKey + \" created\")   "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def write_stream():\n", "    try:\n", "        # Create timebase connection\n", "        db = tbapi.TickDb.createFromUrl(timebase)\n", "        \n", "        # Open in read-write mode\n", "        db.open(False)\n", "        \n", "        print('Connected to ' + timebase)\n", "\n", "        # Define name of the stream    \n", "        streamKey = 'bars'\n", "        \n", "        # Get stream from the timebase\n", "        stream = db.getStream(streamKey)\n", "        \n", "        # if stream not found, try to create new\n", "        if stream == None:\n", "            print('Stream ' + streamKey + ' not exitsts, abort...')\n", "            return\n", "        else:\n", "            print(f\"Stream {streamKey} exists and was loaded\")\n", "        \n", "        # Create a Message Loader for the selected stream and provide loading options\n", "        loader = stream.createLoader(tbapi.LoadingOptions())\n", "        \n", "        # Create BestBidOffer message\n", "        barMessage = tbapi.InstrumentMessage()\n", "        \n", "        # Define message type name according to the Timebase schema type name\n", "        barMessage.typeName = 'com.epam.deltix.timebase.messages.BarMessage'\n", "        \n", "        print('Start loading to ' + streamKey)    \n", "        \n", "        for i in range(100):    \n", "            # Define instrument information\n", "            barMessage.symbol = 'AAPL' if i % 2 == 0 else 'MSFT'\n", "                        \n", "            # Define other message properties\n", "            barMessage.originalTimestamp = 0\n", "            \n", "            # 'undefined' currency code\n", "            barMessage.currencyCode = 999\n", "            barMessage.exchangeId = 'NYSE'\n", "            barMessage.open = 10.0 + i * 2.2\n", "            barMessage.close = 20.0 + i * 3.3\n", "            barMessage.high = 30.0 + i * 4.4\n", "            barMessage.low = 40.0 + i * 5.5\n", "            barMessage.volume = 60.0 + i * 6.6\n", "            \n", "            # Send message\n", "            loader.send(barMessage)\n", "                \n", "        # close Message Loader\n", "        loader.close()\n", "        loader = None\n", "    finally:\n", "        # database connection should be closed anyway\n", "        if db.is<PERSON>pen():\n", "            db.close()\n", "            print(\"Connection \" + timebase + \" closed.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def read_stream():\n", "  \n", "    try:\n", "        db = tbapi.TickDb.createFromUrl(timebase)\n", "        db.open(True)\n", "\n", "        streamKey = 'bars'\n", "        stream = db.getStream(streamKey)\n", "        stream_options = tbapi.SelectionOptions()\n", "        \n", "        cursor = stream.select(0, stream_options, None, None)\n", "        try:\n", "            while cursor.next():\n", "                message = cursor.getMessage()\n", "                print(message)\n", "            \n", "        finally:\n", "            # cursor should be closed anyway\n", "            cursor.close()\n", "            cursor = None\n", "            \n", "    finally:\n", "        # database connection should be closed anyway\n", "        if (db.is<PERSON>pen()):\n", "            db.close()\n", "            print(\"Connection \" + timebase + \" closed.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["read_stream()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}