import redis as rd

#pubsub event handler
def event_handler(payload):
    msg = payload["data"]
    print(msg)

def Main():
    
    #redis 
    r = rd.Redis(host="localhost", port=6379, decode_responses=False)
    pubsub = r.pubsub()
    
    #subscribe
    subscribe_key = "from-server"
    pubsub.psubscribe(**{subscribe_key: event_handler})
    pubsub.run_in_thread(sleep_time=.01)
        
    while True:
        msg = input("Enter message to send, or x to quit")
      
        if msg == "x":
            break
 
        #send message
        r.publish("to-server", msg)
    
    pubsub.unsubscribe("from_server")
    
    
    
if __name__ == '__main__':
    Main()