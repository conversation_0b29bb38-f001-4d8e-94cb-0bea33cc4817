{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "import duckdb\n", "import polars as pl\n", "\n", "duckdb_con = duckdb.connect(\"stock_db.db\")\n", "duckdb_con.execute(\"SET TIMEZONE='UTC'\")\n", "\n", "sys.path.insert(0, '/home/<USER>/development/python_development')\n", "from mattlibrary.datamanagement.polygon import PolygonClient\n", "from mattlibrary.datamanagement.clickhouse_client import ClickHouseClient"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["start_date = \"2014-02-10\"\n", "end_date = \"2024-02-09\"\n", "multiplier = 1\n", "frequency = \"day\"\n", "client = PolygonClient(\"********************************\")\n", "\n", "#load tickerlist from disk\n", "tickerlist = client.load_tickerlist_from_disk(\"tickerlist.db\")\n", "\n", "#load historical data from api\n", "counter = 1\n", "for symbol in tickerlist:\n", "    print(f\"Getting data for {symbol} ({counter}/{len(tickerlist)})\")\n", "\n", "    try:\n", "        df = client.get_ohlc_data(symbol, start_date, end_date, multiplier, frequency, True)\n", "    \n", "        if counter == 1:\n", "            #create duckdb table\n", "            duckdb_con.sql(\"CREATE TABLE polygon_stocks_daily AS SELECT * FROM df\")\n", "        \n", "        duckdb_con.sql(\"INSERT INTO polygon_stocks_daily SELECT * FROM df\")\n", "        \n", "        #increment counter\n", "        counter += 1\n", "    \n", "    except Exception as e:\n", "        print(f\"Error getting data for {symbol}: {e}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#get data from duckdb\n", "print(duckdb_con.sql(\"SELECT count(*) FROM polygon_stocks_daily\").df())\n", "\n", "#filter out duplicates\n", "data = duckdb_con.sql(\"SELECT DISTINCT(*) FROM polygon_stocks_daily\").df()\n", "\n", "#convert duckdb dataframe to polars dataframe\n", "data = pl.DataFrame(data)\n", "\n", "#initialize clickhouse client\n", "clickhouse_client = ClickHouseClient()\n", "\n", "#create table without partitioning\n", "clickhouse_client.create_table(\"polygon_stocks_daily\", \n", "    [(\"symbol\", \"LowCardinality(String)\"), (\"datetime\", \"Datetime64(3, 'UTC') CODEC(DoubleDelta, ZSTD(1))\"),\n", "        (\"open\", \"Float32 CODEC(Gorilla, ZSTD(1))\"), (\"high\", \"Float32 CODEC(Gorilla, ZSTD(1))\"),\n", "        (\"low\", \"Float32 CODEC(Gorilla, ZSTD(1))\"), (\"close\", \"Float32 CODEC(Gorilla, ZSTD(1))\"),\n", "        (\"volume\", \"Int32\")],\n", "    \"MergeTree\", \"\", \"(symbol, datetime)\")\n", "  \n", "#write data to clickhouse\n", "clickhouse_client.write_dataframe(\"polygon_stocks_daily\", data)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 2}