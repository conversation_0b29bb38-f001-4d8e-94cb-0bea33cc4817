{"cells": [{"cell_type": "markdown", "source": ["# Training FX Baskets Prediction/Classification"], "metadata": {}}, {"cell_type": "markdown", "source": ["## Package Imports and Hardware Configurations"], "metadata": {}}, {"cell_type": "code", "execution_count": 1, "source": ["import os\r\n", "import sys\r\n", "import shutil\r\n", "import tensorflow as tf\r\n", "import pandas as pd\r\n", "import numpy as np\r\n", "import matplotlib.pyplot as plt\r\n", "import seaborn as sns\r\n", "from sklearn.preprocessing import MinMaxScaler\r\n", "import tensorflow as tf\r\n", "from tensorflow import keras\r\n", "from tensorflow.keras.models import Sequential\r\n", "from tensorflow.keras.layers import LSTM\r\n", "from tensorflow.keras.layers import Dense\r\n", "from tensorflow.keras.layers import Dropout\r\n", "\r\n", "sys.path.append(\"..\")\r\n", "from mattlibrary import mattlibrary\r\n", "\r\n", "#this allows GPU memory to grow (prevents allocation of the entire memory)\r\n", "gpus = tf.config.experimental.list_physical_devices('GPU')\r\n", "for gpu in gpus:\r\n", "  print(\"Found GPU\")\r\n", "  tf.config.experimental.set_memory_growth(gpu, True)\r\n", "\r\n", "#set whether to run eagerly or build a graph\r\n", "tf.executing_eagerly = False"], "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Found GPU\n"]}], "metadata": {}}, {"cell_type": "markdown", "source": ["## Train Model"], "metadata": {}}, {"cell_type": "code", "execution_count": 2, "source": ["def train_model():\r\n", "\r\n", "    static_data = mattlibrary.StaticData()\r\n", "    \r\n", "    for basket_id in static_data.fx_baskets.keys():\r\n", "\r\n", "        #region configuration and model parameters\r\n", "\r\n", "        model_name = basket_id + \"_Model\"\r\n", "        datetime_from = \"2000-01-01\"\r\n", "        datetime_to = \"2022-01-01\"\r\n", "        model_directory = os.path.join(os.getcwd(), model_name)\r\n", "        data_path_filename = os.path.abspath(os.path.join(os.getcwd(), \"..\",\"raw_data\", \"raw_data_bloomberg.csv\"))\r\n", "        # data_path_filename = os.path.abspath(os.path.join(os.getcwd(), \"..\",\"raw_data\", \"raw_data_bloomberg_ohlc.csv\"))\r\n", "                \r\n", "        print(f\"\\nTraining the following Moddel: {model_name}\\n\")\r\n", "\r\n", "        basket = static_data.fx_baskets[basket_id]\r\n", "        raw_features = list(basket.basket_currencies)\r\n", "        raw_features.extend([\"EURODOLLAR\", \"5YEARBOND\", \"10YEARBOND\", \"30YEARBOND\", \"CRUDEOIL\", \"NATGAS\", \"GOLD\", \"VIX\", \"SPX500\"])\r\n", "\r\n", "        features = list()\r\n", "        # features.append(basket.basket_id)\r\n", "        # for feature in raw_features:\r\n", "        #     features.append(feature + \"_OPEN\")\r\n", "        #     features.append(feature + \"_HIGH\")\r\n", "        #     features.append(feature + \"_LOW\")\r\n", "        #     features.append(feature + \"_CLOSE\")\r\n", "        \r\n", "        #this is just for test purposes\r\n", "        features = raw_features\r\n", "        features.append(basket.basket_id)\r\n", "\r\n", "        model_parameters = mattlibrary.ModelParameters(\r\n", "            model_name = model_name,\r\n", "            features = features,\r\n", "            label = basket_id,\r\n", "            classify_predict = \"classify\", \r\n", "            remove_data_beyond_stdev = 8,\r\n", "            is_retain_best_model = True,\r\n", "            is_early_stopping = False,\r\n", "            is_store_tensorboard_data = False, \r\n", "            is_plot_realtime_tensorboard = False,\r\n", "            epochs = 600, \r\n", "            sequence_length = 20,\r\n", "            label_length = 1, \r\n", "            batch_size = 1024,\r\n", "            train_test_ratio = 0.90,\r\n", "            is_shuffle = True, \r\n", "            shuffle_seed = None)\r\n", "\r\n", "        #create (empty) directory\r\n", "        if os.path.exists(model_directory):\r\n", "            shutil.rmtree(model_directory)\r\n", "        os.mkdir(model_directory)\r\n", "\r\n", "        #store model parameters on disk\r\n", "        model_parameters_filename = os.path.join(model_directory, \"model_parameters.json\")\r\n", "        with open(model_parameters_filename, \"w\") as json_file:\r\n", "            json_data = model_parameters.to_json()\r\n", "            json_file.write(json_data)\r\n", "\r\n", "        #endregion\r\n", "\r\n", "        #region create dataset\r\n", "\r\n", "        #load data\r\n", "        df = pd.read_csv(data_path_filename, header=0, index_col=0, parse_dates=[\"Date\"], dtype=np.float64)\r\n", "\r\n", "        #remove rows that do not lie between datetime_start and datetime_end\r\n", "        df = mattlibrary.filter_dataframe_by_dates(df, datetime_from, datetime_to)\r\n", "\r\n", "        #fill missing values\r\n", "        df = df.ffill(axis=0)\r\n", "\r\n", "        #convert data to percent changes\r\n", "        # df = mattlibrary.convert_dataframe_to_pctchange(df)\r\n", "        df = mattlibrary.convert_dataframe_to_logchange(df)\r\n", "\r\n", "        #remove NaN\r\n", "        df = mattlibrary.cleanse_df(df)\r\n", "\r\n", "        #derive fx basket\r\n", "        df[basket.basket_id] = mattlibrary.calculate_fx_basket_from_fx_returns(df, basket)\r\n", "        #df[basket.basket_id] = mattlibrary.calculate_fx_basket(df, basket)\r\n", "        \r\n", "        #only keep feature columns\r\n", "        df = mattlibrary.remove_columns(df, [col for col in df.columns if col not in model_parameters.features])\r\n", "        \r\n", "        #remove outliers\r\n", "        # returns_df = mattlibrary.remove_statistical_outliers(returns_df, model_parameters.remove_data_beyond_stdev)\r\n", "    \r\n", "        #make sure columns are sorted by name (to ensure scaler is applied to identical columns and order of features is identical between training and prediction stages)\r\n", "        df = mattlibrary.sort_df_by_column_names(df)\r\n", "        \r\n", "        # #split data\r\n", "        train_df, test_df = mattlibrary.split_data(df, model_parameters.train_test_ratio)\r\n", "        \r\n", "        # #create labels\r\n", "        train_labels = mattlibrary.generate_labels(train_df, model_parameters.classify_predict, model_parameters.label, lambda x: 1 if x >= 0 else 0)\r\n", "        test_labels = mattlibrary.generate_labels(test_df, model_parameters.classify_predict, model_parameters.label, lambda x: 1 if x >= 0 else 0)\r\n", "\r\n", "        # #normalize data\r\n", "        scaler_path_filename = os.path.join(model_directory, \"scaler.save\")\r\n", "        train_df, test_df = mattlibrary.normalize_train_and_test_data(train_df, test_df, -1, 1, scaler_path_filename)\r\n", "        \r\n", "        print(f\"\\nLabel used for training/testing: {train_labels.name}\")\r\n", "        print(f\"\\nColumns used for training/testing: {train_df.columns}\")\r\n", "\r\n", "        #create datasets\r\n", "        train_ds = mattlibrary.generate_dataset(train_df, train_labels, model_parameters.sequence_length, model_parameters.batch_size, model_parameters.is_shuffle)\r\n", "        test_ds = mattlibrary.generate_dataset(test_df, test_labels, model_parameters.sequence_length, model_parameters.batch_size, False)\r\n", "        \r\n", "        #endregion\r\n", "\r\n", "        #region create model\r\n", "\r\n", "        optimizer = keras.optimizers.<PERSON>()\r\n", "\r\n", "        if model_parameters.classify_predict == \"classify\":\r\n", "\r\n", "            loss = keras.losses.SparseCategoricalCrossentropy()\r\n", "            metrics = [\"sparse_categorical_accuracy\"]\r\n", "\r\n", "            #Model for Classification\r\n", "            model = Sequential()\r\n", "            model.add(LSTM(50, input_shape=(model_parameters.sequence_length, len(model_parameters.features)), activation=\"tanh\", return_sequences=True))\r\n", "            model.add(Dropout(0.7))\r\n", "            model.add(LSTM(50, input_shape=(model_parameters.sequence_length, len(model_parameters.features)), activation=\"tanh\", return_sequences=True))\r\n", "            model.add(Dropout(0.7))\r\n", "            model.add(LSTM(50, input_shape=(model_parameters.sequence_length, len(model_parameters.features)), activation=\"tanh\", return_sequences=False))\r\n", "            model.add(Dropout(0.7))\r\n", "            model.add(Dense(len(model_parameters.features), activation=\"relu\"))\r\n", "            # model.add(Dropout(0.5))\r\n", "            model.add(Den<PERSON>(2, activation=\"softmax\"))\r\n", "            model.compile(loss=loss, optimizer=optimizer, metrics=metrics)\r\n", "\r\n", "\r\n", "            # model = Sequential()\r\n", "            # model.add(Masking(mask_value=-10., input_shape=(day, num_samples, num_features)))\r\n", "            # model.add(TimeDistributed(LSTM(32, return_sequences=True, activation='tanh')))\r\n", "            # model.add(Dropout(0.3))\r\n", "            # model.add(TimeDistributed(LSTM(16, return_sequences=False, activation='tanh')))\r\n", "            # model.add(Dropout(0.3))\r\n", "            # model.add(<PERSON><PERSON>(16, activation='tanh'))\r\n", "            # model.add(Den<PERSON>(8, activation='tanh'))\r\n", "            # model.add(<PERSON><PERSON>(1))\r\n", "            # model.compile(loss='mse', optimizer='adam' ,metrics=['mae','mse'])\r\n", "            \r\n", "        elif model_parameters.classify_predict == \"predict\":\r\n", "\r\n", "            loss = \"mse\"\r\n", "            metrics = [\"mse\"]\r\n", "\r\n", "            #Model for Prediction\r\n", "            model = Sequential()\r\n", "            model.add(LSTM(len(basket.features), input_shape=(model_parameters.sequence_length, len(model_parameters.features))))\r\n", "            model.add(<PERSON><PERSON>(1))\r\n", "            model.compile(loss=loss, optimizer=optimizer, metrics=metrics)\r\n", "\r\n", "        else:\r\n", "            \r\n", "            raise ValueError(\"classify_predict has to be either 'classify' or 'predict'\")\r\n", "\r\n", "        #endregion\r\n", "\r\n", "        #region train the model\r\n", "\r\n", "        #save the entire model\r\n", "        model_path_filename = os.path.join(model_directory, \"saved_model.h5\")\r\n", "        model.save(model_path_filename, save_format=\"h5\")\r\n", "\r\n", "        #output model summary\r\n", "        print(model.summary())\r\n", "\r\n", "        callbacks = list()\r\n", "\r\n", "        if model_parameters.classify_predict == \"classify\":\r\n", "\r\n", "            if model_parameters.is_retain_best_model == True:\r\n", "                checkpoint_filename = os.path.join(model_directory, \"checkpoint_\" + basket.basket_id + \".hdf5\")\r\n", "                checkpoint = keras.callbacks.ModelCheckpoint(checkpoint_filename, monitor='val_sparse_categorical_accuracy', verbose=1, save_best_only=True, mode=\"max\")\r\n", "                callbacks.append(checkpoint)\r\n", "\r\n", "            if model_parameters.is_early_stopping == True:\r\n", "                early_stopping_callback = keras.callbacks.EarlyStopping(monitor=\"sparse_categorical_accuracy\", restore_best_weights=True, patience=5, verbose=1)\r\n", "                callbacks.append(early_stopping_callback)\r\n", "\r\n", "        elif model_parameters.classify_predict == \"predict\":\r\n", "\r\n", "            if model_parameters.is_retain_best_model == True:\r\n", "                checkpoint_filename = os.path.join(model_directory, \"checkpoint_\" + basket.basket_id + \".hdf5\")\r\n", "                checkpoint = keras.callbacks.ModelCheckpoint(checkpoint_filename, monitor='val_mse', verbose=1, save_best_only=True, mode=\"min\")\r\n", "                callbacks.append(checkpoint)\r\n", "\r\n", "            if model_parameters.is_early_stopping == True:\r\n", "                early_stopping_callback = keras.callbacks.EarlyStopping(monitor=\"mse\", restore_best_weights=True, patience=5, verbose=1)\r\n", "                callbacks.append(early_stopping_callback)\r\n", "\r\n", "        else:\r\n", "            raise ValueError(\"classify_predict has to either be 'classify' or 'predict'\")\r\n", "\r\n", "\r\n", "        if model_parameters.is_store_tensorboard_data == True:\r\n", "            log_dir = os.path.join(model_directory, \"tensorboard\", basket.basket_id)\r\n", "            tensorboard_callback = tf.keras.callbacks.TensorBoard(log_dir=log_dir, histogram_freq=1)\r\n", "            callbacks.append(tensorboard_callback)\r\n", "\r\n", "        if model_parameters.is_plot_realtime_tensorboard == True:\r\n", "            callbacks.append(mattlibrary.Tensorflow_Realtime_Plotting())\r\n", "\r\n", "        #train the model\r\n", "        history = model.fit(train_ds, epochs=model_parameters.epochs, validation_data=test_ds, callbacks=callbacks)\r\n", "\r\n", "        #plot training and validation loss\r\n", "        if model_parameters.classify_predict == \"classify\":\r\n", "\r\n", "            accuracy = history.history['sparse_categorical_accuracy']\r\n", "            val_accuracy = history.history['val_sparse_categorical_accuracy']\r\n", "            epochs = range(1, len(accuracy) + 1)\r\n", "            plt.plot(epochs, accuracy, 'g', label='Training Accuracy')\r\n", "            plt.plot(epochs, val_accuracy, 'b', label='Validation Accuracy')\r\n", "            plt.title('Training and validation loss')\r\n", "            plt.legend()\r\n", "            plt.show()\r\n", "\r\n", "        elif model_parameters.classify_predict == \"predict\":\r\n", "\r\n", "            accuracy = history.history['mse']\r\n", "            val_accuracy = history.history['val_mse']\r\n", "            epochs = range(1, len(accuracy) + 1)\r\n", "            plt.plot(epochs, accuracy, 'g', label='Training MSE')\r\n", "            plt.plot(epochs, val_accuracy, 'b', label='Validation MSE')\r\n", "            plt.title('Training and validation loss')\r\n", "            plt.legend()\r\n", "            plt.show()\r\n", "\r\n", "        else:\r\n", "            raise ValueError(\"classify_predict has to either be 'classify' or 'predict'\")\r\n", "\r\n", "        #endregion\r\n", "\r\n", "    "], "outputs": [], "metadata": {}}, {"cell_type": "markdown", "source": ["## Predict"], "metadata": {}}, {"cell_type": "code", "execution_count": 3, "source": ["def predict():\r\n", "\r\n", "    #obtain model parameters\r\n", "    model_parameter_path_filename = os.path.join(model_directory, \"model_parameters.json\")\r\n", "    with open(model_parameter_path_filename, \"r\") as json_file:\r\n", "        json_data = json_file.read()\r\n", "        model_parameters = ModelParameters.from_json(json_data)\r\n", "\r\n", "    #iterate over each model\r\n", "    result_df = None\r\n", "    for basket in model_parameters.fx_baskets:\r\n", "\r\n", "        print(f\"Inferencing for model: {basket.basket_id}\")\r\n", "\r\n", "        #obtain dataset\r\n", "        raw_dataframe, inference_dataset = create_dataset_training(basket, model_directory, data_pathfilename, \"inference\", model_parameters, static_data, dt_from, dt_to)\r\n", "\r\n", "        #sub model directory\r\n", "        basket_model_directory = os.path.join(model_directory, f\"{basket.basket_id}_Model\")\r\n", "\r\n", "        #load the model\r\n", "        print(\"Before loading model\")\r\n", "        model_path_filename = os.path.join(basket_model_directory, \"saved_model.h5\")\r\n", "        model = keras.models.load_model(model_path_filename)\r\n", "        print(\"After loading model\")\r\n", "\r\n", "        #load checkpoint\r\n", "        checkpoint_filename = os.path.join(basket_model_directory, \"checkpoint_\" + basket.basket_id + \".hdf5\")\r\n", "        model.load_weights(checkpoint_filename)\r\n", "        print(\"Loaded Model Weights...\")\r\n", "\r\n", "        #create dataframe\r\n", "        df = pd.DataFrame(raw_dataframe[basket.basket_currencies][model_parameters.sequence_length:], index = raw_dataframe.index[model_parameters.sequence_length:])\r\n", "        df[basket.basket_id + \"_Return\"] = raw_dataframe[basket.basket_id][model_parameters.sequence_length:]  \r\n", "\r\n", "        #add prediction of basket return\r\n", "        if model_parameters.classify_predict == \"classify\":\r\n", "            df[basket.basket_id + \"_Pred\"] = list(np.argmax(model.predict(inference_dataset), axis=1))\r\n", "        elif model_parameters.classify_predict == \"predict\":\r\n", "            df[basket.basket_id + \"_Pred\"] = model.predict(inference_dataset)\r\n", "        else:\r\n", "            raise ValueError(\"classify_predict has to either be 'classify' or 'predict'\") \r\n", "\r\n", "        #join dataframes\r\n", "        if result_df is None:\r\n", "            result_df = df\r\n", "        else:\r\n", "            result_df = result_df.combine_first(df)\r\n", "\r\n", "    #store result dataframe on disk\r\n", "    result_filename = os.path.join(model_directory, \"results.csv\")\r\n", "    result_df.to_csv(result_filename, index=True)  \r\n"], "outputs": [], "metadata": {}}, {"cell_type": "markdown", "source": ["## Result Analysis Functions"], "metadata": {}}, {"cell_type": "code", "execution_count": 4, "source": ["def analyze_model(directory : str, static_data : mattlibrary.StaticData, datetime_from:str, datetime_to:str, min_sharpe_ratio:float=0.0, sharpe_lookback_period:int=0, min_zscore_predicted_return:float=0.0):\r\n", "\r\n", "    #print model parameters\r\n", "    model_parameter_path_filename = os.path.join(directory, \"model_parameters.json\")\r\n", "    with open(model_parameter_path_filename, \"r\") as json_file:\r\n", "        json_data = json_file.read()\r\n", "        model_parameters = mattlibrary.ModelParameters.from_json(json_data)\r\n", "        #print(model_parameters)\r\n", "        \r\n", "    print(directory)\r\n", "    print()\r\n", "\r\n", "    #load data\r\n", "    result_path_filename = os.path.join(directory, \"results.csv\")\r\n", "    df = pd.read_csv(result_path_filename, header=0, index_col=0, parse_dates=[\"Date\"])\r\n", "    currencies = [\"USD\", \"EUR\", \"GBP\", \"AUD\", \"NZD\", \"CAD\", \"CHF\", \"JPY\"]\r\n", "    fx_pairs = [col for col in df.columns if col in static_data.symbol_dictionary and static_data.symbol_dictionary[col].asset_class == \"FX\"]\r\n", "\r\n", "    #region generate trades\r\n", "    trades = list()\r\n", "    for timestamp, row in df.iterrows():\r\n", "\r\n", "        #iterate over each fx pair and check whether fx pair is being traded or not\r\n", "        for fx_pair in fx_pairs:\r\n", "\r\n", "            #ignore day for this currency if return 0 or NaN\r\n", "            if row[fx_pair] == 0 or row[fx_pair] is np.NaN:\r\n", "                continue\r\n", "\r\n", "            basket1 = fx_pair[0:3] + \"_Pred\"\r\n", "            basket2 = fx_pair[3:6] + \"_Pred\"\r\n", "           \r\n", "            if model_parameters.classify_predict == \"classify\":\r\n", "                if row[basket1] == 1 and row[basket2] == 0: \r\n", "                    trades.append({\"TimeStamp\" : timestamp, \"Symbol\" : fx_pair, \"LongShort\" : \"LONG\", \"ProfitLoss\" : row[fx_pair]})\r\n", "                elif row[basket1] == 0 and row[basket2] == 1:\r\n", "                    trades.append({\"TimeStamp\" : timestamp, \"Symbol\" : fx_pair, \"LongShort\" : \"SHORT\", \"ProfitLoss\" : -row[fx_pair]})\r\n", "            elif model_parameters.classify_predict == \"predict\":\r\n", "                if row[basket1] > 0 and row[basket2] < 0: \r\n", "                    trades.append({\"TimeStamp\" : timestamp, \"Symbol\" : fx_pair, \"LongShort\" : \"LONG\", \"ProfitLoss\" : row[fx_pair], \"Ccy1_Predict\" : row[basket1], \"Ccy2_Predict\" : row[basket2]})\r\n", "                elif row[basket1] < 0 and row[basket2] > 0:\r\n", "                    trades.append({\"TimeStamp\" : timestamp, \"Symbol\" : fx_pair, \"LongShort\" : \"SHORT\", \"ProfitLoss\" : -row[fx_pair], \"Ccy1_Predict\" : row[basket1], \"Ccy2_Predict\" : row[basket2]})\r\n", "            else:\r\n", "                raise ValueError(\"classify_predict has to either be 'classify' or 'predict'\")    \r\n", "\r\n", "    #convert list into dataframe\r\n", "    trades_df = pd.DataFrame.from_records(trades)\r\n", "    trades_df = trades_df.set_index(\"TimeStamp\")\r\n", "\r\n", "    #endregion\r\n", "\r\n", "    #region Model Accuracies\r\n", "    print(\"\\nBasket Accuracies\")\r\n", "\r\n", "    #filter by dates\r\n", "    df_filtered = mattlibrary.filter_dataframe_by_dates(df, datetime_from, datetime_to)\r\n", "    \r\n", "    for currency in currencies:\r\n", "\r\n", "        prediction_id = currency + \"_Pred\"\r\n", "        return_id = currency + \"_Return\"\r\n", "\r\n", "        if model_parameters.classify_predict == \"classify\":\r\n", "            prediction_accuracy = df_filtered[((df_filtered[prediction_id] == 0) & (df_filtered[return_id] < 0 )) | ((df_filtered[prediction_id] == 1) & (df_filtered[return_id] > 0))].count()[prediction_id] / len(df_filtered)    \r\n", "        elif model_parameters.classify_predict == \"predict\":\r\n", "            prediction_accuracy = df_filtered[((df_filtered[prediction_id] < 0) & (df_filtered[return_id] < 0 )) | ((df_filtered[prediction_id] > 0) & (df_filtered[return_id] > 0))].count()[prediction_id] / len(df_filtered)\r\n", "        else:\r\n", "            raise ValueError(\"classify_predict has to either be 'classify' or 'predict'\")     \r\n", "        \r\n", "        print(f\"{currency} Accuracy: {prediction_accuracy:.2%}\")\r\n", "    \r\n", "    #endregion\r\n", "    \r\n", "    #region Single Fx Pair Analysis\r\n", "    df_filtered = mattlibrary.filter_dataframe_by_dates(trades_df, datetime_from, datetime_to)\r\n", "\r\n", "    duration = (df_filtered.index.max() - df_filtered.index.min()).days\r\n", "    profit_loss_df = df_filtered.groupby(by=[\"Symbol\"])[\"ProfitLoss\"]\r\n", "    fx_pair_df = pd.DataFrame()\r\n", "    \r\n", "    #calculate analytics\r\n", "    fx_pair_df[\"Avg Profit\"] = profit_loss_df.mean()\r\n", "    fx_pair_df[\"Std Profit\"] = profit_loss_df.std()\r\n", "    fx_pair_df[\"Total Profit\"] = profit_loss_df.sum()\r\n", "    fx_pair_df[\"Annualized Profit\"] = fx_pair_df[\"Total Profit\"] * (365 / duration)\r\n", "    fx_pair_df[\"Minimum Profit\"] = profit_loss_df.min()\r\n", "    fx_pair_df[\"Maximum Profit\"] = profit_loss_df.max()\r\n", "    fx_pair_df[\"Sharpe Ratio\"] = fx_pair_df[\"Avg Profit\"] / fx_pair_df[\"Std Profit\"] * np.sqrt(252)\r\n", "    fx_pair_df[\"Total Trading Days\"] = profit_loss_df.count()\r\n", "    fx_pair_df[\"Days +/- Ratio\"] = df_filtered[df_filtered[\"ProfitLoss\"] > 0].groupby(by=[\"Symbol\"])[\"ProfitLoss\"].count() / df_filtered[df_filtered[\"ProfitLoss\"] < 0].groupby(by=[\"Symbol\"])[\"ProfitLoss\"].count()\r\n", "    \r\n", "    output = fx_pair_df.to_string(formatters={\r\n", "        \"Avg Profit\": \"{:,.2%}\".format,\r\n", "        \"Std Profit\": \"{:,.2%}\".format,\r\n", "        \"Total Profit\": \"{:,.2%}\".format,\r\n", "        \"Annualized Profit\": \"{:,.2%}\".format,\r\n", "        \"Minimum Profit\": \"{:,.2%}\".format,\r\n", "        \"Maximum Profit\": \"{:,.2%}\".format,\r\n", "        \"Sharpe Ratio\": \"{:.2f}\".format,\r\n", "        \"Days +/- Ratio\": \"{:.2f}\".format,\r\n", "    })\r\n", "       \r\n", "    print(\"\\nSingle Fx Pair Analysis\")\r\n", "    print(output)   \r\n", "\r\n", "    #endregion\r\n", "\r\n", "    #region portfolio analysis\r\n", "\r\n", "    print(\"\\nPortfolio Analysis\")\r\n", "\r\n", "    if sharpe_lookback_period != 0:\r\n", "        trades_df[\"avg\"] = trades_df.groupby(\"Symbol\")[\"ProfitLoss\"].transform(lambda x: x.rolling(sharpe_lookback_period, min_periods=sharpe_lookback_period).mean().shift(1))\r\n", "        trades_df[\"stdev\"] = trades_df.groupby(\"Symbol\")[\"ProfitLoss\"].transform(lambda x: x.rolling(sharpe_lookback_period, min_periods=sharpe_lookback_period).std().shift(1))\r\n", "        trades_df[\"sharpe\"] = (trades_df[\"avg\"] / trades_df[\"stdev\"] * np.sqrt(252)).shift(1)\r\n", "\r\n", "        #filter trades by minimum sharpe ratio\r\n", "        trades_df = trades_df[(trades_df[\"sharpe\"] >= min_sharpe_ratio)]\r\n", "\r\n", "    if min_zscore_predicted_return > 0.0:\r\n", "        \r\n", "        avg_predicted_returns_ccy1 = trades_df[\"Ccy1_Predict\"].mean()\r\n", "        avg_predicted_returns_ccy2 = trades_df[\"Ccy2_Predict\"].mean()\r\n", "        stdev_predicted_returns_ccy1 = trades_df[\"Ccy1_Predict\"].std()\r\n", "        stdev_predicted_returns_ccy2 = trades_df[\"Ccy2_Predict\"].std()\r\n", "        trades_df[\"zscore_ccy1\"] = (trades_df[\"Ccy1_Predict\"] - avg_predicted_returns_ccy1) / stdev_predicted_returns_ccy1\r\n", "        trades_df[\"zscore_ccy2\"] = (trades_df[\"Ccy2_Predict\"] - avg_predicted_returns_ccy2) / stdev_predicted_returns_ccy2\r\n", "\r\n", "        #filter trades by minimum standard deviation\r\n", "        trades_df = trades_df[(trades_df[\"zscore_ccy1\"] >= min_zscore_predicted_return) & (trades_df[\"zscore_ccy2\"] <= -min_zscore_predicted_return)]\r\n", "\r\n", "    #filter trades by datetime_from/datetime_to\r\n", "    trades_df = mattlibrary.filter_dataframe_by_dates(trades_df, datetime_from, datetime_to)\r\n", "   \r\n", "    duration = (trades_df.index.max() - trades_df.index.min()).days\r\n", "    grouped_by_days = trades_df.groupby(by=trades_df.index)[\"ProfitLoss\"]\r\n", "\r\n", "    print(f\"Total Calendar Days: {duration}\")\r\n", "    print(f\"Total Trading Days Count: {len(grouped_by_days)}\")\r\n", "    print(f\"Average Number Trades Per Day: {np.mean(grouped_by_days.count()):.2f}\")\r\n", "    print(f\"Minimum Number Trades Per Day: {min(grouped_by_days.count())}\")\r\n", "    print(f\"Maximum Number Trades Per Day: {max(grouped_by_days.count())}\")\r\n", "    print(f\"Total Return: {grouped_by_days.mean().sum():,.2%}\")\r\n", "    print(f\"Annualized Return: {(grouped_by_days.mean().sum() * (365 / duration)):,.2%}\")\r\n", "    print(f\"<PERSON> Ratio: {grouped_by_days.mean().mean() / grouped_by_days.mean().std() * np.sqrt(252):,.3f}\")\r\n", "    print(f\"Average Daily PnL: {grouped_by_days.mean().mean():,.4%}\")\r\n", "    print(f\"Standard Deviation Daily PnL: {grouped_by_days.mean().std():,.4%}\")\r\n", "    print(f\"Largest Gain: {grouped_by_days.mean().max():,.4%}\")\r\n", "    print(f\"Largest Loss: {grouped_by_days.mean().min():,.4%}\")\r\n", "\r\n", "    #output chart\r\n", "    x = 0\r\n", "    chart_data = grouped_by_days.mean()\r\n", "    timestamps = chart_data.keys()\r\n", "    cumulative_returns = [x := x + t for t in chart_data]\r\n", "\r\n", "    fig, ax = plt.subplots(figsize=(11.7, 8.27))\r\n", "    sns.lineplot(x=timestamps, y=cumulative_returns, ax=ax)\r\n", "    sns.displot(chart_data,  binwidth=0.001, height=8.27, aspect=11.7/8.27)\r\n", "\r\n", "    #endregion\r\n", "\r\n", "\r\n", "\r\n", "\r\n"], "outputs": [], "metadata": {}}, {"cell_type": "markdown", "source": ["## Code Execution"], "metadata": {}}, {"cell_type": "code", "execution_count": null, "source": ["#run prediction\r\n", "def run_prediction():\r\n", "\r\n", "    #make predictions\r\n", "    dt_from = \"2019-01-01\"\r\n", "    dt_to = None\r\n", "    model_directory = os.path.join(os.getcwd(), \"Current_Model\")\r\n", "    #model_directory = os.path.join(os.getcwd(), \"Trained_Model_Original\")\r\n", "    rawdata_path_filename = os.path.join(os.getcwd(), \"ib_data.csv\")\r\n", "    mattlibrary.predict_data(model_directory, rawdata_path_filename, static_data, dt_from, dt_to)\r\n", "\r\n", "#analyze results\r\n", "def run_result_analysis():\r\n", "\r\n", "    min_sharpe_ratio = 0.5\r\n", "    sharpe_ratio_days_lookback = 0\r\n", "    min_zscore_predicted_return = 0.01\r\n", "    datetime_from = \"2019-01-01\"\r\n", "    datetime_to = \"2021-07-11\"\r\n", "    # model_directory = os.path.join(os.getcwd(), \"Trained_Model_Original\")\r\n", "    model_directory = os.path.join(os.getcwd(), \"Current_Model\")\r\n", "    \r\n", "    analyze_model(model_directory, static_data, datetime_from, datetime_to, min_sharpe_ratio=min_sharpe_ratio, sharpe_lookback_period=sharpe_ratio_days_lookback, min_zscore_predicted_return=min_zscore_predicted_return) \r\n", "        \r\n", "#run code\r\n", "train_model()\r\n", "#run_prediction()\r\n", "#run_result_analysis()\r\n"], "outputs": [], "metadata": {}}], "metadata": {"interpreter": {"hash": "1baa965d5efe3ac65b79dfc60c0d706280b1da80fedb7760faf2759126c4f253"}, "kernelspec": {"display_name": "Python 3.8.7 64-bit", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.7"}, "metadata": {"interpreter": {"hash": "1baa965d5efe3ac65b79dfc60c0d706280b1da80fedb7760faf2759126c4f253"}}, "orig_nbformat": 2}, "nbformat": 4, "nbformat_minor": 2}