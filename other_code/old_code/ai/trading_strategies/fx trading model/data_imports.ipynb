{"cells": [{"cell_type": "code", "execution_count": null, "source": ["import os\r\n", "import sys\r\n", "import pendulum\r\n", "import pandas as pd\r\n", "import numpy as np\r\n", "import time\r\n", "import datetime\r\n", "\r\n", "\r\n", "sys.path.append(\"..\")\r\n", "from mattlibrary import matt_finance\r\n", "from mattlibrary import matt_ai\r\n", "from mattlibrary import matt_data_imports\r\n"], "outputs": [], "metadata": {}}, {"cell_type": "markdown", "source": ["## Run Code"], "metadata": {}}, {"cell_type": "code", "execution_count": null, "source": ["static_data = matt_finance.StaticData()\r\n", "ib = matt_data_imports.InteractiveBrokers(\"127.0.0.1\", 7499, 1000)"], "outputs": [], "metadata": {}}, {"cell_type": "code", "execution_count": null, "source": ["bar_size = \"1 day\"\r\n", "duration_string = \"10 D\"\r\n", "end_datetime = pendulum.now(\"America/New_York\")\r\n", "path_file_name = os.path.join(os.getcwd(), \"ib_data.csv\")\r\n", "unprocessed_requests = 0\r\n", "symbols_requested = set()\r\n", "for basket_id, basket in static_data.fx_baskets.items():\r\n", "    for feature in basket.features:\r\n", "        if feature in static_data.symbol_dictionary:\r\n", "            symbols_requested.add(feature)\r\n", "\r\n", "\r\n", "def on_received_historical_data(symbol:matt_finance.Symbol, new_df:pd.DataFrame):\r\n", "        \r\n", "    #filter out unneccessary data\r\n", "    new_df = pd.DataFrame({symbol.symbol : new_df[\"Close\"]}, index=new_df.index)        \r\n", "\r\n", "    #do nothing if there is no actual data\r\n", "    if len(new_df) > 0:\r\n", "        \r\n", "        #load existing dataframe if it exists\r\n", "        if os.path.exists(path_file_name) == True:\r\n", "            df = pd.read_csv(path_file_name, header=0, index_col=0, parse_dates=[\"Date\"], dtype=np.float64)\r\n", "\r\n", "            #merge dataframes\r\n", "            df = df.combine_first(new_df)\r\n", "            \r\n", "        else:\r\n", "            df = new_df\r\n", "            \r\n", "        #save dataframe\r\n", "        df.to_csv(path_file_name, index=True)\r\n", "\r\n", "    #update unprocessed_requests counter\r\n", "    global unprocessed_requests\r\n", "    unprocessed_requests = unprocessed_requests - 1\r\n", "\r\n", "    #print update\r\n", "    print(f\"Received Data for Symbol: {symbol.symbol} -From: {new_df.index[0]} - To: {new_df.index[-1]} - Remaining Requests: {unprocessed_requests}\")\r\n", "\r\n", "#iterate over all symbols and request data\r\n", "for symbol in symbols_requested:\r\n", "    unprocessed_requests = unprocessed_requests + 1\r\n", "    ib.request_historical_data(static_data.symbol_dictionary[symbol], bar_size, duration_string, end_datetime, on_received_historical_data)\r\n", "\r\n", "#wait until done\r\n", "while unprocessed_requests > 0:\r\n", "    time.sleep(1)\r\n", "\r\n", "#clean up\r\n", "if os.path.exists(path_file_name) == True:\r\n", "    df = pd.read_csv(path_file_name, header=0, index_col=0, parse_dates=[\"Date\"], dtype=np.float64) \r\n", "\r\n", "    #fill forward\r\n", "    df = df.ffill()\r\n", "\r\n", "    #remove empty columns\r\n", "    df = df.dropna()\r\n", "\r\n", "    #save dataframe\r\n", "    df.to_csv(path_file_name, index=True)\r\n", "\r\n", "\r\n", "print(f\"Received All Data from {df.index[0]} until {df.index[-1]}\")"], "outputs": [], "metadata": {}}, {"cell_type": "code", "execution_count": null, "source": ["#clean up\r\n", "data_pathfilename = os.path.join(os.getcwd(), \"ib_data.csv\")\r\n", "filename = os.path.join(os.getcwd(), \"ib_cleansed_data.csv\")\r\n", "\r\n", "df = pd.read_csv(data_pathfilename, header=0, index_col=0, parse_dates=[\"Date\"], dtype=np.float64)    \r\n", "\r\n", "# df[\"hour\"] = df.index.hour\r\n", "# df[df[\"hour\"] == 20]\r\n", "\r\n", "df = df[df.index.hour == 20]\r\n", "df.to_csv(filename, index=True)"], "outputs": [], "metadata": {}}], "metadata": {"interpreter": {"hash": "1baa965d5efe3ac65b79dfc60c0d706280b1da80fedb7760faf2759126c4f253"}, "kernelspec": {"display_name": "Python 3.8.7 64-bit", "name": "python3"}, "language_info": {"name": "python", "version": ""}, "orig_nbformat": 2}, "nbformat": 4, "nbformat_minor": 2}