{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "import polars as pl\n", "import talib\n", "from copy import copy, deepcopy\n", "import plotly.express as px\n", "\n", "# pd.options.plotting.backend = \"plotly\"\n", "# output_notebook()\n", "# curdoc().theme = \"dark_minimal\"\n", "\n", "#used to locate custom package/modules\n", "sys.path.insert(0, '/home/<USER>/development/python_development')\n", "import mattlibrary as matt\n", "from mattlibrary.finance.basket import Basket\n", "from mattlibrary.finance.oms import Oms\n", "from mattlibrary.finance.position import Position\n", "from mattlibrary.finance.performance_statistics import PerformanceStatistics\n", "import mattlibrary.visualizations.charting_3D as charting3D\n", "import mattlibrary.optimization.optimizer as optimizer \n", "\n", "%reload_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#Parameters\n", "sma_lookback = 14\n", "atr_lookback = 14\n", "initial_funding = 100_000\n", "fx_symbol_order_size = 100_000"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#Raw Data Reader\n", "df = pl.read_parquet(\"/mnt/d/dukascopy_daily_fx_data.parquet\")\n", "# df = pl.read_parquet('/mnt/d/raw_data_daily.parquet')\n", "\n", "#filter out Closing prices and Datetime column\n", "# df = df.select(pl.col(\"^.*Datetime|^.*_Close$\"))\n", "\n", "#rename columns headers (remove '_Close')\n", "# df.columns = list(map(lambda x:x.replace(\"_Close\", \"\"), df.columns))\n", "\n", "#build baskets\n", "baskets = []\n", "\n", "# baskets.append(Basket(\"EUR\", [\"EURUSD\", \"EURGBP\", \"EURAUD\", \"EURNZD\", \"EURCAD\", \"EURJPY\"]))\n", "# baskets.append(Basket(\"GBP\", [\"GBPUSD\", \"EURGBP\", \"GBPAUD\", \"GBPNZD\", \"GBPCAD\", \"GBPJPY\"]))\n", "# baskets.append(Basket(\"AUD\", [\"AUDUSD\", \"EURAUD\", \"GBPAUD\", \"AUDNZD\", \"AUDCAD\", \"AUDJPY\"]))\n", "# baskets.append(Basket(\"NZD\", [\"NZDUSD\", \"EURNZD\", \"GBPNZD\", \"AUDNZD\", \"NZDCAD\", \"NZDJPY\"]))\n", "# baskets.append(Basket(\"USD\", [\"EURUSD\", \"GBPUSD\", \"AUDUSD\", \"NZDUSD\", \"USDCAD\", \"USDJPY\"]))\n", "# baskets.append(Basket(\"CAD\", [\"USDCAD\", \"EURCAD\", \"GBPCAD\", \"AUDCAD\", \"NZDCAD\", \"CADJPY\"]))\n", "# baskets.append(Basket(\"JPY\", [\"USDJPY\", \"EURJPY\", \"GBPJPY\", \"AUDJPY\", \"NZDJPY\", \"CADJPY\"]))\n", "\n", "baskets.append(Basket(\"EUR\", [\"EURUSD\", \"EURGBP\", \"EURAUD\", \"EURNZD\", \"EURCAD\", \"EURCHF\", \"EURJPY\"]))\n", "baskets.append(Basket(\"GBP\", [\"GBPUSD\", \"EURGBP\", \"GBPAUD\", \"GBPNZD\", \"GBPCAD\", \"GBPCHF\", \"GBPJPY\"]))\n", "baskets.append(Basket(\"AUD\", [\"AUDUSD\", \"EURAUD\", \"GBPAUD\", \"AUDNZD\", \"AUDCAD\", \"AUDCHF\", \"AUDJPY\"]))\n", "baskets.append(Basket(\"NZD\", [\"NZDUSD\", \"EURNZD\", \"GBPNZD\", \"AUDNZD\", \"NZDCAD\", \"NZDCHF\", \"NZDJPY\"]))\n", "baskets.append(Basket(\"USD\", [\"EURUSD\", \"GBPUSD\", \"AUDUSD\", \"NZDUSD\", \"USDCAD\", \"USDCHF\", \"USDJPY\"]))\n", "baskets.append(Basket(\"CAD\", [\"USDCAD\", \"EURCAD\", \"GBPCAD\", \"AUDCAD\", \"NZDCAD\", \"CADCHF\", \"CADJPY\"]))\n", "baskets.append(Basket(\"CHF\", [\"USDCHF\", \"EURCHF\", \"GBPCHF\", \"AUDCHF\", \"NZDCHF\", \"CADCHF\", \"CHFJPY\"]))\n", "baskets.append(Basket(\"JPY\", [\"USDJPY\", \"EURJPY\", \"GBPJPY\", \"AUDJPY\", \"NZDJPY\", \"CADJPY\", \"CHFJPY\"]))\n", "\n", "#create baskets\n", "for basket in baskets:\n", "    series = basket.convert_series(basket.constituent_symbols[0], df[f\"{basket.constituent_symbols[0]}_Close\"])\n", "    for const_symbol in basket.constituent_symbols[1:]:\n", "        series += basket.convert_series(const_symbol, df[f\"{const_symbol}_Close\"])\n", "    series /= len(basket.constituent_symbols)\n", "    \n", "    #add basket\n", "    df = df.with_columns((series).alias(basket.basket_symbol))\n", "    \n", "    #add SMA of basket\n", "    df = df.with_columns((talib.SMA(df[basket.basket_symbol], timeperiod=sma_lookback).alias(f\"{basket.basket_symbol}_SMA{sma_lookback}\")))\n", "    \n", "#create basket and fx symbol collections\n", "basket_symbols = [basket.basket_symbol for basket in baskets]\n", "fx_symbols = list(set([item for sublist in baskets for item in sublist.constituent_symbols])) #done to obtain unique symbols\n", "\n", "#add some TA indicators for fx symbols\n", "for fx_symbol in fx_symbols:\n", "    #add ATR\n", "    df = df.with_columns((talib.ATR(df[f\"{fx_symbol}_High\"], df[f\"{fx_symbol}_Low\"], df[f\"{fx_symbol}_Close\"], timeperiod=atr_lookback).alias(f\"{fx_symbol}_ATR{atr_lookback}\")))\n", "    #add SMA\n", "    df = df.with_columns((talib.SMA(df[f\"{fx_symbol}_Close\"], timeperiod=sma_lookback).alias(f\"{fx_symbol}_SMA{sma_lookback}\")))\n", "    \n", "#remove rows with #na values\n", "source_data = df.filter(pl.all(pl.col(pl.Float32, pl.Float64).is_not_nan())).drop_nulls()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#Backtest Logic\n", "\n", "class BasketMetric():\n", "    \n", "    def __init__(self, symbol):\n", "        self.symbol = symbol\n", "        self.direction = 0\n", "        self.price_current = 0.0\n", "        self.price_previous = 0.0\n", "        self.sma_current = 0.0\n", "        self.sma_previous = 0.0\n", "        \n", "    def reset(self):\n", "        self.direction = 0.0\n", "        self.price_current = 0.0\n", "        self.price_previous = 0.0\n", "        self.sma_current = 0.0\n", "        self.sma_previous = 0.0\n", "\n", "class AssetMetric():\n", "    \n", "    def __init__(self, symbol):\n", "        self.symbol = symbol\n", "        self.direction = 0\n", "        self.price_current = 0.0\n", "        self.price_previous = 0.0\n", "        self.sma_current = 0.0\n", "        self.sma_previous = 0.0\n", "        \n", "    def reset(self):\n", "        self.direction = 0.0\n", "        self.price_current = 0.0\n", "        self.price_previous = 0.0\n", "        self.sma_current = 0.0\n", "        self.sma_previous = 0.0\n", "\n", "\n", "def run_backtest(source_data:pl.DataFrame, optimizer_metrics : dict, is_save_results : bool):\n", "   \n", "    #create order management system\n", "    oms = Oms(\"CAD\")  \n", "        \n", "    #create basket metrics\n", "    basket_metrics = dict()\n", "    for basket_symbol in basket_symbols:\n", "        basket_metrics[basket_symbol] = BasketMetric(basket_symbol)\n", "\n", "    #create single asset metrics\n", "    fx_metrics = dict()\n", "    for fx_symbol in fx_symbols:\n", "        fx_metrics[fx_symbol] = AssetMetric(fx_symbol)\n", "\n", "    #iterate over each row\n", "    is_first_row = True\n", "    for row in source_data.iter_rows(named=True):\n", "        \n", "        timestamp = row[\"Datetime\"]\n", "            \n", "        #iterate over each basket\n", "        for basket_symbol in basket_symbols:\n", "        \n", "            basket_metric = basket_metrics[basket_symbol]\n", "            \n", "            #early exit if processing first row\n", "            if is_first_row == True:\n", "                basket_metric.price_previous = row[basket_symbol]\n", "                basket_metric.sma_previous = row[f\"{basket_symbol}_SMA{sma_lookback}\"]\n", "                continue\n", "\n", "            #update metrics \n", "            basket_metric.price_current = row[basket_symbol]    \n", "            basket_metric.sma_current = row[f\"{basket_symbol}_SMA{sma_lookback}\"]\n", "            \n", "            #determine basket direction\n", "            if (basket_metric.price_current > basket_metric.sma_current) and (basket_metric.price_previous < basket_metric.sma_previous):\n", "                basket_metric.direction = 1\n", "            elif (basket_metric.price_current < basket_metric.sma_current) and (basket_metric.price_previous > basket_metric.sma_previous):\n", "                basket_metric.direction = -1\n", "            else:\n", "                basket_metric.direction = 0\n", "                    \n", "            # if (basket_metric.price_current > basket_metric.sma_current) and (basket_metric.sma_current > basket_metric.sma_previous):\n", "            #     basket_metric.direction = 1\n", "            # elif (basket_metric.price_current < basket_metric.sma_current) and (basket_metric.sma_current < basket_metric.sma_previous):\n", "            #     basket_metric.direction = -1\n", "            # else:\n", "            #     basket_metric.direction = 0\n", "                    \n", "            # if (basket_metric.price_current > basket_metric.sma_current) and (basket_metric.price_previous < basket_metric.sma_previous) and (basket_metric.sma_current > basket_metric.sma_previous):\n", "            #     basket_metric.direction = 1\n", "            # elif (basket_metric.price_current < basket_metric.sma_current) and (basket_metric.price_previous > basket_metric.sma_previous) and (basket_metric.sma_current < basket_metric.sma_previous):\n", "            #     basket_metric.direction = -1\n", "            # else:\n", "            #     basket_metric.direction = 0\n", "        \n", "            #update previous values\n", "            basket_metric.price_previous = row[basket_symbol]\n", "            basket_metric.sma_previous = row[f\"{basket_symbol}_SMA{sma_lookback}\"]\n", "        \n", "        #iterate over each fx symbol and submit orders\n", "        for fx_symbol in fx_symbols:\n", "            \n", "            #exclude any fx pairs with 'CHF'\n", "            # if fx_symbol != \"EURCAD\":\n", "            #     continue\n", "            \n", "            fx_metric = fx_metrics[fx_symbol]\n", "            \n", "            if is_first_row:\n", "                #do this for the first row\n", "                oms.current_positions[fx_symbol] = Position(fx_symbol)\n", "                fx_metric.price_previous = row[f\"{fx_symbol}_Close\"]\n", "                fx_metric.sma_previous = row[f\"{fx_symbol}_SMA{sma_lookback}\"]\n", "                continue\n", "            else:\n", "                #do this for all other rows\n", "                fx_metric.price_current = row[f\"{fx_symbol}_Close\"]    \n", "                fx_metric.sma_current = row[f\"{fx_symbol}_SMA{sma_lookback}\"] \n", "                \n", "            #obtain optimization metrics\n", "            if \"target_level_scaler\" in optimizer_metrics:\n", "                target_level_scaler = optimizer_metrics[\"target_level_scaler\"]\n", "            else:\n", "                target_level_scaler = 0.0\n", "                \n", "            if \"stoploss_level_scaler\" in optimizer_metrics:\n", "                stoploss_level_scaler = optimizer_metrics[\"stoploss_level_scaler\"]\n", "            else:\n", "                stoploss_level_scaler = 0.0\n", "            \n", "            #check whether target price or stop loss is reached    \n", "            target_level_range = target_level_scaler * row[f\"{fx_symbol}_ATR{atr_lookback}\"]\n", "            stoploss_level_range = stoploss_level_scaler * row[f\"{fx_symbol}_ATR{atr_lookback}\"]\n", "            position = oms.current_positions[fx_symbol]\n", "            previous_total_pnl_base = position.unrealized_pnl_base + position.realized_pnl_base\n", "            avg_price = position.average_price\n", "            current_high = row[f\"{fx_symbol}_High\"]\n", "            current_low = row[f\"{fx_symbol}_Low\"]\n", "            current_price = row[f\"{fx_symbol}_Close\"]\n", "                        \n", "            #exit signals (stop loss and target level)\n", "            if position.position_size > 0:\n", "                if target_level_range > 0.0 and current_price - avg_price > target_level_range:\n", "                    #take profit\n", "                    oms.performance_metrics.add_log(timestamp, f\"Exit Take Profit Order for symbol {fx_symbol}\")\n", "                    oms.submit_order(fx_symbol, timestamp, 0, current_price, True, row) \n", "                elif stoploss_level_range > 0.0 and current_price - avg_price < -stoploss_level_range:\n", "                    #stop loss\n", "                    oms.performance_metrics.add_log(timestamp, f\"Exit Stop Loss Order for symbol {fx_symbol}\")\n", "                    oms.submit_order(fx_symbol, timestamp, 0, current_price, True, row) \n", "            elif position.position_size < 0:\n", "                if target_level_range > 0.0 and current_price - avg_price < -target_level_range:\n", "                    #take profit\n", "                    oms.performance_metrics.add_log(timestamp, f\"Exit Take Profit Order for symbol {fx_symbol}\")\n", "                    oms.submit_order(fx_symbol, timestamp, 0, current_price, True, row)  \n", "                elif stoploss_level_range > 0.0 and current_price - avg_price > stoploss_level_range:\n", "                    #stop loss\n", "                    oms.performance_metrics.add_log(timestamp, f\"Exit Stop Loss Order for symbol {fx_symbol}\")\n", "                    oms.submit_order(fx_symbol, timestamp, 0, current_price, True, row) \n", "            \n", "            #entry signals\n", "            \n", "            #based on baskets\n", "            left_direction = basket_metrics[fx_symbol[0:3]].direction\n", "            right_direction = basket_metrics[fx_symbol[3:6]].direction\n", "            if left_direction == 1 and right_direction == -1:\n", "                #be long\n", "                oms.performance_metrics.add_log(timestamp, f\"Entry Order for symbol {fx_symbol}\")\n", "                oms.submit_order(fx_symbol, timestamp, fx_symbol_order_size, current_price, True, row) \n", "            elif left_direction == -1 and right_direction == 1:\n", "                #be short\n", "                oms.performance_metrics.add_log(timestamp, f\"Entry Order for symbol {fx_symbol}\")\n", "                oms.submit_order(fx_symbol, timestamp, -fx_symbol_order_size, current_price, True, row) \n", "            else:\n", "                #no change\n", "                None\n", "            \n", "            #based on the fx pairs themselves        \n", "            # if (fx_metric.price_current > fx_metric.sma_current) and (fx_metric.price_previous < fx_metric.sma_previous) and (fx_metric.sma_current > fx_metric.sma_previous):\n", "            #     #be long\n", "            #     oms.submit_order(fx_symbol, timestamp, fx_symbol_order_size, current_price, True, row) \n", "            # elif (fx_metric.price_current < fx_metric.sma_current) and (fx_metric.price_previous > fx_metric.sma_previous) and (fx_metric.sma_current < fx_metric.sma_previous):\n", "            #     #be short\n", "            #     oms.submit_order(fx_symbol, timestamp, -fx_symbol_order_size, current_price, True, row) \n", "            # else:\n", "            #     #no change\n", "            #     None\n", "            \n", "            #update unrealized pnl\n", "            oms.update_unrealized_pnl(fx_symbol, timestamp, current_price, row)\n", "            \n", "            #update daily total pnl \n", "            position.daily_pnl = (position.unrealized_pnl_base + position.realized_pnl_base) - previous_total_pnl_base\n", "            \n", "            #update previous values\n", "            fx_metric.price_previous = fx_metric.price_current\n", "            fx_metric.sma_previous = fx_metric.sma_current\n", "            \n", "        #handle first row\n", "        if is_first_row == True:\n", "            is_first_row = False\n", "            continue\n", "            \n", "        #store current positions on each timestemp iteration\n", "        oms.store_current_positions() \n", "        \n", "    logging_df = oms.performance_metrics.get_logging_df()\n", "    order_df = oms.performance_metrics.get_order_df()\n", "    trade_df = oms.performance_metrics.get_trade_df()\n", "    position_df = oms.performance_metrics.get_position_df() \n", "\n", "    #store in csv files\n", "    if is_save_results:\n", "        logging_df.write_excel(workbook=\"logging.xlsx\", autofilter=False)\n", "        df.write_excel(workbook='raw_data.xlsx', autofilter=False) \n", "        order_df.write_excel(workbook='orders.xlsx', autofilter=False) \n", "        trade_df.write_excel(workbook='trades.xlsx', autofilter=False) \n", "        position_df.write_excel(workbook='positions.xlsx', autofilter=False) \n", "        \n", "    #return data\n", "    return df, order_df, trade_df, position_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#Optimizer -> Run Multiple Backtests\n", "is_save_results = False\n", "is_output_stats = False\n", "\n", "my_optimizer = optimizer.Optimizer(\n", "    {\n", "        \"target_level_scaler\": optimizer.Optimizer_Independent_Variable(\"target_level_scaler\", var_from=3, var_to=3, var_stepsize=1),\n", "        \"stoploss_level_scaler\": optimizer.Optimizer_Independent_Variable(\"stoploss_level_scaler\", var_from=3, var_to=3, var_stepsize=1)\n", "    },\n", "    {\n", "        \"sharpe_ratio\": optimizer.Optimizer_Dependent_Variable(\"sharpe_ratio\"),\n", "        \"total_profit\": optimizer.Optimizer_Dependent_Variable(\"total_profit\")\n", "    })\n", "\n", "#iterate over each optimizer metric combination\n", "performances = []\n", "iteration_index = 1\n", "while (optimization_metrics := my_optimizer.iterate()) is not None:\n", "    \n", "    print(f\"Current backtest iteration:{iteration_index}\")\n", "    \n", "    #run backtest\n", "    raw_data, order_df, trade_df, position_df = run_backtest(source_data, optimization_metrics, is_save_results)\n", "    \n", "    #generate and capture statistics\n", "    stats = PerformanceStatistics(initial_funding, raw_data, order_df, trade_df, position_df, optimization_metrics)\n", "    stats.generate_statistics()\n", "    performances.append(stats)\n", "        \n", "    my_optimizer.store_values({\"sharpe_ratio\": stats.performance_metrics.sharpe_ratio, \"total_profit\": stats.performance_metrics.total_profit})\n", "        \n", "    #output statistics\n", "    if is_output_stats is True:\n", "        stats.output_performance_metrics()\n", "        \n", "    #increment  indicies\n", "    iteration_index += 1\n", "    \n", "#charting\n", "charting_data = my_optimizer.get_data([\"target_level_scaler\", \"stoploss_level_scaler\", \"sharpe_ratio\", \"total_profit\"])\n", "charting3D.plotly_3D_save_data(\"optimization_3D_data.dat\", charting_data)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#optimization for sharpe ratio\n", "data = charting3D.plotly_3D_load_data(\"optimization_3D_data.dat\")\n", "charting3D.plotly_3D_plot(\"Sharpe Ratios\", \"target_level_scaler\", \"stoploss_level_scaler\", \"sharpe_ratio\", data, 800, 800)\n", "\n", "#optimization for total profit\n", "data = charting3D.plotly_3D_load_data(\"optimization_3D_data.dat\")\n", "charting3D.plotly_3D_plot(\"Sharpe Ratios\", \"target_level_scaler\", \"stoploss_level_scaler\", \"total_profit\", data, 800, 800)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["performance = performances[0]\n", "performance.output_performance_metrics()\n", "position_df = performance.position_df\n", "\n", "#equity curve (all symbols, total base pnl)\n", "equity_curve = position_df.groupby(pl.col(\"timestamp\").dt.date()).agg(pl.col(\"daily_pnl\").sum()).sort(by=\"timestamp\").with_columns(pl.cumsum(\"daily_pnl\"))\n", "fig = px.line(title=\"Equity Curve\", x=equity_curve[\"timestamp\"], y=equity_curve[\"daily_pnl\"], width=1600, height=1200)\n", "fig.show()\n", "\n", "#annual returns (all symbols, total base pnl)\n", "annual_realized_pnl_base = position_df.groupby(pl.col(\"timestamp\").dt.year()).agg(pl.col(\"daily_pnl\").sum()).sort(by=\"timestamp\")\n", "fig = px.bar(title=\"Annual Pnl\", x=annual_realized_pnl_base[\"timestamp\"], y=annual_realized_pnl_base[\"daily_pnl\"], width=1600, height=600)\n", "fig.show()\n", "\n", "#monthly returns (all symbols, total base pnl)\n", "monthly_returns = position_df.set_sorted(\"timestamp\").groupby_dynamic(\"timestamp\", every=\"1mo\").agg(pl.col(\"daily_pnl\").sum()).sort(by=\"timestamp\")\n", "fig = px.bar(title=\"Monthly Pnl\", x=monthly_returns[\"timestamp\"], y=monthly_returns[\"daily_pnl\"], width=1600, height=600)\n", "fig.show()\n", "\n", "#daily returns (all symbols)\n", "daily_returns = position_df.set_sorted(\"timestamp\").groupby_dynamic(\"timestamp\", every=\"1d\").agg(pl.col(\"daily_pnl\").sum()).sort(by=\"timestamp\")\n", "fig = px.bar(title=\"Daily Pnl\", x=daily_returns[\"timestamp\"], y=daily_returns[\"daily_pnl\"], width=1600, height=600)\n", "fig.show()\n", "\n", "#total base pnl by fx pair\n", "pnl_by_fx_pair = position_df.groupby(pl.col(\"symbol\")).agg(pl.col(\"daily_pnl\").sum()).sort(by=\"symbol\")\n", "fig = px.bar(title=\"Total Base Pnl by Fx Pair\", x=pnl_by_fx_pair[\"symbol\"], y=pnl_by_fx_pair[\"daily_pnl\"], width=1600, height=600)\n", "fig.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fx_pair = \"USDCAD\"\n", "    \n", "#annotations\n", "trades = trade_df.filter(pl.col(\"symbol\") == fx_pair)\n", "\n", "annotations = []\n", "for row in trades.iter_rows(named=True):\n", "    \n", "    buy_sell = \"Buy\" if row[\"size\"] > 0 else \"Sell\"\n", "\n", "    #entry annotation\n", "    entry_text = f\"Entry<br>{buy_sell}<br>Size:{abs(row['size'])}<br>@{round(row['price_entry'], 5)}\"\n", "    if buy_sell == \"Buy\":\n", "        annotation_entry = dict(x=row[\"timestamp_entry\"], y=row[\"price_entry\"], ax=0, ay=30, arrowhead=2, arrowwidth=3, arrowcolor=\"white\", hovertext=entry_text)    \n", "    else:\n", "        annotation_entry = dict(x=row[\"timestamp_entry\"], y=row[\"price_entry\"], ax=0, ay=-30, arrowhead=2, arrowwidth=3, arrowcolor=\"white\", hovertext=entry_text)\n", "    \n", "    #exit annotation\n", "    exit_text = f\"Exit<br>{buy_sell}<br>Size:{abs(row['size'])}<br>@{round(row['price_exit'], 5)}<br>Pnl:{round(row['pnl_base'],2)}\"\n", "    if row[\"pnl_base\"] > 0:\n", "        annotation_exit = dict(x=row[\"timestamp_exit\"], y=row[\"price_exit\"], ax=-30, ay=0, arrowhead=2, arrowwidth=3, arrowcolor=\"green\", hovertext=exit_text)    \n", "    else:\n", "        annotation_exit = dict(x=row[\"timestamp_exit\"], y=row[\"price_exit\"], ax=-30, ay=0, arrowhead=2, arrowwidth=3, arrowcolor=\"red\", hovertext=exit_text)\n", "    \n", "    annotations.append(annotation_entry)\n", "    annotations.append(annotation_exit)\n", "\n", "#chart\n", "fig = go.Figure(data=go.Ohlc(x=df['Datetime'],\n", "                    open=df[f\"{fx_pair}_Open\"],\n", "                    high=df[f\"{fx_pair}_High\"],\n", "                    low=df[f\"{fx_pair}_Low\"],\n", "                    close=df[f\"{fx_pair}_Close\"]))\n", "fig.update_layout(width=1600, height=1200, annotations=annotations, paper_bgcolor=\"rgb(40,40,40)\", plot_bgcolor=\"rgb(40,40,40)\", font_color=\"white\")\n", "fig.update_yaxes(title_font_color=\"white\")\n", "fig.update(layout_xaxis_rangeslider_visible=False)\n", "fig.show()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}