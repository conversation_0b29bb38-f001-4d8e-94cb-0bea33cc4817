{"target_level_scaler": {"py/reduce": [{"py/function": "numpy.core.multiarray._reconstruct"}, {"py/tuple": [{"py/type": "numpy.n<PERSON><PERSON>"}, {"py/tuple": [0]}, {"py/b64": "Yg=="}]}, {"py/tuple": [1, {"py/tuple": [20]}, {"py/reduce": [{"py/type": "numpy.dtype"}, {"py/tuple": ["f8", false, true]}, {"py/tuple": [3, "<", null, null, null, -1, -1, 0]}]}, false, {"py/b64": "AAAAAAAA8D8AAAAAAAAAQAAAAAAAAAhAAAAAAAAAEEAAAAAAAAAUQAAAAAAAABhAAAAAAAAAHEAAAAAAAAAgQAAAAAAAACJAAAAAAAAAJEAAAAAAAAAmQAAAAAAAAChAAAAAAAAAKkAAAAAAAAAsQAAAAAAAAC5AAAAAAAAAMEAAAAAAAAAxQAAAAAAAADJAAAAAAAAAM0AAAAAAAAA0QA=="}]}]}, "stoploss_level_scaler": {"py/reduce": [{"py/function": "numpy.core.multiarray._reconstruct"}, {"py/tuple": [{"py/type": "numpy.n<PERSON><PERSON>"}, {"py/tuple": [0]}, {"py/b64": "Yg=="}]}, {"py/tuple": [1, {"py/tuple": [20]}, {"py/id": 2}, false, {"py/b64": "AAAAAAAA8D8AAAAAAAAAQAAAAAAAAAhAAAAAAAAAEEAAAAAAAAAUQAAAAAAAABhAAAAAAAAAHEAAAAAAAAAgQAAAAAAAACJAAAAAAAAAJEAAAAAAAAAmQAAAAAAAAChAAAAAAAAAKkAAAAAAAAAsQAAAAAAAAC5AAAAAAAAAMEAAAAAAAAAxQAAAAAAAADJAAAAAAAAAM0AAAAAAAAA0QA=="}]}]}, "sharpe_ratio": {"py/reduce": [{"py/function": "numpy.core.multiarray._reconstruct"}, {"py/tuple": [{"py/type": "numpy.n<PERSON><PERSON>"}, {"py/tuple": [0]}, {"py/b64": "Yg=="}]}, {"py/tuple": [1, {"py/tuple": [400]}, {"py/id": 2}, false, {"py/b64": "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"}]}]}, "total_profit": {"py/reduce": [{"py/function": "numpy.core.multiarray._reconstruct"}, {"py/tuple": [{"py/type": "numpy.n<PERSON><PERSON>"}, {"py/tuple": [0]}, {"py/b64": "Yg=="}]}, {"py/tuple": [1, {"py/tuple": [400]}, {"py/id": 2}, false, {"py/b64": "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"}]}]}}