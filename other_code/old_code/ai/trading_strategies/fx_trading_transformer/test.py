import os
import numpy as np
import pandas as pd


#fx pairs
pairs = ["EURUSD", "GBPUSD", "AUDUSD", "NZDUSD", "USDCAD", "USDJPY", "USDCHF",
         "EURGBP", "EURAUD", "EURNZD", "EURJPY", "EURCAD", "EURCHF",
         "GBPAUD", "GBPNZD", "GBPJPY", "GBPCAD", "GBPCHF",
         "AUDNZD", "AUDJPY", "AUDCAD", "AUDCHF",
         "NZDJPY", "NZDCAD", "NZDCHF",
         "CADJPY", "CADCHF",
         "CHFJPY"]

#get files in directory
dir = "C:/dukascopy data/"
files = os.listdir(dir)

#test files
usd_files = [file for file in files if "USDJPY" in file]

df = None
for file in usd_files:
    
    fx_pair = file[0:6] 
    print(fx_pair)
    
    #name columns
    if "Bid" in file:
        raw_df = pd.read_csv(os.path.join(dir, file), sep=",", index_col=0, header=0, names=["Timestamp", "Open_Bid", "High_Bid", "Low_Bid", "Close_Bid"], usecols=[0,1,2,3,4])    
    elif "Ask" in file:
        raw_df = pd.read_csv(os.path.join(dir, file), sep=",", index_col=0, header=0, names=["Timestamp", "Open_Ask", "High_Ask", "Low_Ask", "Close_Ask"], usecols=[0,1,2,3,4])
    else:
        raise ValueError("Could not find Bid or Ask string in filename")      
    
    if df is None:
        df = raw_df
    else:
        df = df.join(raw_df, how="outer")
        
        #generate mid prices from bid and ask
        df["Open"] = (df["Open_Bid"] + df["Open_Ask"]) / 2
        df["High"] = (df["High_Bid"] + df["High_Ask"]) / 2
        df["Low"] = (df["Low_Bid"] + df["Low_Ask"]) / 2
        df["Close"] = (df["Close_Bid"] + df["Close_Ask"]) / 2
        
        #round values
        if "JPY" in fx_pair:
            df = df.round(3)
        else:
            df = df.round(5)
        
        #remove unused columns
        df = df.iloc[:, -4:]
        
        #drop na
        df = df.dropna()
                
        #store dataframe back as csv file
        pd.DataFrame.to_csv(df, os.path.join(dir, "new", f"{fx_pair}_1minute.csv"))

        #reset df to none
        df = None
