{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "\n", "#fx pairs\n", "fx_pairs = [\"EURUSD\", \"GBPUSD\", \"AUDUSD\", \"NZDUSD\", \"USDCAD\", \"USDJPY\", \"USDCHF\",\n", "         \"EURGBP\", \"EURAUD\", \"EURNZD\", \"EURJPY\", \"EURCAD\", \"EURCHF\",\n", "         \"GBPAUD\", \"GBPNZD\", \"GBPJPY\", \"GBPCAD\", \"GBPCHF\",\n", "         \"AUDNZD\", \"AUDJPY\", \"AUDCAD\", \"AUDCHF\",\n", "         \"NZDJPY\", \"NZDCAD\", \"NZDCHF\",\n", "         \"CADJPY\", \"CADCHF\",\n", "         \"CHFJPY\"]\n", "\n", "fx_baskets = {\"USD\": [\"EURUSD\", \"GBPUSD\", \"AUDUSD\", \"NZDUSD\", \"USDCAD\", \"USDJPY\", \"USDCHF\"],\n", "              \"EUR\": [\"EURUSD\", \"EURGBP\", \"EURAUD\", \"EURNZD\", \"EURJPY\", \"EURCAD\", \"EURCHF\"],\n", "              \"GBP\": [\"GBPUSD\", \"EURGBP\", \"GBPAUD\", \"GBPNZD\", \"GBPJPY\", \"GBPCAD\", \"GBPCHF\"], \n", "              \"AUD\": [\"AUDUSD\", \"EURAUD\", \"GBPAUD\", \"AUDNZD\", \"AUDJPY\", \"AUDCAD\", \"AUDCHF\"], \n", "              \"NZD\": [\"NZDUSD\", \"EURNZD\", \"GBPNZD\", \"AUDNZD\", \"NZDJPY\", \"NZDCAD\", \"NZDCHF\"],\n", "              \"CAD\": [\"USDCAD\", \"EURCAD\", \"GBPCAD\", \"AUDCAD\", \"NZDCAD\", \"CADJPY\", \"CADCHF\"],\n", "              \"CHF\": [\"USDCHF\", \"EURCHF\", \"GBPCHF\", \"AUDCHF\", \"NZDCHF\", \"CADCHF\", \"CHFJPY\"],\n", "              \"JPY\": [\"USDJPY\", \"EURJPY\", \"GBPJPY\", \"AUDJPY\", \"NZDJPY\", \"CADJPY\", \"CHFJPY\"]}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Import Data From Dukascopy"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def import_from_dukascopy():\n", "    \n", "    #get files in directory\n", "    source_directory = \"C:/dukascopy data/\"\n", "    target_directory = \"C:/dukascopy_minute_data/\"\n", "    files = os.listdir(source_directory)\n", "    fx_pairs = set([file[0:6] for file in files])\n", "   \n", "    df = None\n", "    for fx_pair in fx_pairs:\n", "        \n", "        print(f\"Import the following data for fx pair: {fx_pair}\")\n", "        for source_path_filename in [os.path.join(source_directory, file) for file in files if fx_pair in file]:\n", "        \n", "            #name columns\n", "            if \"Bid\" in source_path_filename:\n", "                raw_df = pd.read_csv(source_path_filename, sep=\",\", index_col=0, header=0, names=[\"Timestamp\", \"Open_Bid\", \"High_Bid\", \"Low_Bid\", \"Close_Bid\"], usecols=[0,1,2,3,4])    \n", "            elif \"Ask\" in source_path_filename:\n", "                raw_df = pd.read_csv(source_path_filename, sep=\",\", index_col=0, header=0, names=[\"Timestamp\", \"Open_Ask\", \"High_Ask\", \"Low_Ask\", \"Close_Ask\"], usecols=[0,1,2,3,4])\n", "            else:\n", "                raise ValueError(\"Could not find <PERSON><PERSON> or Ask string in filename\")      \n", "            \n", "            if df is None:\n", "                df = raw_df\n", "            else:\n", "                df = df.join(raw_df, how=\"outer\")\n", "                \n", "                #generate mid prices from bid and ask\n", "                df[\"Open\"] = (df[\"Open_Bid\"] + df[\"Open_Ask\"]) / 2\n", "                df[\"High\"] = (df[\"High_Bid\"] + df[\"High_Ask\"]) / 2\n", "                df[\"Low\"] = (df[\"Low_Bid\"] + df[\"Low_Ask\"]) / 2\n", "                df[\"Close\"] = (df[\"Close_Bid\"] + df[\"Close_Ask\"]) / 2\n", "                \n", "                #round values\n", "                if \"JPY\" in fx_pair:\n", "                    df = df.round(3)\n", "                else:\n", "                    df = df.round(5)\n", "                \n", "                #remove unused columns\n", "                df = df.iloc[:, -4:]\n", "                \n", "                #drop na\n", "                df = df.dropna()\n", "                        \n", "                #store dataframe back as csv file\n", "                print(f\"Export the following data for fx pair: {fx_pair}\")\n", "                pd.DataFrame.to_csv(df, os.path.join(target_directory, f\"{fx_pair}_1minute.csv\"))\n", "\n", "                #reset df to none\n", "                df = None\n", "                \n", "\n", "def generate_single_dataframe(source_dir:str, target_dir:str):\n", "                  \n", "    df = None\n", "    for fx_pair in fx_pairs:\n", "\n", "        print(f\"Reading the following fx pair: {fx_pair}\")\n", "        new_df = pd.read_csv(os.path.join(source_dir, f\"{fx_pair}_1minute.csv\"), sep=\",\", index_col=0, header=0, \n", "                             names=[\"Timestamp\", f\"{fx_pair}_Open\", f\"{fx_pair}_High\", f\"{fx_pair}_Low\", f\"{fx_pair}_Close\"])  \n", "        \n", "        if df is None:\n", "              df = new_df\n", "        else:\n", "            df = df.join(new_df, how=\"outer\")\n", "     \n", "    # df = df.fillna(method=\"ffill\")\n", "    df = df.ffill(axis=0)\n", "    df = df.dropna()\n", "    print(\"Exporting single dataframe\")\n", "    df.to_csv(\"C:/Users/<USER>/Downloads/df.csv\")\n", "     \n", "    return df"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Generate Features"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading Raw Dataframe from disk\n", "generate fx basket\n", "save dataframe to disk\n"]}], "source": ["def generate_features():\n", "    \n", "    print(\"Loading Raw Dataframe from disk\")\n", "    raw_df = pd.read_csv(\"C:/Users/<USER>/Downloads/df.csv\", sep=\",\", index_col=0, header=0) #nrows=10000\n", "\n", "    #choose basket\n", "    basket_ccy = \"USD\"\n", "    basket = fx_baskets[basket_ccy]\n", "\n", "    #generate fx basket\n", "    print(\"generate fx basket\")\n", "    for bar_part in [\"Open\", \"High\", \"Low\", \"Close\"]:\n", "        \n", "        basket_series = None\n", "        \n", "        for fx_pair in basket:\n", "            \n", "            series = raw_df[f\"{fx_pair}_{bar_part}\"]\n", "            \n", "            #divide by 100 for yen base pairs\n", "            if \"JPY\" in fx_pair:\n", "                series = series / 100\n", "            \n", "            #inverse if basket currency is base currency of fx pair \n", "            #also swap high and low series to account for the inverse\n", "            if basket_ccy == fx_pair[-3:]:\n", "                if bar_part == \"High\":\n", "                    series = 1 / raw_df[f\"{fx_pair}_Low\"]\n", "                elif bar_part == \"Low\":\n", "                    series = 1 / raw_df[f\"{fx_pair}_High\"]\n", "                else:\n", "                    series = 1 / series\n", "            \n", "            #add to basket\n", "            if basket_series is None:\n", "                basket_series = series\n", "            else:\n", "                basket_series = basket_series + series\n", "                \n", "        basket_series = basket_series / len(basket)  \n", "        raw_df[f\"{basket_ccy}_{bar_part}\"] = basket_series\n", "              \n", "    #generate features\n", "    df = pd.DataFrame()\n", "    symbols = [basket_ccy] + basket\n", "         \n", "    for symbol in symbols:\n", "        \n", "        #closing price\n", "        # df[f\"{symbol}_Open\"] = raw_df[f\"{symbol}_Open\"]\n", "        # df[f\"{symbol}_High\"] = raw_df[f\"{symbol}_High\"]\n", "        # df[f\"{symbol}_Low\"] = raw_df[f\"{symbol}_Low\"]\n", "        df[f\"{symbol}price\"] = raw_df[f\"{symbol}_Close\"]\n", "        \n", "        #distance from SMA\n", "        sma_window_size = 15\n", "        sma = raw_df[f\"{symbol}_Close\"].rolling(sma_window_size, min_periods=sma_window_size).mean()\n", "        df[f\"{symbol}_smadistance\"] = raw_df[f\"{symbol}_Close\"] / sma - 1\n", "        \n", "        #min/max percent\n", "        df[f\"{symbol}_minmax\"] = raw_df[f\"{symbol}_High\"] / raw_df[f\"{symbol}_Low\"] - 1\n", "                    \n", "        #Window Highs and lows and standard deviation\n", "        windows = [10, 60, 1440] #[10, 60, 1440]\n", "        for window in windows:\n", "            df[f\"{symbol}_window_{window}_high\"] = raw_df[f\"{symbol}_High\"].rolling(window, min_periods=window).max() / raw_df[f\"{symbol}_Close\"] - 1\n", "            df[f\"{symbol}_window_{window}_low\"] = raw_df[f\"{symbol}_Close\"] / raw_df[f\"{symbol}_Low\"].rolling(window, min_periods=window).min() - 1\n", "            df[f\"{symbol}_window_{window}_stdev\"] = raw_df[f\"{symbol}_Close\"].rolling(window, min_periods=window).std()\n", "\n", "    #remove rows with empty cells\n", "    df = df.dropna()\n", "    \n", "    #sort by column\n", "    # df = df.reindex(sorted(df.columns), axis=1)\n", "                \n", "    #save dataframe  \n", "    print(\"save dataframe to disk\")\n", "    df.to_csv(\"C:/Users/<USER>/Downloads/features.csv\")\n", "    return df\n", "           \n", "df = generate_features()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"interpreter": {"hash": "2df95ccf3dd4d8d55e8628a7094851bf6700911f731acd25ee9d5219fc123dda"}, "kernelspec": {"display_name": "Python 3.9.7 64-bit", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}