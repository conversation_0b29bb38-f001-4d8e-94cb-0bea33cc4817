import numpy as np
import pandas as pd
import os
import gc
import os.path as path
import time
import datetime
from sklearn.preprocessing import StandardScaler
from sklearn.preprocessing import RobustScaler
from matplotlib import pyplot

stocks_path = os.path.join(os.getcwd(), "stock trading model", "source_datasets", "raw_data.csv")
df = pd.read_csv(stocks_path, header=0, index_col=0, parse_dates=True, dtype=np.float32)
returns_df = df.pct_change()

print(returns_df["S_A_CLOSE"].describe(percentiles=[0.0, 0.1, 0.2, 0.3, 0.5, 0.7, 0.8, 0.9, 1.0]))
print()

returns = pd.DataFrame(returns_df["S_A_CLOSE"])

scaler = RobustScaler()
standardized = pd.DataFrame(scaler.fit_transform(returns))
des = standardized[0].describe(percentiles=[0.1, 0.26, 0.42, 0.58, 0.74, 0.9])
print(des)


print(standardized.quantile(0.1))
print(standardized.quantile(0.26))
print(standardized.quantile(0.42))
print(standardized.quantile(0.58))
print(standardized.quantile(0.74))
print(standardized.quantile(0.9))


print(des[0]["10%"])
print(des[0]["26%"])
print(des[0]["42%"])
print(des[0]["58%"])
print(des["74%"])
print(des["90%"])


standardized.hist(bins=100)
pyplot.show()

scaler = RobustScaler(quantile_range=(10,90))
standardized = pd.DataFrame(scaler.fit_transform(returns_df), index=returns_df.index, columns=returns_df.columns)
print(standardized["S_A_CLOSE"].describe(percentiles=[0.0, 0.1, 0.2, 0.3, 0.5, 0.7, 0.8, 0.9, 1.0]))

standardized["S_A_CLOSE"].hist(bins=100)
pyplot.show()