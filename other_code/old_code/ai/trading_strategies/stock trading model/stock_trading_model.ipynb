{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Declarations and Imports"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import os\n", "import gc\n", "import os.path as path\n", "import time\n", "import datetime\n", "import matplotlib.pyplot as plt\n", "import scipy\n", "import pickle\n", "import seaborn as sns\n", "import tensorflow as tf\n", "from tensorflow import keras\n", "from tensorflow.keras import layers\n", "from tensorflow.keras.models import Sequential\n", "from tensorflow.keras.layers import LSTM\n", "from sklearn.preprocessing import StandardScaler\n", "from sklearn.preprocessing import RobustScaler\n", "from sklearn.preprocessing import Normalizer\n", "\n", "from pushover import init, Client\n", "\n", "gpus = tf.config.experimental.list_physical_devices('GPU')\n", "for gpu in gpus:\n", "  print(gpu)\n", "  tf.config.experimental.set_memory_growth(gpu, True)\n", "\n", "#set whether to run eagerly or build a graph\n", "tf.executing_eagerly = True"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class ModelParameters(object):\n", "\n", "    def __init__(self, sequence_length, number_output_channels, label_shift, is_shuffle, batch_size,\n", "        number_layers, number_heads, d_model, ff_dim, dense_units, dropout_rate,\n", "        epochs, learning_rate_warmup_steps):\n", "\n", "        #data parameters\n", "        self.sequence_length = sequence_length #how many data points in a sequence\n", "        self.number_output_channels = number_output_channels  #how many possible output values (0 and 1 for now)\n", "        self.label_shift = label_shift #1 = shift label 1 forward => predict next element\n", "        self.is_shuffle = is_shuffle #shuffles the sequences in batches (order within sequence is preserved)\n", "        self.batch_size = batch_size  #how many sequences in each batch\n", "\n", "        #model parameters\n", "        self.number_layers = number_layers #how many encoder layers make up the entire encoder\n", "        self.number_heads = number_heads #how many attention heads\n", "        self.d_model = d_model #number features\n", "        self.ff_dim = ff_dim \n", "        self.dense_units = dense_units #mlp_units (dense layer units right after global pooling layer)\n", "        self.dropout_rate = dropout_rate #regularization (0.1=drop 10% of output neurons)\n", "\n", "        #training parameters\n", "        self.epochs = epochs\n", "        self.learning_rate_warmup_steps = learning_rate_warmup_steps"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Data Preprocessing"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_features(df:pd.DataFrame, window_size:int):\n", "    array = np.lib.stride_tricks.sliding_window_view(df, window_shape=(window_size, len(df.columns)))\n", "    array = array.reshape(-1,window_size,len(df.columns))[0:-1,:]   \n", "    array\n", "    return array\n", "\n", "\n", "def get_targets(series:pd.Series, window_size:int):\n", "    array = series[window_size:].to_numpy()\n", "    return array\n", "   \n", "\n", "def bloomberg_raw_data_to_dataframe():    \n", "\n", "    df = None\n", "\n", "    #iterate over each stock raw file\n", "    stocks_path = os.path.join(os.getcwd(), \"raw_data\")\n", "    filenames = [filename for filename in os.listdir(stocks_path)]\n", "    counter = 0\n", "\n", "    for filename in filenames:\n", "        \n", "        #load raw data\n", "        symbol_df = pd.read_csv(os.path.join(stocks_path, filename), header=1, index_col=0, parse_dates=True, dtype=np.float32)\n", "        symbol_df.index.name = \"Date\"\n", "        symbol_df.index = pd.to_datetime(symbol_df.index, errors=\"coerce\")\n", "        symbol_df = symbol_df.dropna()      \n", "\n", "        #obtain symbol name and rename columns\n", "        if \"_F.csv\" in filename:\n", "            #non stock\n", "            symbol = filename.split(\"_\")[0]\n", "            symbol_df = symbol_df.rename(columns={\"PX_OPEN\": symbol + \"_OPEN\", \"PX_HIGH\": symbol + \"_HIGH\", \"PX_LOW\": symbol + \"_LOW\", \"PX_LAST\": symbol + \"_CLOSE\"})\n", "        else:\n", "            #stock\n", "            symbol = \"S_\" + filename.split(\".\")[0]\n", "            symbol_df = symbol_df.rename(columns={\"PX_OPEN\": symbol + \"_OPEN\", \"PX_HIGH\": symbol + \"_HIGH\", \"PX_LOW\": symbol + \"_LOW\", \"PX_LAST\": symbol + \"_CLOSE\", \"PX_VOLUME\": symbol + \"_VOLUME\"})\n", "             \n", "        \n", "        #sort by index\n", "        symbol_df.sort_index(inplace=True)\n", "            \n", "        #merge dataframes\n", "        if df is None:\n", "            df = symbol_df\n", "        else:\n", "            df = pd.merge(df, symbol_df, on=\"Date\", how=\"outer\")   \n", "               \n", "        counter += 1\n", "        print(f\"Processed {counter}/{len(filenames)} Files - Filename: {filename}\")\n", "\n", "    #sort by index\n", "    df.sort_index(inplace=True)\n", "\n", "    #fill missing values\n", "    df = df.fillna(method=\"ffill\")\n", "\n", "    #store dataframe on disk\n", "    pathfilename = os.path.join(os.path.join(os.getcwd(), \"source_datasets\", \"raw_data.csv\"))\n", "    df.to_csv(pathfilename)\n", "\n", "\n", "def generate_dataset_v1(dataset_name:str, parameters:ModelParameters, dt_start:str, dt_end:str, \n", "    stocks_to_include:list, is_save_scaler:bool, is_apply_saved_scaler:bool):\n", "\n", "    other_feature_columns = [\"VIX_OPEN\", \"VIX_HIGH\", \"VIX_LOW\", \"VIX_CLOSE\",\n", "                             \"EURODOLLAR_OPEN\", \"EURODOLLAR_HIGH\", \"EURODOLLAR_LOW\", \"EURODOLLAR_CLOSE\",\n", "                             \"5YEARBON<PERSON>_OP<PERSON>\", \"5YEARBOND_HIGH\", \"5YEARBOND_LOW\", \"5YEARBOND_CLOSE\", \n", "                             \"10YEARBON<PERSON>_OP<PERSON>\", \"10YEARBOND_HIGH\", \"10YEARBOND_LOW\", \"10YEARBOND_CLOSE\",\n", "                             \"30YEARBON<PERSON>_OP<PERSON>\", \"30YEARBOND_HIGH\", \"30YEARBOND_LOW\", \"30YEARBOND_CLOSE\", \n", "                             \"GOLD_OPEN\", \"GOLD_HIGH\", \"GOLD_LOW\", \"GOLD_CLOSE\",\n", "                             \"SPX500_OPEN\", \"SPX500_HIGH\", \"SPX500_LOW\", \"SPX500_CLOSE\", \n", "                             \"RUSSELL2000_OP<PERSON>\", \"RUSSELL2000_HIGH\", \"RUSSELL2000_LOW\", \"RUSSELL2000_CLOSE\", \n", "                             \"CRU<PERSON><PERSON><PERSON>_OPEN\", \"CRUDEOIL_HIGH\", \"CRU<PERSON><PERSON><PERSON>_LOW\", \"CRUDEOIL_CLOSE\",\n", "                             \"NATGAS_OPEN\", \"NATGAS_HIGH\", \"NATGAS_LOW\", \"NATGAS_CLOSE\"]\n", "\n", "    #load dataframe\n", "    path_filename = os.path.join(os.getcwd(), \"source_datasets\", \"raw_data.csv\")\n", "    df = pd.read_csv(path_filename, header=0, index_col=0, parse_dates=True, dtype=np.float32)\n", "\n", "    #filter out unwanted stocks\n", "    stock_symbols = set([col.split(\"_\")[1] for col in df.columns if col.split(\"_\")[0] == \"S\"])\n", "    if len(stocks_to_include) > 0:\n", "        stock_symbols = [symbol for symbol in stock_symbols if symbol in stocks_to_include]\n", "        \n", "    stock_symbols = list(stock_symbols)\n", "    stock_symbols.sort()\n", "    \n", "    #iterate over all stock symbols\n", "    count = 0\n", "    master_features_for_scaler = None\n", "    master_df = None\n", "    \n", "    for stock_symbol in stock_symbols:\n", "\n", "        #increment counter\n", "        count += 1\n", "\n", "        #obtain all feature columns\n", "        feature_columns = [f\"S_{stock_symbol}_OPEN\", f\"S_{stock_symbol}_HIGH\", f\"S_{stock_symbol}_LOW\", f\"S_{stock_symbol}_CLOSE\", f\"S_{stock_symbol}_VOLUME\"] + other_feature_columns\n", "              \n", "        #filter columns\n", "        sub_df = df[feature_columns]\n", "\n", "        #filter rows by start and end dates (taking into account sequence length)   \n", "        index_dt_start = sub_df.index.get_loc(dt_start, method=\"nearest\")\n", "        index_dt_end = sub_df.index.get_loc(dt_end, method=\"nearest\")   \n", "        sub_df = sub_df[max(0, index_dt_start - parameters.sequence_length) : min(len(sub_df), index_dt_end + 1)]\n", "\n", "        #remove rows with missing values\n", "        sub_df = sub_df.dropna()\n", "\n", "        #continue if there is no data\n", "        if len(sub_df) < int(parameters.sequence_length):\n", "            print(f\"Skip Symbol ({stock_symbol}) because length of Dataframe is: {len(sub_df)}\")\n", "            continue\n", "\n", "        #rename stock columns (done in order to sort columns later correctly)\n", "        sub_df = sub_df.rename(columns={\n", "            f\"S_{stock_symbol}_OPEN\": \"STOCK_OPEN\", \n", "            f\"S_{stock_symbol}_HIGH\": \"STOCK_HIGH\",\n", "            f\"S_{stock_symbol}_LOW\": \"STOCK_LOW\",\n", "            f\"S_{stock_symbol}_CLOSE\": \"STOCK_CLOSE\",\n", "            f\"S_{stock_symbol}_VOLUME\": \"STOCK_VOLUME\"})\n", "\n", "        #columns to convert to percentages\n", "        columns_to_convert_to_pct = sub_df.columns\n", "\n", "        #add next_days \n", "        column_names = set([col.split(\"_\")[0] for col in sub_df.columns])\n", "        for column_name in column_names:\n", "            sub_df[column_name + \"_OPENVSPREVCLOSE\"] = sub_df[column_name + \"_OPEN\"].shift(-1) / sub_df[column_name + \"_CLOSE\"] - 1\n", "\n", "        #capture feature columns\n", "        feature_columns = sub_df.columns\n", "\n", "        #generate TARGET series that only contains open-close returns (for inferencing purposes)\n", "        sub_df[\"TARGET_RAW\"] = (sub_df[\"STOCK_CLOSE\"] / sub_df[\"STOCK_OPEN\"] - 1)\n", "        sub_df[\"TARGET_CLASSIFIED\"] = sub_df[\"TARGET_RAW\"].transform(lambda x: 1 if x >= 0 else 0)\n", "\n", "        #convert raw data to return data\n", "        sub_df[columns_to_convert_to_pct] = sub_df[columns_to_convert_to_pct].pct_change()\n", "\n", "        #cleanse data\n", "        sub_df = sub_df.dropna()\n", "\n", "        #extract features and targets\n", "        features_df = sub_df[feature_columns]\n", "        features_df = features_df.reindex(sorted(features_df.columns), axis=1)\n", "        targets = sub_df[\"TARGET_CLASSIFIED\"]\n", "        targets = targets.astype(int)\n", "\n", "        #normalize data\n", "        if is_apply_saved_scaler:\n", "            scaler = scaler = pickle.load(open(os.path.join(os.getcwd(), \"source_datasets\", f\"{dataset_name}_scaler.save\"), 'rb'))\n", "            features_df = pd.DataFrame(scaler.transform(features_df), index=features_df.index, columns=features_df.columns)\n", "        else:\n", "            scaler = StandardScaler()  \n", "            features_df = pd.DataFrame(scaler.fit_transform(features_df), index=features_df.index, columns=features_df.columns)\n", "\n", "        #collect dataframes of features of all stocks and concatenate in order to apply and store scaler to all data\n", "        if is_save_scaler:\n", "            if master_features_for_scaler is None:\n", "                master_features_for_scaler = features_df\n", "            else:\n", "                master_features_for_scaler = pd.concat([master_features_for_scaler, features_df], axis=0)\n", "\n", "        #ignore dataframe if number rows is not sufficient to form sequences\n", "        if len(features_df) < int(parameters.sequence_length):\n", "            print(f\"Skip Symbol ({stock_symbol}) because length of Dataframe is: {len(features_df)}\")\n", "            continue\n", "\n", "        #generate dataset\n", "        dataset = tf.keras.preprocessing.sequence.TimeseriesGenerator(\n", "                features_df,\n", "                targets, \n", "                length=parameters.sequence_length,\n", "                sampling_rate=1,\n", "                stride=1,\n", "                shuffle=False, #Always false here as sequences first need to get stacked \n", "                batch_size=10000000) #set to large value in order to concatenate numpy arrays later\n", "\n", "        #generate dataframe that contains all data for given symbol\n", "        single_df = pd.DataFrame({\n", "            f\"{stock_symbol}_RETURNS\": sub_df[\"TARGET_RAW\"][parameters.sequence_length:],\n", "            f\"{stock_symbol}_FEATURES\": list(dataset[0][0]),\n", "            f\"{stock_symbol}_TARGETS\": dataset[0][1]})\n", "\n", "        #concatanate dataframes\n", "        if master_df is None:\n", "            master_df = single_df\n", "        else:\n", "            master_df = pd.merge(master_df, single_df, on=\"Date\", how=\"outer\") \n", "\n", "        print(f\"\\nSymbol: {stock_symbol}\")\n", "        print(f\"Progress: {count}/{len(stock_symbols)}\")\n", "        print(f\"Feature Count:{len(features_df.columns)}\")\n", "        print(f\"Feature Row Size:{len(features_df)}\")\n", "        print(f\"Target Row Size:{len(targets)}\")\n", "\n", "    #scale and save scaler across all stocks\n", "    if is_save_scaler:\n", "        scaler = StandardScaler()  \n", "        scaler.fit(master_features_for_scaler)\n", "        joblib.dump(scaler, os.path.join(os.getcwd(), \"source_datasets\", f\"{dataset_name}_scaler.save\"))\n", "\n", "    #store numpy array on disk\n", "    print(\"Store dataframe on disk...\")\n", "    master_df.to_csv(os.path.join(os.getcwd(), \"source_datasets\", f\"{dataset_name}_dataframe.csv\"))\n", "    print(\"Done storing dataframe\")\n", "\n", "\n", "def generate_dataset_v2(dataset_name:str, parameters:ModelParameters, dt_start:str, dt_end:str, \n", "    stocks_to_include:list, is_save_scaler:bool, is_apply_saved_scaler:bool):\n", "\n", "    other_feature_columns = [\"VIX_OPEN\", \"VIX_HIGH\", \"VIX_LOW\", \"VIX_CLOSE\",\n", "                             \"EURODOLLAR_OPEN\", \"EURODOLLAR_HIGH\", \"EURODOLLAR_LOW\", \"EURODOLLAR_CLOSE\",\n", "                             \"5YEARBON<PERSON>_OP<PERSON>\", \"5YEARBOND_HIGH\", \"5YEARBOND_LOW\", \"5YEARBOND_CLOSE\", \n", "                             \"10YEARBON<PERSON>_OP<PERSON>\", \"10YEARBOND_HIGH\", \"10YEARBOND_LOW\", \"10YEARBOND_CLOSE\",\n", "                             \"30YEARBON<PERSON>_OP<PERSON>\", \"30YEARBOND_HIGH\", \"30YEARBOND_LOW\", \"30YEARBOND_CLOSE\", \n", "                             \"GOLD_OPEN\", \"GOLD_HIGH\", \"GOLD_LOW\", \"GOLD_CLOSE\",\n", "                             \"SPX500_OPEN\", \"SPX500_HIGH\", \"SPX500_LOW\", \"SPX500_CLOSE\", \n", "                             \"RUSSELL2000_OP<PERSON>\", \"RUSSELL2000_HIGH\", \"RUSSELL2000_LOW\", \"RUSSELL2000_CLOSE\", \n", "                             \"CRU<PERSON><PERSON><PERSON>_OPEN\", \"CRUDEOIL_HIGH\", \"CRU<PERSON><PERSON><PERSON>_LOW\", \"CRUDEOIL_CLOSE\",\n", "                             \"NATGAS_OPEN\", \"NATGAS_HIGH\", \"NATGAS_LOW\", \"NATGAS_CLOSE\"]\n", "    #load dataframe\n", "    path_filename = os.path.join(os.getcwd(), \"source_datasets\", \"raw_data.csv\")\n", "    df = pd.read_csv(path_filename, header=0, index_col=0, parse_dates=True, dtype=np.float32)\n", "\n", "    #define specific columns\n", "    columns_to_keep = []\n", "    unique_column_names = []\n", "    for column_name in df.columns:\n", "        \n", "        col_split = column_name.split(\"_\")\n", "        if len(stocks_to_include) > 0 and col_split[0] == \"S\" and col_split[1] not in stocks_to_include:\n", "            continue\n", "        \n", "        #keep this column    \n", "        columns_to_keep.append(column_name)\n", "\n", "        #keep unique column names\n", "        if \"OPEN\" in column_name:\n", "            unique_column_names.append(column_name[:column_name.rfind(\"_\")])\n", "    \n", "    #filter out unwanted columns \n", "    df = df[columns_to_keep]\n", "\n", "    #filter rows by start and end dates (taking into account sequence length)   \n", "    index_dt_start = df.index.get_loc(dt_start, method=\"nearest\")\n", "    index_dt_end = df.index.get_loc(dt_end, method=\"nearest\")   \n", "    df = df[max(0, index_dt_start - parameters.sequence_length) : min(len(df), index_dt_end + 1)]    \n", "\n", "    #columns to convert to percentages\n", "    columns_to_convert_to_pct = df.columns\n", "\n", "    #add extra feature (percent return: open vs previous close) \n", "    for unique_column_name in unique_column_names:\n", "        df[f\"{unique_column_name}_OPENVSPREVCLOSE\"] = df[f\"{unique_column_name}_OPEN\"].shift(-1) / df[f\"{unique_column_name}_CLOSE\"] - 1\n", "\n", "    #generate TARGET series that only contains open-close returns (for inferencing purposes)\n", "        sub_df[\"TARGET_RAW\"] = (sub_df[\"STOCK_CLOSE\"] / sub_df[\"STOCK_OPEN\"] - 1)\n", "        sub_df[\"TARGET_CLASSIFIED\"] = sub_df[\"TARGET_RAW\"].transform(lambda x: 1 if x >= 0 else 0)\n", "\n", "\n", "\n", "\n", "    sub_df = sub_df.rename(columns={\n", "            f\"S_{stock_symbol}_OPEN\": \"STOCK_OPEN\", \n", "            f\"S_{stock_symbol}_HIGH\": \"STOCK_HIGH\",\n", "            f\"S_{stock_symbol}_LOW\": \"STOCK_LOW\",\n", "            f\"S_{stock_symbol}_CLOSE\": \"STOCK_CLOSE\",\n", "            f\"S_{stock_symbol}_VOLUME\": \"STOCK_VOLUME\"})\n", "\n", "    \n", "\n", "\n", "\n", "    #iterate over all stock symbols\n", "    count = 0\n", "    master_features_for_scaler = None\n", "    master_df = None\n", "    \n", "    for stock_symbol in stock_symbols:\n", "\n", "        #increment counter\n", "        count += 1\n", "\n", "        \n", "        #filter columns\n", "        sub_df = df[feature_columns]\n", "\n", "        #remove rows with missing values\n", "        sub_df = sub_df.dropna()\n", "\n", "        #continue if there is no data\n", "        if len(sub_df) < int(parameters.sequence_length):\n", "            print(f\"Skip Symbol ({stock_symbol}) because length of Dataframe is: {len(sub_df)}\")\n", "            continue\n", "\n", "        #rename stock columns (done in order to sort columns later correctly)\n", "        \n", "\n", "        #columns to convert to percentages\n", "        columns_to_convert_to_pct = sub_df.columns\n", "\n", "        #add next_days \n", "        column_names = set([col.split(\"_\")[0] for col in sub_df.columns])\n", "        for column_name in column_names:\n", "            sub_df[column_name + \"_OPENVSPREVCLOSE\"] = sub_df[column_name + \"_OPEN\"].shift(-1) / sub_df[column_name + \"_CLOSE\"] - 1\n", "\n", "        #capture feature columns\n", "        feature_columns = sub_df.columns\n", "\n", "        #generate TARGET series that only contains open-close returns (for inferencing purposes)\n", "        sub_df[\"TARGET_RAW\"] = (sub_df[\"STOCK_CLOSE\"] / sub_df[\"STOCK_OPEN\"] - 1)\n", "        sub_df[\"TARGET_CLASSIFIED\"] = sub_df[\"TARGET_RAW\"].transform(lambda x: 1 if x >= 0 else 0)\n", "\n", "        #convert raw data to return data\n", "        sub_df[columns_to_convert_to_pct] = sub_df[columns_to_convert_to_pct].pct_change()\n", "\n", "        #cleanse data\n", "        sub_df = sub_df.dropna()\n", "\n", "        #extract features and targets\n", "        features_df = sub_df[feature_columns]\n", "        features_df = features_df.reindex(sorted(features_df.columns), axis=1)\n", "        targets = sub_df[\"TARGET_CLASSIFIED\"]\n", "        targets = targets.astype(int)\n", "\n", "        #normalize data\n", "        if is_apply_saved_scaler:\n", "            scaler = joblib.load(os.path.join(os.getcwd(), \"source_datasets\", f\"{dataset_name}_scaler.save\"))\n", "            features_df = pd.DataFrame(scaler.transform(features_df), index=features_df.index, columns=features_df.columns)\n", "        else:\n", "            scaler = StandardScaler()  \n", "            features_df = pd.DataFrame(scaler.fit_transform(features_df), index=features_df.index, columns=features_df.columns)\n", "\n", "        #collect dataframes of features of all stocks and concatenate in order to apply and store scaler to all data\n", "        if is_save_scaler:\n", "            if master_features_for_scaler is None:\n", "                master_features_for_scaler = features_df\n", "            else:\n", "                master_features_for_scaler = pd.concat([master_features_for_scaler, features_df], axis=0)\n", "\n", "        #ignore dataframe if number rows is not sufficient to form sequences\n", "        if len(features_df) < int(parameters.sequence_length):\n", "            print(f\"Skip Symbol ({stock_symbol}) because length of Dataframe is: {len(features_df)}\")\n", "            continue\n", "\n", "        #generate dataset\n", "        dataset = tf.keras.preprocessing.sequence.TimeseriesGenerator(\n", "                features_df,\n", "                targets, \n", "                length=parameters.sequence_length,\n", "                sampling_rate=1,\n", "                stride=1,\n", "                shuffle=False, #Always false here as sequences first need to get stacked \n", "                batch_size=10000000) #set to large value in order to concatenate numpy arrays later\n", "\n", "        #generate dataframe that contains all data for given symbol\n", "        single_df = pd.DataFrame({\n", "            f\"{stock_symbol}_RETURNS\": sub_df[\"TARGET_RAW\"][parameters.sequence_length:],\n", "            f\"{stock_symbol}_FEATURES\": list(dataset[0][0]),\n", "            f\"{stock_symbol}_TARGETS\": dataset[0][1]})\n", "\n", "        #concatanate dataframes\n", "        if master_df is None:\n", "            master_df = single_df\n", "        else:\n", "            master_df = pd.merge(master_df, single_df, on=\"Date\", how=\"outer\") \n", "\n", "        print(f\"\\nSymbol: {stock_symbol}\")\n", "        print(f\"Progress: {count}/{len(stock_symbols)}\")\n", "        print(f\"Feature Count:{len(features_df.columns)}\")\n", "        print(f\"Feature Row Size:{len(features_df)}\")\n", "        print(f\"Target Row Size:{len(targets)}\")\n", "\n", "    #scale and save scaler across all stocks\n", "    if is_save_scaler:\n", "        scaler = StandardScaler()  \n", "        scaler.fit(master_features_for_scaler)\n", "        joblib.dump(scaler, os.path.join(os.getcwd(), \"source_datasets\", f\"{dataset_name}_scaler.save\"))\n", "\n", "    #store numpy array on disk\n", "    print(\"Store dataframe on disk...\")\n", "    master_df.to_csv(os.path.join(os.getcwd(), \"source_datasets\", f\"{dataset_name}_dataframe.csv\"))\n", "    print(\"Done storing dataframe\")\n", "\n", "\n", "def generate_dataset_v3(dataset_name:str, scalar_name:str, parameters:ModelParameters, dt_start:str, dt_end:str, \n", "    stocks_to_include:list, learn_new_scaler:bool):\n", "\n", "    other_feature_columns = [\"VIX_OPEN\", \"VIX_HIGH\", \"VIX_LOW\", \"VIX_CLOSE\",\n", "                             \"EURODOLLAR_OPEN\", \"EURODOLLAR_HIGH\", \"EURODOLLAR_LOW\", \"EURODOLLAR_CLOSE\",\n", "                             \"5YEARBON<PERSON>_OP<PERSON>\", \"5YEARBOND_HIGH\", \"5YEARBOND_LOW\", \"5YEARBOND_CLOSE\", \n", "                             \"10YEARBON<PERSON>_OP<PERSON>\", \"10YEARBOND_HIGH\", \"10YEARBOND_LOW\", \"10YEARBOND_CLOSE\",\n", "                             \"30YEARBON<PERSON>_OP<PERSON>\", \"30YEARBOND_HIGH\", \"30YEARBOND_LOW\", \"30YEARBOND_CLOSE\", \n", "                             \"GOLD_OPEN\", \"GOLD_HIGH\", \"GOLD_LOW\", \"GOLD_CLOSE\",\n", "                             \"SPX500_OPEN\", \"SPX500_HIGH\", \"SPX500_LOW\", \"SPX500_CLOSE\", \n", "                             \"RUSSELL2000_OP<PERSON>\", \"RUSSELL2000_HIGH\", \"RUSSELL2000_LOW\", \"RUSSELL2000_CLOSE\", \n", "                             \"CRU<PERSON><PERSON><PERSON>_OPEN\", \"CRUDEOIL_HIGH\", \"CRU<PERSON><PERSON><PERSON>_LOW\", \"CRUDEOIL_CLOSE\",\n", "                             \"NATGAS_OPEN\", \"NATGAS_HIGH\", \"NATGAS_LOW\", \"NATGAS_CLOSE\"]\n", "\n", "    #load dataframe\n", "    path_filename = os.path.join(os.getcwd(), \"source_datasets\", \"raw_data.csv\")\n", "    print(path_filename)\n", "    \n", "    df = pd.read_csv(path_filename, header=0, index_col=0, parse_dates=True, dtype=np.float32)\n", "\n", "    #filter out unwanted stocks\n", "    stock_symbols = set([col.split(\"_\")[1] for col in df.columns if col.split(\"_\")[0] == \"S\"])\n", "    if len(stocks_to_include) > 0:\n", "        stock_symbols = [symbol for symbol in stock_symbols if symbol in stocks_to_include]\n", "        \n", "    stock_symbols = list(stock_symbols)\n", "    stock_symbols.sort()\n", "        \n", "    #scalers\n", "    if learn_new_scaler is True:\n", "        scalers = dict()    \n", "    else:\n", "        scalers = pickle.load(open(os.path.join(os.getcwd(), \"source_datasets\", f\"{scalar_name}_scalers.save\"), 'rb'))\n", "            \n", "    #iterate over all stock symbols\n", "    count = 0\n", "    master_features = []\n", "    master_targets = []\n", "    master_analytics = []\n", "    \n", "    for stock_symbol in stock_symbols:\n", "\n", "        #increment counter\n", "        count += 1\n", "\n", "        #obtain all feature columns\n", "        feature_columns = [f\"S_{stock_symbol}_OPEN\", f\"S_{stock_symbol}_HIGH\", f\"S_{stock_symbol}_LOW\", f\"S_{stock_symbol}_CLOSE\", f\"S_{stock_symbol}_VOLUME\"] + other_feature_columns\n", "              \n", "        #filter columns\n", "        sub_df = df[feature_columns]\n", "\n", "        #filter rows by start and end dates (taking into account sequence length)   \n", "        index_dt_start = sub_df.index.get_loc(dt_start, method=\"nearest\")\n", "        index_dt_end = sub_df.index.get_loc(dt_end, method=\"nearest\")   \n", "        sub_df = sub_df[max(0, index_dt_start - parameters.sequence_length) : min(len(sub_df), index_dt_end + 1)]\n", "\n", "        #remove rows with missing values\n", "        sub_df = sub_df.dropna()\n", "\n", "        #continue if there is no data\n", "        if len(sub_df) < int(parameters.sequence_length):\n", "            print(f\"Skip Symbol ({stock_symbol}) because length of Dataframe is: {len(sub_df)}\")\n", "            continue\n", "\n", "        #rename stock columns (done in order to sort columns later correctly)\n", "        sub_df = sub_df.rename(columns={\n", "            f\"S_{stock_symbol}_OPEN\": \"STOCK_OPEN\", \n", "            f\"S_{stock_symbol}_HIGH\": \"STOCK_HIGH\",\n", "            f\"S_{stock_symbol}_LOW\": \"STOCK_LOW\",\n", "            f\"S_{stock_symbol}_CLOSE\": \"STOCK_CLOSE\",\n", "            f\"S_{stock_symbol}_VOLUME\": \"STOCK_VOLUME\"})\n", "\n", "        #columns to convert to percentages\n", "        columns_to_convert_to_pct = sub_df.columns\n", "\n", "        #add next_days \n", "        column_names = set([col.split(\"_\")[0] for col in sub_df.columns])\n", "        for column_name in column_names:\n", "            sub_df[column_name + \"_OPENVSPREVCLOSE\"] = sub_df[column_name + \"_OPEN\"].shift(-1) / sub_df[column_name + \"_CLOSE\"] - 1\n", "\n", "        #capture feature columns\n", "        feature_columns = sub_df.columns\n", "\n", "        #generate TARGET series that only contains open-close returns (for inferencing purposes)\n", "        sub_df[\"TARGET_RAW\"] = (sub_df[\"STOCK_CLOSE\"] / sub_df[\"STOCK_OPEN\"] - 1)\n", "        # sub_df[\"TARGET_CLASSIFIED\"] = sub_df[\"TARGET_RAW\"].transform(lambda x: 1 if x >= 0 else 0)\n", "        sub_df[\"TARGET_CLASSIFIED\"] = sub_df[\"TARGET_RAW\"].transform(generate_targets)\n", "\n", "        #convert raw data to return data\n", "        sub_df[columns_to_convert_to_pct] = sub_df[columns_to_convert_to_pct].pct_change()\n", "\n", "        #cleanse data\n", "        sub_df = sub_df.dropna()\n", "\n", "        #extract features and targets\n", "        features_df = sub_df[feature_columns]\n", "        features_df = features_df.reindex(sorted(features_df.columns), axis=1)\n", "        targets = sub_df[\"TARGET_CLASSIFIED\"]\n", "        targets = targets.astype(int)\n", "\n", "        #normalize data\n", "        if learn_new_scaler:\n", "            scaler = StandardScaler()\n", "            features_df = pd.DataFrame(scaler.fit_transform(features_df), index=features_df.index, columns=features_df.columns)\n", "            scalers[stock_symbol] = scaler\n", "        else:\n", "            scaler = scalers[stock_symbol]\n", "            features_df = pd.DataFrame(scaler.transform(features_df), index=features_df.index, columns=features_df.columns)\n", "\n", "        #ignore dataframe if number rows is not sufficient to form sequences\n", "        if len(features_df) < int(parameters.sequence_length):\n", "            print(f\"Skip Symbol ({stock_symbol}) because length of Dataframe is: {len(features_df)}\")\n", "            continue\n", "\n", "        #generate and store numpy arrays\n", "        features_array = get_features(features_df, parameters.sequence_length)\n", "        target_array = get_targets(targets, parameters.sequence_length)\n", "        master_features.append(features_array)\n", "        master_targets.append(target_array)\n", "\n", "        #generate dataframe that contains all data for given symbol\n", "        analytics_df = pd.DataFrame({\n", "            \"SYMBOL\": stock_symbol,\n", "            \"RETURN\": sub_df[\"TARGET_RAW\"][parameters.sequence_length:],\n", "            \"TARGET\": targets[parameters.sequence_length:]})\n", "        master_analytics.append(analytics_df)\n", "\n", "    print(\"Stack and write arrays to disk...\")\n", "    features_total = np.vstack(master_features)\n", "    targets_total = np.concatenate(master_targets)\n", "    np.save(os.path.join(os.getcwd(), \"source_datasets\", f\"{dataset_name}_features\"), features_total)\n", "    np.save(os.path.join(os.getcwd(), \"source_datasets\", f\"{dataset_name}_targets\"), targets_total)\n", "\n", "    #save scalers \n", "    if learn_new_scaler:\n", "        print(\"Write scaler to disk\")\n", "        pickle.dump(scalers, open(os.path.join(os.getcwd(), \"source_datasets\", f\"{scalar_name}_scalers.save\"), 'wb'))\n", "\n", "    print(\"Stack analytics data and write to disk...\")\n", "    master_analytics = pd.concat(master_analytics, axis=0)\n", "    master_analytics.to_csv(os.path.join(os.getcwd(), \"source_datasets\", f\"{dataset_name}_analytics.csv\"))\n", "    \n", "    \n", "def generate_dataset_v4(dataset_name:str, scalar_name:str, parameters:ModelParameters, dt_start:str, dt_end:str, \n", "    stocks_to_include:list, learn_new_scaler:bool):\n", "\n", "    other_feature_columns = [\"VIX_OPEN\", \"VIX_HIGH\", \"VIX_LOW\", \"VIX_CLOSE\",\n", "                             \"EURODOLLAR_OPEN\", \"EURODOLLAR_HIGH\", \"EURODOLLAR_LOW\", \"EURODOLLAR_CLOSE\",\n", "                             \"5YEARBON<PERSON>_OP<PERSON>\", \"5YEARBOND_HIGH\", \"5YEARBOND_LOW\", \"5YEARBOND_CLOSE\", \n", "                             \"10YEARBON<PERSON>_OP<PERSON>\", \"10YEARBOND_HIGH\", \"10YEARBOND_LOW\", \"10YEARBOND_CLOSE\",\n", "                             \"30YEARBON<PERSON>_OP<PERSON>\", \"30YEARBOND_HIGH\", \"30YEARBOND_LOW\", \"30YEARBOND_CLOSE\", \n", "                             \"GOLD_OPEN\", \"GOLD_HIGH\", \"GOLD_LOW\", \"GOLD_CLOSE\",\n", "                             \"SPX500_OPEN\", \"SPX500_HIGH\", \"SPX500_LOW\", \"SPX500_CLOSE\", \n", "                             \"RUSSELL2000_OP<PERSON>\", \"RUSSELL2000_HIGH\", \"RUSSELL2000_LOW\", \"RUSSELL2000_CLOSE\", \n", "                             \"CRU<PERSON><PERSON><PERSON>_OPEN\", \"CRUDEOIL_HIGH\", \"CRU<PERSON><PERSON><PERSON>_LOW\", \"CRUDEOIL_CLOSE\",\n", "                             \"NATGAS_OPEN\", \"NATGAS_HIGH\", \"NATGAS_LOW\", \"NATGAS_CLOSE\"]\n", "\n", "    #load dataframe\n", "    path_filename = os.path.join(os.getcwd(), \"source_datasets\", \"raw_data.csv\")    \n", "    df = pd.read_csv(path_filename, header=0, index_col=0, parse_dates=True, dtype=np.float32)\n", "\n", "    #filter out unwanted stocks\n", "    stock_symbols = set([col.split(\"_\")[1] for col in df.columns if col.split(\"_\")[0] == \"S\"])\n", "    if len(stocks_to_include) > 0:\n", "        stock_symbols = [symbol for symbol in stock_symbols if symbol in stocks_to_include]\n", "        \n", "    stock_symbols = list(stock_symbols)\n", "    stock_symbols.sort()\n", "        \n", "    #scalers\n", "    if learn_new_scaler is True:\n", "        scalers = dict()    \n", "    else:\n", "        scalers = pickle.load(open(os.path.join(os.getcwd(), \"source_datasets\", f\"{scalar_name}_scalers.save\"), 'rb'))\n", "            \n", "    #iterate over all stock symbols\n", "    count = 0\n", "    master_features = []\n", "    master_targets = []\n", "    master_analytics = []\n", "    \n", "    for stock_symbol in stock_symbols:\n", "\n", "        #increment counter\n", "        count += 1\n", "\n", "        #obtain all feature columns\n", "        feature_columns = [f\"S_{stock_symbol}_OPEN\", f\"S_{stock_symbol}_HIGH\", f\"S_{stock_symbol}_LOW\", f\"S_{stock_symbol}_CLOSE\", f\"S_{stock_symbol}_VOLUME\"] + other_feature_columns\n", "              \n", "        #filter columns\n", "        sub_df = df[feature_columns]\n", "\n", "        #filter rows by start and end dates (taking into account sequence length)   \n", "        index_dt_start = sub_df.index.get_loc(dt_start, method=\"nearest\")\n", "        index_dt_end = sub_df.index.get_loc(dt_end, method=\"nearest\")   \n", "        sub_df = sub_df[max(0, index_dt_start - parameters.sequence_length) : min(len(sub_df), index_dt_end + 1)]\n", "\n", "        #remove rows with missing values\n", "        sub_df = sub_df.dropna()\n", "\n", "        #continue if there is no data\n", "        if len(sub_df) < int(parameters.sequence_length):\n", "            print(f\"Skip Symbol ({stock_symbol}) because length of Dataframe is: {len(sub_df)}\")\n", "            continue\n", "\n", "        #rename stock columns (done in order to sort columns later correctly)\n", "        sub_df = sub_df.rename(columns={\n", "            f\"S_{stock_symbol}_OPEN\": \"STOCK_OPEN\", \n", "            f\"S_{stock_symbol}_HIGH\": \"STOCK_HIGH\",\n", "            f\"S_{stock_symbol}_LOW\": \"STOCK_LOW\",\n", "            f\"S_{stock_symbol}_CLOSE\": \"STOCK_CLOSE\",\n", "            f\"S_{stock_symbol}_VOLUME\": \"STOCK_VOLUME\"})\n", "\n", "        #columns to convert to percentages\n", "        columns_to_convert_to_pct = sub_df.columns\n", "\n", "        #add next_days \n", "        column_names = set([col.split(\"_\")[0] for col in sub_df.columns])\n", "        for column_name in column_names:\n", "            sub_df[column_name + \"_OPENVSPREVCLOSE\"] = sub_df[column_name + \"_OPEN\"].shift(-1) / sub_df[column_name + \"_CLOSE\"] - 1\n", "\n", "        #capture feature columns\n", "        feature_columns = sub_df.columns\n", "\n", "        #generate TARGET series that only contains open-close returns (for inferencing purposes)        \n", "        sub_df[\"TARGET_RAW\"] = (sub_df[\"STOCK_CLOSE\"] / sub_df[\"STOCK_OPEN\"] - 1)\n", "        sub_df[\"TARGET_CLASSIFIED\"] = sub_df[\"TARGET_RAW\"].transform(lambda x: 1 if x >= 0 else 0)\n", "                \n", "        #convert raw data to return data\n", "        sub_df[columns_to_convert_to_pct] = sub_df[columns_to_convert_to_pct].pct_change()\n", "\n", "        #cleanse data\n", "        sub_df = sub_df.dropna()\n", "\n", "        #extract features and targets\n", "        features_df = sub_df[feature_columns]\n", "        features_df = features_df.reindex(sorted(features_df.columns), axis=1)\n", "        targets = sub_df[\"TARGET_CLASSIFIED\"]\n", "        targets = targets.astype(int)\n", "        \n", "        #normalize data\n", "        if learn_new_scaler:\n", "            scaler = RobustScaler()\n", "            features_df = pd.DataFrame(scaler.fit_transform(features_df), index=features_df.index, columns=features_df.columns)\n", "            scalers[stock_symbol] = scaler\n", "        else:\n", "            \n", "            if stock_symbol not in scalers:\n", "                continue\n", "            \n", "            scaler = scalers[stock_symbol]\n", "            features_df = pd.DataFrame(scaler.transform(features_df), index=features_df.index, columns=features_df.columns)\n", "\n", "        #ignore dataframe if number rows is not sufficient to form sequences\n", "        if len(features_df) < int(parameters.sequence_length):\n", "            print(f\"Skip Symbol ({stock_symbol}) because length of Dataframe is: {len(features_df)}\")\n", "            continue\n", "\n", "        #generate and store numpy arrays\n", "        features_array = get_features(features_df, parameters.sequence_length)\n", "        target_array = get_targets(targets, parameters.sequence_length)\n", "        master_features.append(features_array)\n", "        master_targets.append(target_array)\n", "\n", "        #generate dataframe that contains all data for given symbol\n", "        analytics_df = pd.DataFrame({\n", "            \"SYMBOL\": stock_symbol,\n", "            \"RETURN\": sub_df[\"TARGET_RAW\"][parameters.sequence_length:],\n", "            \"TARGET\": targets[parameters.sequence_length:]})\n", "        master_analytics.append(analytics_df)\n", "        \n", "    print(\"Stack and write arrays to disk...\")\n", "    features_total = np.vstack(master_features)\n", "    targets_total = np.concatenate(master_targets)\n", "    np.save(os.path.join(os.getcwd(), \"source_datasets\", f\"{dataset_name}_features\"), features_total)\n", "    np.save(os.path.join(os.getcwd(), \"source_datasets\", f\"{dataset_name}_targets\"), targets_total)\n", "\n", "    #save scalers \n", "    if learn_new_scaler:\n", "        print(\"Write scaler to disk\")\n", "        pickle.dump(scalers, open(os.path.join(os.getcwd(), \"source_datasets\", f\"{scalar_name}_scalers.save\"), 'wb'))\n", "\n", "    print(\"Stack analytics data and write to disk...\")\n", "    master_analytics = pd.concat(master_analytics, axis=0)\n", "    master_analytics.to_csv(os.path.join(os.getcwd(), \"source_datasets\", f\"{dataset_name}_analytics.csv\"))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Deep Neural Network"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_angles(pos, i, d_model, sequence_length):\n", "  angle_rates = 1 / np.power(sequence_length, (2 * (i//2)) / np.float32(d_model))\n", "  # angle_rates = 1 / np.power(10000, (2 * (i//2)) / np.float32(d_model))\n", "  return pos * angle_rates\n", "\n", "\n", "def positional_encoding(sequence_length, d_model):\n", "\n", "  angle_rads = get_angles(np.arange(sequence_length)[:, np.newaxis], np.arange(d_model)[np.newaxis, :], d_model, sequence_length)\n", "  \n", "  # apply sin to even indices in the array; 2i\n", "  angle_rads[:, 0::2] = np.sin(angle_rads[:, 0::2])\n", "  \n", "  # apply cos to odd indices in the array; 2i+1\n", "  angle_rads[:, 1::2] = np.cos(angle_rads[:, 1::2])\n", "    \n", "  pos_encoding = angle_rads[np.newaxis, ...]\n", "    \n", "  return tf.cast(pos_encoding, dtype=tf.float32)\n", "\n", "\n", "class EncoderLayer(tf.keras.layers.Layer):\n", "  \n", "    def __init__(self, d_model, num_heads, ff_dim, dropout_rate):\n", "        super(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "\n", "        self.d_model = d_model\n", "        self.num_heads = num_heads\n", "        self.ff_dim = ff_dim\n", "        self.dropout_rate = dropout_rate\n", "      \n", "    def build(self, input_shape):\n", "\n", "        self.sequence_length = input_shape[1]\n", "        self.positional_encoding = positional_encoding(self.sequence_length, self.d_model)\n", "        \n", "        #normalization and attention\n", "        self.layernorm1 = layers.LayerNormalization(epsilon=1e-6)\n", "        self.multihead_attention = layers.MultiHeadAttention(key_dim=self.d_model, num_heads=self.num_heads, dropout=self.dropout_rate)\n", "        self.dropout1 = layers.Dropout(self.dropout_rate)\n", "              \n", "        #feed forward part\n", "        self.layernorm2 = layers.LayerNormalization(epsilon=1e-6)\n", "        self.conv1 = layers.Conv1D(filters=self.ff_dim, kernel_size=1, activation=\"relu\")\n", "        self.dropout2 = layers.Dropout(self.dropout_rate)\n", "        self.conv2 = layers.Conv1D(filters=input_shape[-1], kernel_size=1)\n", "\n", "    def call(self, inputs, training):\n", "      \n", "        #experimental (position encoding in each layer vs original paper, only in input layer)\n", "        x = inputs + self.positional_encoding[:, :self.sequence_length, :]\n", "        # x = inputs\n", "\n", "        #normalization and attention\n", "        x = self.layernorm1(x)\n", "        x = self.multihead_attention(x, x)\n", "        x = self.dropout1(x, training=training)\n", "\n", "        res = x + inputs\n", "\n", "        #feed forward part\n", "        x = self.layernorm2(res)\n", "        x = self.conv1(x)\n", "        x = self.dropout2(x, training=training)\n", "        x = self.conv2(x)\n", "        \n", "        return x + res\n", "        \n", "      \n", "class Encoder(tf.keras.layers.Layer):\n", "  \n", "    def __init__(self, num_layers, d_model, num_heads, ff_dim, dense_units, dropout_rate):\n", "        super(Encoder, self).__init__()\n", "\n", "        self.num_layers = num_layers\n", "        self.d_model = d_model\n", "        self.num_heads = num_heads\n", "        self.ff_dim = ff_dim\n", "        self.dense_units = dense_units\n", "        self.dropout_rate = dropout_rate        \n", "\n", "    def build(self, input_shape):\n", "\n", "        self.encoder_layers = [EncoderLayer(self.d_model, self.num_heads, self.ff_dim, self.dropout_rate) for _ in range(self.num_layers)]\n", "        self.pooling_layer = layers.GlobalMaxPooling1D(data_format=\"channels_first\")\n", "        self.dense_layer = layers.Dense(units=self.dense_units, activation=\"relu\")\n", "        self.dropout_layer = layers.Dropout(rate=self.dropout_rate)\n", "\n", "    def call(self, inputs, training):\n", "      \n", "        x = inputs\n", "\n", "        for encoder_layer in self.encoder_layers:\n", "            x = encoder_layer(x, training=training)\n", "\n", "        x = self.pooling_layer(x)\n", "        x = self.dense_layer(x)\n", "        x = self.dropout_layer(x, training=training)\n", "        \n", "        return x\n", "\n", "\n", "class Transformer(tf.keras.Model):\n", "\n", "      def __init__(self, output_channels, num_layers, d_model, num_heads, ff_dim, dense_units, dropout_rate):\n", "          super(Transformer, self).__init__()\n", "\n", "          self.encoder = Encoder(num_layers, d_model, num_heads, ff_dim, dense_units, dropout_rate)\n", "          self.output_layer = layers.Dense(units=output_channels, activation=\"softmax\")\n", "      \n", "      def call(self, inputs, training):\n", "\n", "          x = self.encoder(inputs=inputs, training=training)\n", "          x = self.output_layer(x)\n", "\n", "          return x\n", "\n", "\n", "class CustomLearningRateSchedule(keras.optimizers.schedules.LearningRateSchedule):\n", "  \n", "      def __init__(self, d_model, warmup_steps=4000):\n", "          super(CustomLearningRateSchedule, self).__init__()\n", "          \n", "          self.d_model = d_model\n", "          self.d_model = tf.cast(self.d_model, tf.float32)\n", "          self.warmup_steps = warmup_steps\n", "          \n", "      def __call__(self, step):\n", "\n", "          arg1 = tf.math.rsqrt(step)\n", "          arg2 = step * (self.warmup_steps ** -1.5)\n", "          return tf.math.rsqrt(self.d_model) * tf.math.minimum(arg1, arg2)\n", "\n", "\n", "def create_transformer_model(parameters:ModelParameters):\n", "    \n", "    input_shape = (parameters.batch_size, parameters.sequence_length, parameters.d_model)   \n", "    custom_learning_rate = CustomLearningRateSchedule(parameters.d_model, parameters.learning_rate_warmup_steps)\n", "    optimizer = keras.optimizers.<PERSON>(custom_learning_rate, beta_1=0.9, beta_2=0.98, epsilon=1e-9) #\"Adam\"\n", "    optimizer = tf.keras.mixed_precision.LossScaleOptimizer(optimizer) #enables Automatic Mixed Precision (AMP)\n", "    loss = tf.keras.losses.SparseCategoricalCrossentropy(from_logits=False, reduction='none')\n", "    metrics = [\"sparse_categorical_accuracy\"]\n", "\n", "    model = Transformer(output_channels=parameters.number_output_channels, num_layers=parameters.number_layers, d_model=parameters.d_model, \n", "                        num_heads=parameters.number_heads, ff_dim=parameters.ff_dim, dense_units=parameters.dense_units, dropout_rate=parameters.dropout_rate)\n", "\n", "    model.compile(loss=loss, optimizer=optimizer, metrics=metrics)\n", "    model.build(input_shape)\n", "    model.summary()\n", "\n", "    return model"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Training"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def training(parameters:ModelParameters, model_id:str, train_data_id:str, validation_data_id:str, is_single_gpu:bool):\n", "\n", "    #obtain training data\n", "    print(\"loading training data...\")\n", "    train_data = np.load(os.path.join(os.getcwd(), \"source_datasets\", f\"{train_data_id}_features.npy\"))\n", "    train_targets = np.load(os.path.join(os.getcwd(), \"source_datasets\", f\"{train_data_id}_targets.npy\"))    \n", "\n", "    #use generator to create dataset\n", "    print(\"training dataset construction...\")\n", "    def generator():\n", "        for i,j in zip(train_data, train_targets):\n", "        # for i,j in zip(feature_chunks, target_chunks):\n", "            yield i, j\n", "\n", "    output_signature = (tf.TensorSpec(shape=(parameters.sequence_length, parameters.d_model), dtype=tf.float32), tf.TensorSpec(shape=(), dtype=tf.int16))\n", "    train_ds = tf.data.Dataset.from_generator(generator, output_signature=output_signature)  # output_types=(tf.float32, tf.int16), output_shapes=(train_data.shape, train_targets.shape)\n", "    # train_ds = tf.data.Dataset.from_tensor_slices((feature_chunks[0], target_chunks[0]))\n", "\n", "    print(\"shuffle training data...\")\n", "    train_ds = train_ds.shuffle(buffer_size=len(train_data))\n", "\n", "    print(\"batch training data...\")\n", "    train_ds = train_ds.batch(parameters.batch_size)\n", "\n", "    print(\"cache and prefetch training data...\")\n", "    train_ds = train_ds.cache()\n", "    train_ds = train_ds.prefetch(tf.data.AUTOTUNE)\n", "\n", "    callbacks = list()\n", "    checkpoint_path = os.path.join(os.getcwd(), \"model\", f\"{model_id}_bestweights.hdf5\")\n", "    \n", "    if len(validation_data_id) == 0:\n", "\n", "        #best weights checkpoint callback\n", "        checkpoint = keras.callbacks.ModelCheckpoint(checkpoint_path, monitor='sparse_categorical_accuracy', verbose=1, save_best_only=True, mode=\"max\", save_weights_only=True)\n", "        callbacks.append(checkpoint)\n", "\n", "    else:\n", "\n", "        print(\"loading validation data...\")\n", "        validation_data = np.load(os.path.join(os.getcwd(), \"source_datasets\", f\"{validation_data_id}_features.npy\"))\n", "        validation_targets = np.load(os.path.join(os.getcwd(), \"source_datasets\", f\"{validation_data_id}_targets.npy\"))\n", "\n", "        print(\"validation dataset construction...\")\n", "        validation_ds = tf.data.Dataset.from_tensor_slices((validation_data, validation_targets))\n", "\n", "        print(\"batch validation data...\")\n", "        validation_ds = validation_ds.batch(parameters.batch_size)\n", "\n", "        print(\"cache and prefetch validation data...\")\n", "        validation_ds = validation_ds.cache()\n", "        validation_ds = validation_ds.prefetch(tf.data.AUTOTUNE)\n", "\n", "        #best weights checkpoint callback\n", "        checkpoint = keras.callbacks.ModelCheckpoint(checkpoint_path, monitor='val_sparse_categorical_accuracy', verbose=1, save_best_only=True, mode=\"max\", save_weights_only=True)\n", "        callbacks.append(checkpoint)\n", "\n", "    #create and compile and build model\n", "    print(\"create and compile model...\")\n", "    if is_single_gpu is True:\n", "        model = create_transformer_model(parameters)\n", "    else:\n", "        strategy = tf.distribute.MirroredStrategy(cross_device_ops=tf.distribute.HierarchicalCopyAllReduce())\n", "        print(\"Number of devices: {}\".format(strategy.num_replicas_in_sync))\n", "\n", "        with strategy.scope():\n", "            model = create_transformer_model(parameters)\n", "            \n", "    #train the model\n", "    print(\"train the model...\")\n", "\n", "    if len(validation_data_id) > 0:\n", "        history = model.fit(train_ds, epochs=parameters.epochs, validation_data=validation_ds, callbacks=callbacks) \n", "    else:\n", "        history = model.fit(train_ds, epochs=parameters.epochs, callbacks=callbacks) \n", "\n", "    #save last weights\n", "    model_weights_path = os.path.join(os.getcwd(), \"model\", f\"{model_id}_lastweights.hdf5\")\n", "    model.save_weights(model_weights_path, overwrite=True, save_format=\"h5\")\n", "    \n", "    #visualize training and validation accuracy over epochs\n", "    accuracy = history.history['sparse_categorical_accuracy']\n", "    epochs = range(1, len(accuracy) + 1)\n", "    plt.plot(epochs, accuracy, 'g', label='Training Accuracy')\n", "    \n", "    if len(validation_data_id) > 0:\n", "        val_accuracy = history.history['val_sparse_categorical_accuracy']\n", "        plt.plot(epochs, val_accuracy, 'b', label='Validation Accuracy')\n", "    \n", "    plt.title('Training and Validation Accuracy')\n", "    plt.legend()\n", "    plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Inferencing"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_bus_day(dt:datetime, include_current_dt:bool, number_bus_days:int):\n", "    \n", "    new_dt = dt + pd.tseries.offsets.BDay(number_bus_days)\n", "    \n", "    if include_current_dt:\n", "\n", "        if bool(len(pd.bdate_range(dt, dt))):\n", "            #current dt is a business day\n", "            adjustement = 0\n", "            if number_bus_days > 0:\n", "                adjustment = -1\n", "            elif number_bus_days < 0:\n", "                adjustment = 1\n", "            \n", "            new_dt = new_dt + pd.tseries.offsets.BDay(adjustment)\n", "\n", "    return new_dt\n", "\n", "\n", "def analytics_cycle(analytics_df:pd.DataFrame, previous_single_df:pd.DataFrame, previous_portfolio_df:pd.DataFrame, \n", "    symbols_to_include:list, datetime_from:str, datetime_to:str, notes:str,\n", "    output_visuals:bool, store_single_stock_results:bool, store_portfolio_results:bool):\n", "    \n", "    #filter dataframe by dates and symbols\n", "    print(\"Filter by Start/End Dates and Symbols...\")\n", "    analytics_df = analytics_df.reset_index()\n", "    if len(symbols_to_include) == 0:\n", "        analytics_df = analytics_df[(analytics_df['Date'] >= datetime_from) & (analytics_df[\"Date\"] <= datetime_to)]\n", "    else:\n", "        analytics_df = analytics_df[(analytics_df['SYMBOL'].isin(symbols_to_include)) & \n", "            (analytics_df['Date'] >= datetime_from) & (analytics_df[\"Date\"] <= datetime_to)]\n", "    \n", "    #generate statistics\n", "    print(\"Generate Statistics...\")\n", "    analytics_df[\"ACCURACY\"] = np.where(analytics_df[\"TARGET\"] == analytics_df[\"PREDICTED_CLASSIFIER\"], 1, 0)\n", "    analytics_df[\"LONGSHORT_TARGET\"] = np.where(analytics_df[\"TARGET\"] == 1, 1, -1)\n", "    analytics_df[\"LONGSHORT_PREDICTED\"] = np.where(analytics_df[\"PREDICTED_CLASSIFIER\"] == 1, 1, -1)\n", "    analytics_df[\"PNL\"] = np.where(analytics_df[\"PREDICTED_CLASSIFIER\"] == 1, analytics_df[\"RETURN\"], -analytics_df[\"RETURN\"])\n", "    \n", "    #this goes long in every symbol of the same day if the netlong position of that day is >400 and short if <-400 \n", "    # grouped_by_date_predicted = analytics_df.groupby(by=analytics_df[\"Date\"])[\"LONGSHORT_PREDICTED\"].sum()\n", "    # analytics_df = analytics_df.join(grouped_by_date_predicted, on=analytics_df[\"Date\"], rsuffix=\"_NETLONGSHORT\")\n", "    # analytics_df = analytics_df.rename(columns={\"LONGSHORT_PREDICTED_NETLONGSHORT\":\"NETLONGSHORT\"})\n", "    # analytics_df[\"PNL\"] = 0.0\n", "    # analytics_df[\"PNL\"] = analytics_df[\"PNL\"].mask(analytics_df[\"NETLONGSHORT\"] >= 400, analytics_df[\"RETURN\"]).mask(analytics_df[\"NETLONGSHORT\"] <= -400, -analytics_df[\"RETURN\"])\n", "     \n", "    #Single Stock Analysis\n", "    print(\"Single Stock Analysis\")\n", "    grouped_by_stocks = analytics_df.groupby(by=analytics_df[\"SYMBOL\"])\n", "    duration = [td.days for td in (grouped_by_stocks[\"Date\"].max() - grouped_by_stocks[\"Date\"].min())]\n", "       \n", "    single_stocks_df = pd.DataFrame({\n", "        \"DATETIME_FROM\": grouped_by_stocks[\"Date\"].min(),\n", "        \"DATETIME_TO\": grouped_by_stocks[\"Date\"].max(),\n", "        \"CALENDAR_DAYS\": duration,\n", "        \"TRADING_DAYS\": grouped_by_stocks[\"PNL\"].count(),\n", "        \"TOTAL_PNL\": [str(value) + \"%\" for value in round(100 * grouped_by_stocks[\"PNL\"].sum(), 2)],\n", "        \"ANNUALIZED_PNL\": [str(value) + \"%\" for value in round(100 * grouped_by_stocks[\"PNL\"].sum() * 365 / duration, 2)], \n", "        \"SHARPE_RATIO\": round(grouped_by_stocks[\"PNL\"].mean() / grouped_by_stocks[\"PNL\"].std() * np.sqrt(252), 2),\n", "        \"AVG_DAILY_PNL\": [str(value) + \"%\" for value in round(100 * grouped_by_stocks[\"PNL\"].mean(), 4)],\n", "        \"STD_DAILY_PNL\": [str(value) + \"%\" for value in round(100 * grouped_by_stocks[\"PNL\"].std(), 4)],\n", "        \"MAX_DAILY_GAIN\": [str(value) + \"%\" for value in round(100 * grouped_by_stocks[\"PNL\"].max(), 4)],\n", "        \"MAX_DAILY_LOSS\": [str(value) + \"%\" for value in round(100 * grouped_by_stocks[\"PNL\"].min(), 4)],\n", "        \"SKEW_DAILY_PNL\": [round(scipy.stats.skew(v[\"PNL\"]), 4) for k,v in grouped_by_stocks],\n", "        \"KURTOSIS_DAILY_PNL\": [round(scipy.stats.kurtosis(v[\"PNL\"]), 4) for k,v in grouped_by_stocks]})\n", "    \n", "    #save dataframe\n", "    if store_single_stock_results:\n", "        single_stocks_df.to_csv(os.path.join(os.getcwd(), \"results\", \"single_stock_analysis.csv\"))    \n", "\n", "    #Portfolio Analysis\n", "    print(\"Portfolio Analysis...\")\n", "\n", "    if \"portfolio_analysis.csv\" in os.listdir(os.path.join(os.getcwd(), \"results\")):\n", "        portfolio_df = pd.read_csv(os.path.join(os.getcwd(), \"results\", \"portfolio_analysis.csv\"), header=0, index_col=[0])\n", "    else:\n", "        portfolio_df = pd.DataFrame(columns = [\"DATETIME_FROM\", \"DATETIME_TO\", \"CA<PERSON><PERSON>AR_DAYS\", \"TRADING_DAYS\", \"AVG_TRADES_PER_DAY\", \"MIN_TRADES_PER_DAY\", \"MAX_TRADES_PER_DAY\", \"TOTAL_PNL\", \"ANNUALIZED_PNL\", \n", "            \"SHARPE_RATIO\", \"AVG_DAILY_PNL\", \"STD_DAILY_PNL\", \"MAX_DAILY_GAIN\", \"MAX_DAILY_LOSS\", \"SKEW_DAILY_PNL\", \"KURTOSIS_DAILY_PNL\", \"NOTES\"])\n", "\n", "    analytics_df = analytics_df.set_index(\"Date\")\n", "    grouped_by_days = analytics_df.groupby(by=analytics_df.index)\n", "    pnl_grouped_by_days = grouped_by_days[\"PNL\"]\n", "    duration = (analytics_df.index.max() - analytics_df.index.min()).days\n", "        \n", "    if len(grouped_by_days) == 0:\n", "        print(f\"Datetime From/To did not produce any Portfolio Data: {datetime_from} - {datetime_to}\")\n", "        return single_stocks_df, pd.DataFrame()\n", "\n", "    portfolio_df = portfolio_df.append({\n", "        \"DATETIME_FROM\": analytics_df.index.min(),\n", "        \"DATETIME_TO\": analytics_df.index.max(), \n", "        \"CALENDAR_DAYS\": duration, \n", "        \"TRADING_DAYS\": len(pnl_grouped_by_days), \n", "        \"AVG_TRADES_PER_DAY\": round(np.mean(pnl_grouped_by_days.count()), 0), \n", "        \"MIN_TRADES_PER_DAY\": min(pnl_grouped_by_days.count()), \n", "        \"MAX_TRADES_PER_DAY\": max(pnl_grouped_by_days.count()), \n", "        \"TOTAL_PNL\": str(round(100 * pnl_grouped_by_days.mean().sum(), 2)) + \"%\", \n", "        \"ANNUALIZED_PNL\": str(round(100 * (pnl_grouped_by_days.mean().sum() * (365 / duration)), 2)) + \"%\", \n", "        \"SHARPE_RATIO\": round(pnl_grouped_by_days.mean().mean() / pnl_grouped_by_days.mean().std() * np.sqrt(252), 2), \n", "        \"AVG_DAILY_PNL\": str(round(100 * pnl_grouped_by_days.mean().mean(), 4)) + \"%\", \n", "        \"STD_DAILY_PNL\": str(round(100* pnl_grouped_by_days.mean().std(), 4)) + \"%\", \n", "        \"MAX_DAILY_GAIN\": str(round(100 * pnl_grouped_by_days.mean().max(), 4)) + \"%\", \n", "        \"MAX_DAILY_LOSS\": str(round(100 * pnl_grouped_by_days.mean().min(), 4)) + \"%\", \n", "        \"SKEW_DAILY_PNL\": round(scipy.stats.skew(pnl_grouped_by_days.mean()), 4), \n", "        \"KURTOSIS_DAILY_PNL\": round(scipy.stats.kurtosis(pnl_grouped_by_days.mean()), 4),\n", "        \"NOTES\": notes}, ignore_index=True)\n", "\n", "    #save dataframe\n", "    if store_portfolio_results:\n", "        portfolio_df.to_csv(os.path.join(os.getcwd(), \"results\", \"portfolio_analysis.csv\"))\n", "\n", "    if output_visuals:\n", "        \n", "        #visualizes net long/shorts per day over time\n", "        net_longshort_target_data = grouped_by_days[\"LONGSHORT_TARGET\"].sum()\n", "        net_longshort_predicted_data = grouped_by_days[\"LONGSHORT_PREDICTED\"].sum()\n", "        timestamps = net_longshort_target_data.keys()\n", "        fig = plt.figure(figsize=(11.7, 8.27))\n", "        sns.lineplot(x=timestamps, y=net_longshort_target_data)    \n", "        sns.lineplot(x=timestamps, y=net_longshort_predicted_data)\n", "        plt.title(\"Net Long/Shorts Target vs Predicted\")\n", "        fig.legend(labels=[\"Target Data\", \"Predicted Data\"])\n", "        plt.show()\n", "               \n", "        fig = plt.figure(figsize=(11.7, 8.27))\n", "        sns.scatterplot(x=net_longshort_target_data, y=net_longshort_predicted_data)\n", "        plt.title(\"Net Long/Shorts Target vs Predicted\")\n", "        plt.show()\n", "\n", "        print(\"Correlation: \", scipy.stats.pearsonr(net_longshort_target_data, net_longshort_predicted_data))\n", "\n", "        #visualize accuracies over time\n", "        chart_data = grouped_by_days[\"ACCURACY\"].mean()\n", "        rolling = chart_data.rolling(10).mean()\n", "        timestamps = rolling.keys()\n", "        values = rolling\n", "        fig, ax = plt.subplots(figsize=(11.7, 8.27))\n", "        sns.lineplot(x=timestamps, y=values, ax=ax)    \n", "\n", "        #output equity curve and return distribution\n", "        x = 0\n", "        chart_data = grouped_by_days[\"PNL\"].mean()\n", "        timestamps = chart_data.keys()\n", "        cumulative_returns = [x := x + t for t in chart_data]\n", "        fig, ax = plt.subplots(figsize=(11.7, 8.27))\n", "        sns.lineplot(x=timestamps, y=cumulative_returns, ax=ax)\n", "        sns.displot(chart_data,  binwidth=0.001, height=8.27, aspect=11.7/8.27)\n", "\n", "    #return single_stock dataframe and portfolio dataframe\n", "    return single_stocks_df, portfolio_df\n", "\n", "\n", "def run_analytics(dataset_name:str, model_weights_filename:str, notes:str, parameters:ModelParameters, symbols_to_include:list, \n", "    datetime_from:str, datetime_to:str, split_business_days:int, top_symbol_count:int):\n", "       \n", "    #load analytics dataframe\n", "    print(\"Load Analytics Dataframe...\")\n", "    analytics_df = pd.read_csv(os.path.join(os.getcwd(), \"source_datasets\", f\"{dataset_name}_analytics.csv\"), header=0, index_col=0, parse_dates=True)\n", "    \n", "    #generate predictions if not yet present\n", "    if \"PREDICTED_CLASSIFIER\" not in analytics_df.columns:\n", "\n", "        print(\"\\nGenerate Predictions and Store in Analytics Dataframe\\n\")\n", "\n", "        #create model\n", "        print(\"Create Model\")\n", "        model = create_transformer_model(parameters)\n", "        \n", "        #load weights\n", "        print(\"Load weights\")\n", "        weights_path_file_name = os.path.join(os.getcwd(), \"model\", f\"{model_weights_filename}.hdf5\")\n", "        model.load_weights(weights_path_file_name)\n", "\n", "        #obtain numpy arrays\n", "        print(\"Loading numpy arrays...\")\n", "        features = np.load(os.path.join(os.getcwd(), \"source_datasets\", f\"{dataset_name}_features.npy\"))\n", "        targets = np.load(os.path.join(os.getcwd(), \"source_datasets\", f\"{dataset_name}_targets.npy\"))\n", "\n", "        #split source data in half and process each half      \n", "        split_row_index = int(len(analytics_df) / 2)\n", "\n", "        #first half prediction\n", "        print(\"\\nFirst Half Inferencing\")\n", "\n", "        #obtain split numpy arrays\n", "        sub_features = features[0:split_row_index]\n", "        sub_targets = targets[0:split_row_index]\n", "\n", "        #convert to dataset\n", "        print(\"Dataset Construction...\")\n", "        dataset = tf.data.Dataset.from_tensor_slices((sub_features, sub_targets))\n", "        \n", "        #batch data\n", "        print(\"Batch Data...\")\n", "        dataset = dataset.batch(parameters.batch_size)\n", "                    \n", "        #evaluate model\n", "        print(\"Evaluate Model Accuracy\")\n", "        loss, accuracy = model.evaluate(dataset)\n", "        print(\"Model Accuracy on 1st dataset: {:5.2f}%\".format(100 * accuracy))\n", "\n", "        #inference/predict\n", "        print(\"Predict/Inference Model on Data\")\n", "        predictions = list(np.argmax(model.predict(dataset), axis=1))\n", "        analytics_df[\"PREDICTED_CLASSIFIER\"] = -1\n", "        analytics_df[\"PREDICTED_CLASSIFIER\"][0:split_row_index] = predictions\n", "\n", "        #second half prediction\n", "        print(\"\\nSecond Half Inferencing\")\n", "\n", "        #obtain split numpy arrays\n", "        sub_features = features[split_row_index:]\n", "        sub_targets = targets[split_row_index:]\n", "\n", "        #convert to dataset\n", "        print(\"Dataset Construction...\")\n", "        dataset = tf.data.Dataset.from_tensor_slices((sub_features, sub_targets))\n", "        \n", "        #batch data\n", "        print(\"Batch Data...\")\n", "        dataset = dataset.batch(parameters.batch_size)\n", "                    \n", "        #evaluate model\n", "        print(\"Evaluate Model Accuracy\")\n", "        loss, accuracy = model.evaluate(dataset)\n", "        print(\"Model Accuracy on 2nd dataset: {:5.2f}%\".format(100 * accuracy))\n", "\n", "        #inference/predict\n", "        print(\"Predict/Inference Model on Data\")\n", "        predictions = list(np.argmax(model.predict(dataset), axis=1))\n", "        analytics_df[\"PREDICTED_CLASSIFIER\"][split_row_index:] = predictions\n", "\n", "        #write updated analytics dataframe to disk\n", "        analytics_df.to_csv(os.path.join(os.getcwd(), \"source_datasets\", f\"{dataset_name}_analytics.csv\"))\n", "\n", "    return\n", "\n", "    #split timespan into subsets and iterate over each timespan\n", "    dt_start = pd.to_datetime(datetime_from)\n", "    dt_end = pd.to_datetime(datetime_to)\n", "    current_dt = dt_start\n", "     \n", "    #find first symbol subsection on previous data\n", "    first_dt_to = get_bus_day(current_dt, False, -1)\n", "    first_dt_from = get_bus_day(first_dt_to, True, -split_business_days)\n", "\n", "    previous_single_df, previous_portfolio_df = analytics_cycle(analytics_df, None, None, \n", "        symbols_to_include, first_dt_from, first_dt_to, notes,\n", "        output_visuals=False, store_single_stock_results=False, store_portfolio_results=False)\n", "\n", "    symbol_sub_selection = previous_single_df.nlargest(top_symbol_count, \"SHARPE_RATIO\").index.tolist()    \n", "\n", "    #iteration count\n", "    iteration_count = 0\n", "    \n", "    #iterate over sub-timespans\n", "    while current_dt < dt_end:\n", "\n", "        iteration_count += 1\n", "\n", "        #determine end date\n", "        sub_start = current_dt\n", "        sub_end = min(get_bus_day(current_dt, True, split_business_days), dt_end)\n", "\n", "        print(f\"\\nAnalyze Sub-Period Date Start: {sub_start} - Date End: {sub_end}\")\n", "        print(f\"Symbol Sub Selection: {len(symbol_sub_selection)}\")\n", "\n", "        #run analytics on sub timespan and sub symbol selection\n", "        single_df, portfolio_df = analytics_cycle(analytics_df, previous_single_df, previous_portfolio_df, \n", "            symbol_sub_selection, sub_start, sub_end, notes,\n", "            output_visuals=False, store_single_stock_results=False, store_portfolio_results=True)\n", "\n", "        #select subset of symbols\n", "        previous_single_df, previous_portfolio_df = analytics_cycle(analytics_df, None, None, \n", "            symbols_to_include, sub_start, sub_end, notes, \n", "            output_visuals=False, store_single_stock_results=False, store_portfolio_results=False)\n", "        \n", "        symbol_sub_selection = single_df.nlargest(top_symbol_count, \"SHARPE_RATIO\").index.tolist()\n", "        # symbol_sub_selection = single_df[single_df[\"SHARPE_RATIO\"] > 5].index.tolist()\n", "        # pickle.dump(symbol_sub_section, open(os.path.join(os.getcwd(), \"results\", \"symbol_filter.pkl\"), \"wb\"))\n", "\n", "        #increment current datetime\n", "        current_dt = sub_end + np.timedelta64(1, \"D\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Code Execution"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#model hypter parameters\n", "parameters = ModelParameters(\n", "    sequence_length= 200, #20\n", "    number_output_channels = 2, #2\n", "    label_shift = 1,\n", "    is_shuffle = True,\n", "    batch_size = 128, #8192/16384 (500 stock large transformer model)\n", "    epochs = 300, #300 (500 stock large transformer model)\n", "    learning_rate_warmup_steps = 4000, #4000\n", "    number_layers = 8, #8\n", "    number_heads = 4, #4\n", "    d_model = 56, #56 number features => needs to be updated after obtaining data\n", "    ff_dim = 512, #512\n", "    dense_units = 128, #128\n", "    dropout_rate = 0.5) #0.3\n", "\n", "# get datasets\n", "# start = time.time()\n", "# generate_dataset_v4(\n", "#     dataset_name=\"training_data\", \n", "#     scalar_name = \"training_data\",\n", "#     parameters=parameters, \n", "#     dt_start=\"2017-01-01\",\n", "#     dt_end=\"2018-12-31\",\n", "#     stocks_to_include=[], #pickle.load(open(os.path.join(os.getcwd(), \"results\", \"symbol_filter.pkl\"), \"rb\"))\n", "#     learn_new_scaler = True)\n", "# end = time.time()\n", "# print(\"Time spent building dataset (seconds): \", end-start)\n", "\n", "#train model\n", "training(\n", "    parameters=parameters,\n", "    model_id=\"model_200elements\",\n", "    train_data_id=\"training_data\",\n", "    validation_data_id=\"\",\n", "    is_single_gpu=True)\n", "\n", "#run inerence/analytics\n", "# run_analytics(\n", "#     dataset_name=\"test_data\",\n", "#     model_weights_filename=\"seven_categories_2_bestweights\",\n", "#     notes=\"\",\n", "#     parameters=parameters,\n", "#     symbols_to_include=[], #pickle.load(open(os.path.join(os.getcwd(), \"results\", \"symbol_filter.pkl\"), \"rb\"))\n", "#     datetime_from=\"2019-01-01\",\n", "#     datetime_to=\"2019-12-31\",\n", "#     split_business_days=60, #determines whether the dataset is analyzed as one or split into smaller timespans\n", "#     top_symbol_count=505) #determines how many symbols to trade (top picks by sharpe ratio of previous timespan)\n", "    "]}], "metadata": {"interpreter": {"hash": "2df95ccf3dd4d8d55e8628a7094851bf6700911f731acd25ee9d5219fc123dda"}, "kernelspec": {"display_name": "Python 3.9.7 64-bit", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}