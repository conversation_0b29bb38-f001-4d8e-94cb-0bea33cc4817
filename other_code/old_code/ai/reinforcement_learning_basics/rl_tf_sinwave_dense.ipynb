{"cells": [{"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Operations run on CPU\n", "Tensorflow Version: 2.11.0\n", "Execute eagerly? True\n"]}], "source": ["import os\n", "import numpy as np\n", "import random\n", "\n", "#disable GPU\n", "os.environ[\"CUDA_VISIBLE_DEVICES\"]=\"-1\" \n", "\n", "import gym\n", "from gym import spaces\n", "import tensorflow as tf\n", "from keras.layers import Dense\n", "from keras.models import Sequential\n", "from keras.optimizers import Adam\n", "\n", "#set random seed\n", "tf.random.set_seed(0)\n", "\n", "#output which device to compute on\n", "if len(tf.config.list_physical_devices(\"GPU\")) == 0:\n", "    print(\"Operations run on CPU\")\n", "else:\n", "    print(\"Operations run on GPU\")\n", "    \n", "#tensorflow version\n", "print(\"Tensorflow Version:\", tf.__version__)\n", "\n", "#runs eagerly?\n", "print(f\"Execute eagerly? {tf.executing_eagerly()}\")"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["#Environment\n", "class SineWaveEnvironment(gym.Env):\n", "    \n", "    metadata = {'render.modes': ['human']}\n", "        \n", "    def __init__(self, state_size, max_steps_per_episode):\n", "        super(SineWaveEnvironment, self).__init__()\n", "        \n", "        self._state_size = state_size\n", "        self._max_steps_per_episode = max_steps_per_episode\n", "        \n", "        self._data = self._generate_data()\n", "        self._state_from = 0\n", "        self._state_to = self._state_from + self._state_size\n", "        self._state = self._data[self._state_from: self._state_to]\n", "        self._step_counter = 0\n", "        self._reward_in_episode = 0\n", "        self.action_space = spaces.Discrete(2) #two actions\n", "        self.observation_space = spaces.Box(np.sin(-3*np.pi), np.sin(3*np.pi), shape=(1, 10), dtype=np.float32)\n", "        \n", "    \n", "    def reset(self):\n", "        \n", "        self._state_from = np.random.randint(0, len(self._data) - self._state_size - 1)\n", "        self._state_to = self._state_from + self._state_size\n", "        self._state = self._data[self._state_from:self._state_to]\n", "        self._step_counter = 0\n", "        self._reward_in_episode = 0\n", "        return self._state\n", "        \n", "        \n", "    def step(self, action):\n", "        \n", "        done = False\n", "        self._step_counter += 1\n", "        \n", "        #move window 1 step forward\n", "        self._state_from += 1\n", "        self._state_to += 1\n", "        self._state = self._data[self._state_from:self._state_to]\n", "        \n", "        #evaluate action\n", "        change = self._data[self._state_to] - self._data[self._state_to - 1]\n", "        if (action == 0 and change < 0) or (action == 1 and change > 0):\n", "            reward = 1\n", "        else:\n", "            reward = -1\n", "        self._reward_in_episode += reward\n", "    \n", "        #check whether done\n", "        if self._step_counter == self._max_steps_per_episode or self._state_to == len(self._data) - 1:\n", "            #we are done\n", "            done = True\n", "\n", "        #return tuple\n", "        return self._state, reward, done, {}\n", "            \n", "            \n", "    def render(self, mode='human', close=False):\n", "        print(f\"Current Step: {self._step_counter} - Cummulative Reward: {self._reward_in_episode}\")\n", "        \n", "            \n", "    def _generate_data(self):\n", "        \n", "        time = np.arange(-3*np.pi, 3*np.pi, 0.01)\n", "        amplitude = np.sin(time)\n", "        return amplitude.astype(np.float32)\n", "    \n", "\n", "    def output_environment_stats(self):\n", "        \n", "        print(f\"Number Datapoints in Dataset: {len(self._data)}\")\n", "        print(f\"Size of each state: {self._state_to}\")\n", "        print(f\"Maximum steps per episode: {self._max_steps_per_episode}\")\n", "        \n", "        \n", "def test_environment(env, min_action, max_action, number_iterations):\n", "   \n", "    obs = env.reset()\n", "    episode_steps = 0\n", "    \n", "    for i in range(number_iterations):\n", "        \n", "        episode_steps += 1\n", "        random_action = random.randint(min_action, max_action)\n", "        obs, rewards, done, info = env.step(random_action)\n", "\n", "        #render\n", "        if i % 1 == 0:\n", "            env.render(mode='human')\n", "\n", "        #reset if done\n", "        if done:\n", "            env.reset()\n", "            \n", "            \n", "def test_environment2(env, num_episodes):\n", "    \n", "    rewards = []\n", "    steps = []\n", "\n", "    for _ in range(num_episodes):\n", "      \n", "        done = False\n", "        episode_reward = 0\n", "        episode_steps = 0\n", "        obs = env.reset()\n", "        \n", "        while not done:\n", "            action = random.randint(0, 1)\n", "            obs, reward, done, info = env.step(action)\n", "            episode_steps += 1\n", "            episode_reward += reward\n", "            \n", "        rewards.append(episode_reward)\n", "        steps.append(episode_steps)\n", "    \n", "    num_steps = np.sum(steps)\n", "    avg_length = np.mean(steps)\n", "    avg_reward = np.mean(rewards)\n", "    max_reward = np.max(rewards)\n", "    max_length = np.max(steps)\n", "\n", "    print('num_episodes:', num_episodes, 'num_steps:', num_steps)\n", "    print('avg_length', avg_length, 'avg_reward:', avg_reward)\n", "    print('max_length', max_length, 'max_reward:', max_reward)"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["#Replay Buffer\n", "class ReplayBuffer(object):\n", "    \n", "    \n", "    def __init__(self, max_size, input_shape, n_actions, discrete=True):\n", "        \n", "        self.mem_size = max_size\n", "        self.mem_cntr = 0\n", "        self.discrete = discrete #discrete action space\n", "        self.dtype = np.int8 if self.discrete else np.float32\n", "        self.n_actions = n_actions\n", "        self.input_shape = input_shape #shape of state in environment\n", "        \n", "        self.state_memory = np.zeros((self.mem_size, self.input_shape))\n", "        self.new_state_memory = np.zeros((self.mem_size, self.input_shape))\n", "        self.action_memory = np.zeros((self.mem_size, self.n_actions), dtype=self.dtype)\n", "        self.reward_memory = np.zeros(self.mem_size)\n", "        self.terminal_memory = np.zeros(self.mem_size, dtype=np.float32)\n", "        \n", "        \n", "    def store_transition(self, state, action, reward, state_, done):\n", "        \n", "        index = self.mem_cntr % self.mem_size #fills up replay buffer to mem_size, then above it overwrites old content\n", "        \n", "        self.state_memory[index] = state\n", "        self.new_state_memory[index] = state_\n", "        self.reward_memory[index] = reward\n", "        self.terminal_memory[index] = 1 - int(done)\n", "        \n", "        if self.discrete:\n", "            actions = np.zeros(self.action_memory.shape[1])\n", "            actions[action] = 1.0\n", "            self.action_memory[index] = actions\n", "        else:\n", "            self.action_memory[index] = action\n", "            \n", "        self.mem_cntr += 1\n", "        \n", "        \n", "    def sample_buffer(self, batch_size):\n", "        \n", "        max_mem = min(self.mem_cntr, self.mem_size)\n", "        batch = np.random.choice(max_mem, batch_size)\n", "        \n", "        states = self.state_memory[batch]\n", "        states_ = self.new_state_memory[batch]\n", "        rewards = self.reward_memory[batch]\n", "        actions = self.action_memory[batch]\n", "        terminal = self.terminal_memory[batch]\n", "        \n", "        return states, actions, rewards, states_, terminal"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["#Network\n", "def build_dqn(learning_rate, n_actions, input_dims):\n", "        \n", "    model = Sequential()\n", "    model.add(Dense(20, input_shape=(input_dims), activation=\"relu\")) #last pos blank - represents batch size\n", "    model.add(<PERSON><PERSON>(10, activation=\"relu\"))  \n", "    model.add(Dense(n_actions))\n", "    model.compile(optimizer=<PERSON>(learning_rate=learning_rate), loss=\"mse\")\n", "    \n", "    return model"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["#Agent\n", "class Agent(object):\n", "    \n", "    \n", "    def __init__(self, learning_rate, gamma, n_actions, epsilon, batch_size, \n", "                 input_dims, epsilon_dec=0.996, epsilon_end=0.01,\n", "                 mem_size=1000000, fname=\"dqn_model.h5\"):\n", "        \n", "        self.action_space=[i for i in range(n_actions)]\n", "        self.gamma = gamma\n", "        self.epsilon = epsilon\n", "        self.epsilon_dec = epsilon_dec\n", "        self.epsilon_min = epsilon_end\n", "        self.batch_size = batch_size\n", "        self.model_file = fname\n", "        \n", "        self.memory = ReplayBuffer(mem_size, input_dims, n_actions, discrete=True)\n", "        self.q_eval = build_dqn(learning_rate, n_actions, input_dims)\n", "        \n", "        \n", "    def remember(self, state, action, reward, state_, done):\n", "        \n", "        self.memory.store_transition(state, action, reward, state_, done)\n", "        \n", "        \n", "    def choose_action(self, state):\n", "        \n", "        state = state[np.newaxis, :] #adds an axis to the vector\n", "        rand = np.random.random()\n", "        \n", "        if rand < self.epsilon:\n", "            #random action\n", "            action = np.random.choice(self.action_space)\n", "        else:\n", "            #greedy action\n", "            actions = self.q_eval.predict(state, verbose=0)\n", "            action = np.argmax(actions)\n", "            \n", "        return action\n", "    \n", "    \n", "    def learn(self):\n", "        \n", "        if self.memory.mem_cntr < self.batch_size:\n", "            return\n", "        \n", "        state, action, reward, new_state, done = self.memory.sample_buffer(self.batch_size) #obtain batch of trajectories from replay memory\n", "        \n", "        action_values = np.array(self.action_space, dtype=np.int8) #go back from one-hot encoding\n", "        action_indices = np.dot(action, action_values) #go back from one-hot encoding\n", "                \n", "        q_eval = self.q_eval.predict(state, verbose=0) \n", "        q_next = self.q_eval.predict(new_state, verbose=0)\n", "        \n", "        q_target = q_eval.copy()\n", "        \n", "        batch_index = np.arange(self.batch_size, dtype=np.int32) #???\n", "        \n", "        #update q-targets\n", "        q_target[batch_index, action_indices] = reward + self.gamma * np.max(q_next, axis=1) * done\n", "        \n", "        #fit (use q-targets for the optimization)\n", "        _ = self.q_eval.fit(state, q_target, verbose=0)\n", "        \n", "        #update epsilon\n", "        self.epsilon = self.epsilon * self.epsilon_dec if self.epsilon > self.epsilon_min else self.epsilon_min\n", "        \n", "        \n", "    def save_model(self):\n", "        \n", "        self.q_eval.save(self.model_file)\n", "        \n", "        \n", "    def load_model(self):\n", "        \n", "        # self.q_eval.load_weights()\n", "        self.q_eval = tf.keras.models.load_model(self.model_file) #load_model is imported from keras"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"ename": "TypeError", "evalue": "'int' object is not iterable", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mTypeError\u001b[0m                                 Traceback (most recent call last)", "Cell \u001b[0;32mIn[19], line 16\u001b[0m\n\u001b[1;32m     13\u001b[0m env \u001b[39m=\u001b[39m SineWaveEnvironment(state_size\u001b[39m=\u001b[39minput_dimensions, max_steps_per_episode\u001b[39m=\u001b[39m\u001b[39m100\u001b[39m)\n\u001b[1;32m     14\u001b[0m \u001b[39m# env = gym.make(\"CartPole-v1\")\u001b[39;00m\n\u001b[0;32m---> 16\u001b[0m agent \u001b[39m=\u001b[39m Agent(gamma\u001b[39m=\u001b[39;49mgamma, epsilon\u001b[39m=\u001b[39;49mepsilon, learning_rate\u001b[39m=\u001b[39;49mlearning_rate, input_dims\u001b[39m=\u001b[39;49minput_dimensions, \n\u001b[1;32m     17\u001b[0m               n_actions\u001b[39m=\u001b[39;49mn_actions, mem_size\u001b[39m=\u001b[39;49mmem_size, batch_size\u001b[39m=\u001b[39;49mbatch_size, epsilon_end\u001b[39m=\u001b[39;49mepsilon_end)\n\u001b[1;32m     19\u001b[0m \u001b[39m#load saved model\u001b[39;00m\n\u001b[1;32m     20\u001b[0m \u001b[39mif\u001b[39;00m os\u001b[39m.\u001b[39mpath\u001b[39m.\u001b[39mexists(\u001b[39m\"\u001b[39m\u001b[39mdqn_model.h5\u001b[39m\u001b[39m\"\u001b[39m):\n", "Cell \u001b[0;32mIn[18], line 18\u001b[0m, in \u001b[0;36mAgent.__init__\u001b[0;34m(self, learning_rate, gamma, n_actions, epsilon, batch_size, input_dims, epsilon_dec, epsilon_end, mem_size, fname)\u001b[0m\n\u001b[1;32m     15\u001b[0m \u001b[39mself\u001b[39m\u001b[39m.\u001b[39mmodel_file \u001b[39m=\u001b[39m fname\n\u001b[1;32m     17\u001b[0m \u001b[39mself\u001b[39m\u001b[39m.\u001b[39mmemory \u001b[39m=\u001b[39m ReplayBuffer(mem_size, input_dims, n_actions, discrete\u001b[39m=\u001b[39m\u001b[39mTrue\u001b[39;00m)\n\u001b[0;32m---> 18\u001b[0m \u001b[39mself\u001b[39m\u001b[39m.\u001b[39mq_eval \u001b[39m=\u001b[39m build_dqn(learning_rate, n_actions, input_dims)\n", "Cell \u001b[0;32mIn[17], line 5\u001b[0m, in \u001b[0;36mbuild_dqn\u001b[0;34m(learning_rate, n_actions, input_dims)\u001b[0m\n\u001b[1;32m      2\u001b[0m \u001b[39mdef\u001b[39;00m \u001b[39mbuild_dqn\u001b[39m(learning_rate, n_actions, input_dims):\n\u001b[1;32m      4\u001b[0m     model \u001b[39m=\u001b[39m Sequential()\n\u001b[0;32m----> 5\u001b[0m     model\u001b[39m.\u001b[39madd(Dense(\u001b[39m20\u001b[39;49m, input_shape\u001b[39m=\u001b[39;49m(input_dims), activation\u001b[39m=\u001b[39;49m\u001b[39m\"\u001b[39;49m\u001b[39mrelu\u001b[39;49m\u001b[39m\"\u001b[39;49m)) \u001b[39m#last pos blank - represents batch size\u001b[39;00m\n\u001b[1;32m      6\u001b[0m     model\u001b[39m.\u001b[39madd(Dense(\u001b[39m10\u001b[39m, activation\u001b[39m=\u001b[39m\u001b[39m\"\u001b[39m\u001b[39mrelu\u001b[39m\u001b[39m\"\u001b[39m))  \n\u001b[1;32m      7\u001b[0m     model\u001b[39m.\u001b[39madd(Dense(n_actions))\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/keras/dtensor/utils.py:96\u001b[0m, in \u001b[0;36mallow_initializer_layout.<locals>._wrap_function\u001b[0;34m(layer_instance, *args, **kwargs)\u001b[0m\n\u001b[1;32m     93\u001b[0m         \u001b[39mif\u001b[39;00m layout:\n\u001b[1;32m     94\u001b[0m             layout_args[variable_name \u001b[39m+\u001b[39m \u001b[39m\"\u001b[39m\u001b[39m_layout\u001b[39m\u001b[39m\"\u001b[39m] \u001b[39m=\u001b[39m layout\n\u001b[0;32m---> 96\u001b[0m init_method(layer_instance, \u001b[39m*\u001b[39;49margs, \u001b[39m*\u001b[39;49m\u001b[39m*\u001b[39;49mkwargs)\n\u001b[1;32m     98\u001b[0m \u001b[39m# Inject the layout parameter after the invocation of __init__()\u001b[39;00m\n\u001b[1;32m     99\u001b[0m \u001b[39mfor\u001b[39;00m layout_param_name, layout \u001b[39min\u001b[39;00m layout_args\u001b[39m.\u001b[39mitems():\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/keras/layers/core/dense.py:117\u001b[0m, in \u001b[0;36mDense.__init__\u001b[0;34m(self, units, activation, use_bias, kernel_initializer, bias_initializer, kernel_regularizer, bias_regularizer, activity_regularizer, kernel_constraint, bias_constraint, **kwargs)\u001b[0m\n\u001b[1;32m    102\u001b[0m \u001b[39m@utils\u001b[39m\u001b[39m.\u001b[39mallow_initializer_layout\n\u001b[1;32m    103\u001b[0m \u001b[39mdef\u001b[39;00m \u001b[39m__init__\u001b[39m(\n\u001b[1;32m    104\u001b[0m     \u001b[39mself\u001b[39m,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    115\u001b[0m     \u001b[39m*\u001b[39m\u001b[39m*\u001b[39mkwargs,\n\u001b[1;32m    116\u001b[0m ):\n\u001b[0;32m--> 117\u001b[0m     \u001b[39msuper\u001b[39;49m()\u001b[39m.\u001b[39;49m\u001b[39m__init__\u001b[39;49m(activity_regularizer\u001b[39m=\u001b[39;49mactivity_regularizer, \u001b[39m*\u001b[39;49m\u001b[39m*\u001b[39;49mkwargs)\n\u001b[1;32m    119\u001b[0m     \u001b[39mself\u001b[39m\u001b[39m.\u001b[39munits \u001b[39m=\u001b[39m \u001b[39mint\u001b[39m(units) \u001b[39mif\u001b[39;00m \u001b[39mnot\u001b[39;00m \u001b[39misinstance\u001b[39m(units, \u001b[39mint\u001b[39m) \u001b[39melse\u001b[39;00m units\n\u001b[1;32m    120\u001b[0m     \u001b[39mif\u001b[39;00m \u001b[39mself\u001b[39m\u001b[39m.\u001b[39munits \u001b[39m<\u001b[39m \u001b[39m0\u001b[39m:\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/tensorflow/python/trackable/base.py:205\u001b[0m, in \u001b[0;36mno_automatic_dependency_tracking.<locals>._method_wrapper\u001b[0;34m(self, *args, **kwargs)\u001b[0m\n\u001b[1;32m    203\u001b[0m \u001b[39mself\u001b[39m\u001b[39m.\u001b[39m_self_setattr_tracking \u001b[39m=\u001b[39m \u001b[39mFalse\u001b[39;00m  \u001b[39m# pylint: disable=protected-access\u001b[39;00m\n\u001b[1;32m    204\u001b[0m \u001b[39mtry\u001b[39;00m:\n\u001b[0;32m--> 205\u001b[0m   result \u001b[39m=\u001b[39m method(\u001b[39mself\u001b[39;49m, \u001b[39m*\u001b[39;49margs, \u001b[39m*\u001b[39;49m\u001b[39m*\u001b[39;49mkwargs)\n\u001b[1;32m    206\u001b[0m \u001b[39mfinally\u001b[39;00m:\n\u001b[1;32m    207\u001b[0m   \u001b[39mself\u001b[39m\u001b[39m.\u001b[39m_self_setattr_tracking \u001b[39m=\u001b[39m previous_value  \u001b[39m# pylint: disable=protected-access\u001b[39;00m\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/keras/engine/base_layer.py:452\u001b[0m, in \u001b[0;36mLayer.__init__\u001b[0;34m(self, trainable, name, dtype, dynamic, **kwargs)\u001b[0m\n\u001b[1;32m    450\u001b[0m         \u001b[39melse\u001b[39;00m:\n\u001b[1;32m    451\u001b[0m             batch_size \u001b[39m=\u001b[39m \u001b[39mNone\u001b[39;00m\n\u001b[0;32m--> 452\u001b[0m         batch_input_shape \u001b[39m=\u001b[39m (batch_size,) \u001b[39m+\u001b[39m \u001b[39mtuple\u001b[39;49m(kwargs[\u001b[39m\"\u001b[39;49m\u001b[39minput_shape\u001b[39;49m\u001b[39m\"\u001b[39;49m])\n\u001b[1;32m    453\u001b[0m     \u001b[39mself\u001b[39m\u001b[39m.\u001b[39m_batch_input_shape \u001b[39m=\u001b[39m batch_input_shape\n\u001b[1;32m    455\u001b[0m \u001b[39m# Manage initial weight values if passed.\u001b[39;00m\n", "\u001b[0;31mTypeError\u001b[0m: 'int' object is not iterable"]}], "source": ["#Train\n", "n_games = 500\n", "batch_size = 64\n", "input_dimensions = 10\n", "n_actions = 2\n", "mem_size = 100000\n", "\n", "gamma = 0.99\n", "epsilon = 1.0\n", "epsilon_end = 0.01\n", "learning_rate = 0.0005\n", "\n", "env = SineWaveEnvironment(state_size=input_dimensions, max_steps_per_episode=100)\n", "# env = gym.make(\"CartPole-v1\")\n", "\n", "agent = Agent(gamma=gamma, epsilon=epsilon, learning_rate=learning_rate, input_dims=input_dimensions, \n", "              n_actions=n_actions, mem_size=mem_size, batch_size=batch_size, epsilon_end=epsilon_end)\n", "\n", "#load saved model\n", "if os.path.exists(\"dqn_model.h5\"):\n", "    agent.load_model()\n", "\n", "scores = []\n", "eps_history = []\n", "\n", "for i in range(n_games):\n", "    \n", "    done=False\n", "    score = 0\n", "    observation = env.reset()\n", "    \n", "    while not done:\n", "        \n", "        action = agent.choose_action(observation)\n", "        observation_, reward, done, info = env.step(action)\n", "        score += reward\n", "        agent.remember(observation, action, reward, observation_, done)\n", "        observation = observation_\n", "        agent.learn()\n", "        \n", "    eps_history.append(agent.epsilon)\n", "    scores.append(score)\n", "    \n", "    avg_score = np.mean(scores[max(0, i-10):(i+1)])\n", "    print(f\"Episode: {i} - Score: {score:.2f} - Average Score: {avg_score:.2f}\")\n", "    \n", "    #save model every 10 episodes\n", "    if i % 10 == 0 and i > 0:\n", "        agent.save_model()    "]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.6"}, "orig_nbformat": 4, "vscode": {"interpreter": {"hash": "916dbcbb3f70747c44a77c7bcd40155683ae19c65e1c03b4aa3499c5328201f1"}}}, "nbformat": 4, "nbformat_minor": 2}