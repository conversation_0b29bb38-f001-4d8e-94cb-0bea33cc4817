{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#region Packages\n", "import os\n", "import numpy as np\n", "import pandas as pd\n", "import datetime as dt\n", "import json\n", "import matplotlib.pyplot as plt\n", "from sklearn.preprocessing import StandardScaler\n", "import plotly.express as px\n", "import tensorflow as tf\n", "\n", "#disable GPU\n", "# os.environ[\"CUDA_VISIBLE_DEVICES\"]=\"-1\" \n", "\n", "#custom libraries\n", "import Matt_RL_Library as matt\n", "%reload_ext autoreload\n", "%autoreload 2\n", "\n", "#set random seed\n", "tf.random.set_seed(0)\n", "\n", "#output which device to compute on\n", "if len(tf.config.list_physical_devices(\"GPU\")) == 0:\n", "    print(\"Operations run on CPU\")\n", "else:\n", "    print(\"Operations run on GPU\")\n", "    tf.config.experimental.set_memory_growth(tf.config.list_physical_devices(\"GPU\")[0], True)\n", "    \n", "#tensorflow version\n", "print(\"Tensorflow Version:\", tf.__version__)\n", "\n", "#runs eagerly?\n", "print(f\"Execute eagerly? {tf.executing_eagerly()}\")\n", "\n", "#endregion"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class Settings(object):\n", "    \n", "    def __init__(self):\n", "        \n", "        self.episodes = 500\n", "        self.batch_size = 128\n", "        self.number_actions = 2\n", "        self.state_shape = (20, 18)\n", "        self.memory_size = 50_000\n", "        self.min_memory_size = 1_000\n", "        self.update_target_every = 10\n", "        self.max_steps_per_episode = 100\n", "        self.datetime_from = \"2000-01-01\"\n", "        self.datetime_to = \"2030-12-31\"\n", "        self.training_symbol = \"USD\"\n", "        self.features = ['EURODOLLAR', '5YEARBOND', '10YEARBOND', '30YEARBOND', 'SPX500', 'RUSSELL2000', 'GOLD', 'VIX', 'CRUDEOIL', 'NATGAS', 'EUR', 'GBP', 'AUD', 'NZD', 'USD', 'CAD', 'CHF', 'JPY']\n", "        # self.features = ['EUR', 'GBP', 'AUD', 'NZD', 'USD', 'CAD', 'CHF', 'JPY']\n", "        self.source_data_directory = '/mnt/d/raw_data_daily_with_bbg.parquet'\n", "        self.train_target_directory = f\"/mnt/d/reinforcement_learning/LSTM_{dt.datetime.now().strftime('%Y-%b-%d %H-%M-%S')}/\"\n", "        self.eval_model_path_filename = f\"/mnt/d/reinforcement_learning/Trading_LSTM_2023-Apr-20 13-35-07/saved_model_episode_450.h5\"\n", "\n", "        self.enable_load_model = False\n", "        self.enable_save_model = True\n", "        self.is_log_to_tensorboard = True\n", "        self.is_log_trade_specifics = False\n", "        self.is_log_learning_profiler = False  \n", "        self.save_model_every_n_episodes = 10 if self.enable_save_model else 0\n", "        \n", "        self.learning_rate = 0.0005\n", "        self.gamma = 0.99\n", "        self.epsilon = 1.0\n", "        self.epsilon_dec = 0.9999 #0.996\n", "        self.epsilon_minimum = 0.05      \n", "        \n", "    def toJsonFile(self, path_filename):\n", "        with open(path_filename, \"w\") as f:\n", "            json.dump(self.__dict__, f, indent=4)        \n", "        \n", "    def fromJsonFile(self, path_filename):\n", "        with open(path_filename, \"r\") as f:\n", "            self.__dict__ = json.load(f)\n", "            self.state_shape = (self.state_shape[0], self.state_shape[1]) \n", "\n", "\n", "#parameters\n", "parameters = Settings()\n", "\n", "#train model\n", "# matt.train_model(parameters)\n", "\n", "#evaluate model\n", "matt.evaluate_model(parameters)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.6"}, "orig_nbformat": 4, "vscode": {"interpreter": {"hash": "916dbcbb3f70747c44a77c7bcd40155683ae19c65e1c03b4aa3499c5328201f1"}}}, "nbformat": 4, "nbformat_minor": 2}