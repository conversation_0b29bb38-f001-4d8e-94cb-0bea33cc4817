#region Packages
import os
import sys
import copy
import json
import numpy as np
import pandas as pd
import datetime as dt
import matplotlib.pyplot as plt
from sklearn.preprocessing import StandardScaler
import plotly.express as px

#disable GPU
# os.environ["CUDA_VISIBLE_DEVICES"]="-1" 
import tensorflow as tf

#set random seed
tf.random.set_seed(0)

#custom libraries
import mattlibrary.reinforcement_learning.train_evaluate as train_evaluate

#output which device to compute on
if len(tf.config.list_physical_devices("GPU")) == 0:
    print("Operations run on CPU")
else:
    print("Operations run on GPU")
    tf.config.experimental.set_memory_growth(tf.config.list_physical_devices("GPU")[0], True)
    
#tensorflow version
print("Tensorflow Version:", tf.__version__)

#runs eagerly?
print(f"Execute eagerly? {tf.executing_eagerly()}")

#endregion

#region Evaluate Model

#endregion

#region Train Model
 
#endregion
     
#Execution Code
class Settings(object):
    
    def __init__(self):
        
        self.episodes = 500
        self.batch_size = 128
        self.number_actions = 2
        self.state_shape = (20, 18)
        self.memory_size = 50_000
        self.min_memory_size = 1_000
        self.update_target_every = 10
        self.max_steps_per_episode = 100
        self.datetime_from = "2000-01-01"
        self.datetime_to = "2030-12-31"
        self.training_symbol = "USD"
        self.features = ['EURODOLLAR', '5YEARBOND', '10YEARBOND', '30YEARBOND', 'SPX500', 'RUSSELL2000', 'GOLD', 'VIX', 'CRUDEOIL', 'NATGAS', 'EUR', 'GBP', 'AUD', 'NZD', 'USD', 'CAD', 'CHF', 'JPY']
        # self.features = ['EUR', 'GBP', 'AUD', 'NZD', 'USD', 'CAD', 'CHF', 'JPY']
        self.source_data_directory = '/mnt/d/raw_data_daily_with_bbg.parquet'
        self.train_target_directory = f"/mnt/d/reinforcement_learning/LSTM_{dt.datetime.now().strftime('%Y-%b-%d %H-%M-%S')}/"
        self.eval_model_path_filename = f"/mnt/d/reinforcement_learning/Trading_LSTM_2023-Apr-20 13-35-07/saved_model_episode_450.h5"

        self.enable_load_model = False
        self.enable_save_model = True
        self.is_log_to_tensorboard = True
        self.is_log_trade_specifics = False
        self.is_log_learning_profiler = False  
        self.save_model_every_n_episodes = 10 if self.enable_save_model else 0
        
        self.learning_rate = 0.0005
        self.gamma = 0.99
        self.epsilon = 1.0
        self.epsilon_dec = 0.9999 #0.996
        self.epsilon_minimum = 0.05      
        
    def toJsonFile(self, path_filename):
        with open(path_filename, "w") as f:
            json.dump(self.__dict__, f, indent=4)        
        
    def fromJsonFile(self, path_filename):
        with open(path_filename, "r") as f:
            self.__dict__ = json.load(f)
            self.state_shape = (self.state_shape[0], self.state_shape[1]) 


if __name__ == "__main__":
    
    directory = os.path.dirname(os.path.abspath(__file__))
    
    #load parameters
    parameters = Settings()

    if os.path.isfile(f"{directory}/parameters.json"):
        parameters.fromJsonFile(f"{directory}/parameters.json")
    else:
        #save hyper parameters
        parameters.toJsonFile(f"{directory}/parameters.json")

    #train model
    # matt.train_model(parameters)
    
    #evaluate model
    train_evaluate.evaluate_model(parameters)
    
    
    