"""Reinforcement learning agent implementation for trading strategies."""

import datetime as dt
import numpy as np
import tensorflow as tf
from tensorflow.keras.layers import Dense
from tensorflow.keras.layers import LSTM
from tensorflow.keras.layers import Dropout
from tensorflow.keras.models import Sequential
from tensorflow.keras.optimizers import Adam
import mattlibrary as matt

class Agent:
    """Base class for reinforcement learning agents.
    
    This class implements a DQN (Deep Q-Network) agent with experience replay
    and target network for stable training.
    
    Attributes:
        batch_size (int): Number of samples to train on in each batch
        number_actions (int): Number of possible actions the agent can take
        state_shape (tuple): Shape of the state input to the neural network
        memory_size (int): Maximum size of the replay memory
        min_memory_size (int): Minimum size of replay memory before training starts
        update_target_every (int): Number of steps between target network updates
        log_to_tensorboard (bool): Whether to log training metrics to TensorBoard
        log_profiler (bool): Whether to log performance metrics
        callbacks (list): List of Keras callbacks for training
        learning_rate (float): Learning rate for the neural network
        gamma (float): Discount factor for future rewards
        epsilon (float): Initial exploration rate
        epsilon_dec (float): Rate at which exploration decreases
        epsilon_minimum (float): Minimum exploration rate
        time_consumption (dict): Dictionary tracking time spent in different operations
        memory (ReplayBuffer): Experience replay buffer
        model (Sequential): Main neural network model
        target_model (Sequential): Target network for stable training
        target_update_counter (int): Counter for target network updates
    """
    
    def __init__(self, batch_size, number_actions, state_shape, memory_size, min_memory_size, 
                 update_target_every, log_to_tensorboard, callbacks, learning_rate, gamma, 
                 epsilon, epsilon_dec, epsilon_minimum, log_profiler):
        """Initialize the agent with the specified parameters.
        
        Args:
            batch_size (int): Number of samples to train on in each batch
            number_actions (int): Number of possible actions the agent can take
            state_shape (tuple): Shape of the state input to the neural network
            memory_size (int): Maximum size of the replay memory
            min_memory_size (int): Minimum size of replay memory before training starts
            update_target_every (int): Number of steps between target network updates
            log_to_tensorboard (bool): Whether to log training metrics to TensorBoard
            callbacks (list): List of Keras callbacks for training
            learning_rate (float): Learning rate for the neural network
            gamma (float): Discount factor for future rewards
            epsilon (float): Initial exploration rate
            epsilon_dec (float): Rate at which exploration decreases
            epsilon_minimum (float): Minimum exploration rate
            log_profiler (bool): Whether to log performance metrics
        """
        self.batch_size = batch_size
        self.number_actions = number_actions
        self.action_space = [i for i in range(number_actions)]
        self.state_shape = state_shape
        self.memory_size = memory_size
        self.min_memory_size = min_memory_size
        self.update_target_every = update_target_every
        self.log_to_tensorboard = log_to_tensorboard
        self.log_profiler = log_profiler
        self.callbacks = callbacks
                
        self.learning_rate = learning_rate
        self.gamma = gamma
        self.epsilon = epsilon
        self.epsilon_dec = epsilon_dec
        self.epsilon_minimum = epsilon_minimum
        self.time_consumption = {
            "predict1": [], "predict2": [], "update_qtarget": [], 
            "fit": [], "finalize": [], "total": []
        }
        
        # Initialize replay memory
        self.memory = matt.reinforcement_learning.ReplayBuffer(memory_size, state_shape, number_actions)
                
        # Initialize main and target models
        self.model = self.create_model()
        self.target_model = self.create_model()
        self.target_model.set_weights(self.model.get_weights())
        self.target_update_counter = 0
                        
    def create_model(self):
        model = Sequential()
        model.add(LSTM(32, name="LSTM1", input_shape=self.state_shape, activation="tanh", return_sequences=True))
        #model.add(Dropout(0.30))
        model.add(LSTM(32, name="LSTM2", activation="tanh", return_sequences=True))
        #model.add(Dropout(0.30))
        model.add(LSTM(32, name="LSTM3", activation="tanh", return_sequences=False))
        #model.add(Dropout(0.30))
        model.add(Dense(32, name="Dense", activation="relu"))  
        model.add(Dense(self.number_actions, name="Dense_Output", activation="softmax"))
        
        model.compile(optimizer=Adam(learning_rate=self.learning_rate), loss="mse")   
        model.build()
        # print(model.summary())    
        return model

    def remember(self, state, action, reward, state_, done):
        self.memory.store_transition(state, action, reward, state_, done)
        
    def choose_action(self, state):
        state = state[np.newaxis, :] #adds an axis to the vector
        rand = np.random.random()
        
        if rand < self.epsilon:
            #random action
            action = np.random.choice(self.action_space)
        else:
            #greedy action
            actions = self.model.predict(state, verbose=0)
            action = np.argmax(actions)
            
        return action

    def learn(self, terminal_state, step):
        
        if self.log_profiler: learn_start = dt.datetime.now()
        
        #ensure enough trajectories are stored in memory before sampling batch
        if self.memory.mem_cntr < self.min_memory_size:
            return
        
        #grab batch of trajectories from memory
        states, actions, rewards, new_states, done = self.memory.sample_buffer(self.batch_size) #obtain batch of trajectories from replay memory
        
        if self.log_profiler: dt0 = dt.datetime.now()
        
        #predict current state q-values using current network
        current_qvalues = self.model.predict(states, verbose=0)
        
        if self.log_profiler:
            dt1 = dt.datetime.now()
            self.time_consumption["predict1"].append((dt1-dt0).total_seconds())
                
        #predict next state q-values using target network
        future_qvalues = self.target_model.predict(new_states, verbose=0)
        
        if self.log_profiler:
            dt2 = dt.datetime.now()
            self.time_consumption["predict2"].append((dt2-dt1).total_seconds())
        
        #update q-targets
        q_target = current_qvalues.copy()
        batch_index = np.arange(self.batch_size, dtype=np.int32)
        # action_values = np.array(self.action_space, dtype=np.int32) #go back from one-hot encoding
        # action_indices = np.dot(actions, action_values) #go back from one-hot encoding
        q_target[batch_index, actions] = rewards + self.gamma * np.max(future_qvalues, axis=1) * (1-done)
        
        if self.log_profiler:
            dt3 = dt.datetime.now()
            self.time_consumption["update_qtarget"].append((dt3-dt2).total_seconds())
        
        #fit (use q-targets for the optimization)
        if self.log_to_tensorboard:
            self.model.fit(states, q_target, shuffle=False, verbose=0, callbacks=self.callbacks if terminal_state else None)
        else:
            self.model.fit(states, q_target, shuffle=False, verbose=0)

        if self.log_profiler:
            dt4 = dt.datetime.now()
            self.time_consumption["fit"].append((dt4-dt3).total_seconds())

        #update target network counter every episode
        if terminal_state:
            self.target_update_counter += 1
            
        #if counter reaches set value, update target network with weights of main network
        if self.target_update_counter > self.update_target_every:
            self.target_model.set_weights(self.model.get_weights())
            self.target_update_counter = 0
    
        #update epsilon
        self.epsilon = self.epsilon * self.epsilon_dec if self.epsilon > self.epsilon_minimum else self.epsilon_minimum
             
        if self.log_profiler:   
            learn_end = dt.datetime.now()
            self.time_consumption["finalize"].append((learn_end-dt4).total_seconds())
            self.time_consumption["total"].append((learn_end-learn_start).total_seconds())
                    
                    
    def evaluate_state(self, state):
        
        action_raw = self.model.predict(state, verbose=0)
        action = np.argmax(action_raw)
        return action


    def evaluate_states(self, states):
        
        actions_raw = self.model.predict(states, verbose=0)
        actions = np.argmax(actions_raw, axis=1)
        return actions

                       
    def output_time_profiler(self):
        
        print(f"Predict1 (sec):{sum(self.time_consumption['predict1']):.4f}")
        print(f"Predict2 (sec):{sum(self.time_consumption['predict2']):.4f}")
        print(f"Update Q Target (sec):{sum(self.time_consumption['update_qtarget']):.4f}")
        print(f"Fit (sec):{sum(self.time_consumption['fit']):.4f}")
        print(f"Finalize (sec):{sum(self.time_consumption['finalize']):.4f}")
        print(f"Total Learning (sec):{sum(self.time_consumption['total']):.4f}")
            
        self.time_consumption['predict1'].clear()
        self.time_consumption['predict2'].clear()
        self.time_consumption['update_qtarget'].clear()
        self.time_consumption['fit'].clear()
        self.time_consumption['finalize'].clear()
        self.time_consumption['total'].clear()
         
    def load_model(self, path_filename):
        self.model = tf.keras.models.load_model(path_filename)    
                
    def save_model(self, path_filename):
        self.model.save(path_filename)