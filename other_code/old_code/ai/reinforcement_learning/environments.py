import random
import numpy as np
import pandas as pd
import gym
from gym import spaces
from sklearn.preprocessing import StandardScaler
import mattlibrary as matt

class TradingEnvironment(gym.Env):
    
    metadata = {'render.modes': ['human']}
    
    def __init__(self, obs_shape, max_steps_per_episode, log, training_dataframe, performance_dataframe, performance_column_index):
        super(TradingEnvironment, self).__init__()
        
        #environment related variables
        self.training_data = training_dataframe.to_numpy()
        self.performance_data = performance_dataframe.to_numpy()
        self.performance_column_index = performance_column_index
            
        self.log = log 
        self.obs_shape = obs_shape #(sequence_length, number_features)
        self.sequence_length = obs_shape[0]
        self.step_counter = 0
        self.state_from = 0
        self.state_to = self.state_from + self.sequence_length
        self.max_steps_per_episode = max_steps_per_episode #max steps before episode ends
        self.action_space = spaces.Discrete(3) #Four discrete actions in this environment
        self.observation_space = spaces.Box(low=np.amin(self.training_data), high=np.amax(self.training_data), shape=self.obs_shape, dtype=np.float32)

        #trading related variables
        self.initial_account = 100_000 #default account size
        self.trade_size = 100_000 #default trade size
        self.position = matt.finance.Position()
        self.reward_in_episode = 0
    
   
    def reset(self, start_at_the_beginning):
        
        if start_at_the_beginning:
            self.state_from = 0
        else:
            #generate a random starting state within dataset    
            self.state_from = np.random.randint(0, len(self.training_data) - self.sequence_length - self.max_steps_per_episode)
        
        self.state_to = self.state_from + self.sequence_length
        self.step_counter = 0
        self.position.reset()
        self.reward_in_episode = 0
        return self.training_data[self.state_from:self.state_to,:], {}
    
        
    def step(self, action):
        
        #set done flag
        done = False
                
        #capture pnl before action is taken
        pnl_before = self.position.unrealized_pnl + self.position.realized_pnl
                        
        #take the action here (include timestamp and last price)
        self.take_action(action, self.performance_data[self.state_to, 0], self.performance_data[self.state_to, self.performance_column_index], self.log)
                 
        #move one step forward
        self.step_counter += 1
        self.state_from += 1
        self.state_to += 1
                      
        #update position with updated last timestamp and last price
        self.position.update_price(self.performance_data[self.state_to, 0], self.performance_data[self.state_to, self.performance_column_index]) 
        
        #determine the reward (evaluate pnl of action taken vs no action taken)
        pnl_change = (self.position.unrealized_pnl + self.position.realized_pnl) - pnl_before
        
        # reward = pnl_change / self.initial_account #did not work (reward bouncing around zero)
        # reward = pnl_change #did not work (reward bouncing around zero)
        reward = 1.0 if pnl_change >= 0 else -1.0
                
        #log if requested
        if self.log == True:
            print(self.position)
    
        #update cumulative reward in episode
        self.reward_in_episode += reward   
               
        #check whether done
        if self.step_counter == self.max_steps_per_episode or self.state_to == len(self.training_data) - 1:
            done = True   
                
        return self.training_data[self.state_from:self.state_to,:], reward, done, {}, {}
    
    
    def render(self, mode='human', close=False):
        print(f"Current Step: {self.step_counter} - Cum Reward: {round(100 * self.reward_in_episode, 2)}% - Account: {round(self.initial_account + self.position.unrealized_pnl + self.position.realized_pnl, 2)}")
        print(f"Portfolio: {self.position}")
       
        
    def take_action(self, action, timestamp, price, log):
        
        match(action):
            # case 0: #flat position
            #     if self.position.position_size == 0:
            #         if log: print("Action: Flat Position -> Do nothing")    
            #     else:
            #         order_size = -self.position.position_size
            #         if log: print(f"Action: Flat Position -> Order: Size:{order_size} @{price:.5f} - Timestamp:{timestamp}")
            #         self.position.process_order(timestamp, order_size, price)
                
            case 0: #hold long position
                
                if self.position.position_size == self.trade_size:
                    if log: print("Action: Long Position -> Do nothing")    
                else:
                    order_size = self.trade_size - self.position.position_size
                    if log: print(f"Action: Long Position -> Order: Size:{order_size} @{price:.5f} - Timestamp:{timestamp}")
                    self.position.process_order(timestamp, order_size, price)
                    
            case 1: #hold short position
                
                if self.position.position_size == -self.trade_size:
                    if log: print("Action: Short Position -> Do nothing")    
                else:
                    order_size = -self.trade_size - self.position.position_size
                    if log: print(f"Action: Short Position -> Order: Size:{order_size} @{price:.5f} - Timestamp:{timestamp}")
                    self.position.process_order(timestamp, order_size, price)
           
            case _:
                raise Exception("Invalid Action")
                    
        
    def generate_simulated_data(self):
        time = np.arange(-3*np.pi, 3*np.pi, 0.01)
        amplitude = (1 + np.sin(time)/5).astype(np.float32) #shift sin values up by 100 units
        master_df = pd.DataFrame({"Close":amplitude})
        
        transformed_df = master_df.pct_change()
        transformed_df = transformed_df.dropna()
        
        scaler = StandardScaler()
        transformed_df = pd.DataFrame(scaler.fit_transform(transformed_df), index=transformed_df.index, columns=transformed_df.columns)
        
        return transformed_df, master_df[1:]
             
class SineWaveEnvironment(gym.Env):
    
    metadata = {'render.modes': ['human']}
        
    def __init__(self, obs_shape, max_steps_per_episode):
        super(SineWaveEnvironment, self).__init__()
        
        self._obs_shape = obs_shape #(sequence_length, number_features)
        self._state_size = obs_shape[0]
        self._max_steps_per_episode = max_steps_per_episode
        
        self._data = self._generate_data()
        self._state_from = 0
        self._state_to = self._state_from + self._state_size
        self._state = self._data[self._state_from: self._state_to]
        self._step_counter = 0
        self._reward_in_episode = 0
        self.action_space = spaces.Discrete(2) #two actions
        self.observation_space = spaces.Box(np.sin(-3*np.pi), np.sin(3*np.pi), shape=(1, 10), dtype=np.float32)
        
    
    def reset(self):
        self._state_from = np.random.randint(0, len(self._data) - self._state_size - self._max_steps_per_episode)
        self._state_to = self._state_from + self._state_size
        self._state = self._data[self._state_from:self._state_to]
        self._step_counter = 0
        self._reward_in_episode = 0
        
        reshaped_state = self._state.reshape(self._obs_shape)
        return reshaped_state, {}
        
    def step(self, action):
        done = False
        self._step_counter += 1
        
        #move window 1 step forward
        self._state_from += 1
        self._state_to += 1
        self._state = self._data[self._state_from:self._state_to]
        
        #evaluate action
        change = self._data[self._state_to] - self._data[self._state_to - 1]
        if (action == 0 and change < 0) or (action == 1 and change > 0):
            reward = 1
        else:
            reward = -1
        self._reward_in_episode += reward
    
        #check whether done
        if self._step_counter == self._max_steps_per_episode or self._state_to == len(self._data) - 1:
            #we are done
            done = True

        #return tuple
        reshaped_state = self._state.reshape(self._obs_shape)
        return reshaped_state, reward, done, {}, {}
            
    def render(self, mode='human', close=False):
        print(f"Current Step: {self._step_counter} - Cummulative Reward: {self._reward_in_episode}")
            
    def _generate_data(self):
        time = np.arange(-3*np.pi, 3*np.pi, 0.01)
        amplitude = np.sin(time)
        return amplitude.astype(np.float32)

    def output_environment_stats(self):
        print(f"Number Datapoints in Dataset: {len(self._data)}")
        print(f"Size of each state: {self._state_to}")
        print(f"Maximum steps per episode: {self._max_steps_per_episode}")

def test_environment(env, min_action, max_action, number_iterations):
   
    obs = env.reset()
    episode_steps = 0
    
    for i in range(number_iterations):
        
        episode_steps += 1
        random_action = random.randint(min_action, max_action)
        obs, rewards, done, info, _ = env.step(random_action)

        #render
        if i % 1 == 0:
            None
            # env.render(mode='human')

        #reset if done
        if done:
            env.reset()
                 
def test_environment2(env, num_episodes, num_actions):
    
    rewards = []
    steps = []
    
    for _ in range(num_episodes):
      
        done = False
        episode_reward = 0
        episode_steps = 0
        obs = env.reset()
        
        while not done:
            action = random.randint(0, num_actions-1)
            obs, reward, done, info, _ = env.step(action)
            episode_steps += 1
            episode_reward += reward
            
        rewards.append(episode_reward)
        steps.append(episode_steps)
    
    num_steps = np.sum(steps)
    avg_length = np.mean(steps)
    avg_reward = np.mean(rewards)
    max_reward = np.max(rewards)
    min_reward = np.min(rewards)
    max_length = np.max(steps)

    print('num_episodes:', num_episodes, 'num_steps:', num_steps)
    print('avg_length', avg_length, 'avg_reward:', avg_reward)
    print('max_length', max_length, 'max_reward:', max_reward, 'min_reward:', min_reward) 