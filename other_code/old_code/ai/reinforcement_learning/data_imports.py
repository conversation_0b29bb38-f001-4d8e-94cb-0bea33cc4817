import os
import pandas as pd
import numpy as np
import talib
from sklearn.preprocessing import StandardScaler

def resample_dataframe(df, dt_compression_string):
            
    logic = {'Open' : 'first',
            'High' : 'max',
            'Low' : 'min',
            'Close' : 'last'}

    return df.resample(dt_compression_string).apply(logic)
                     
def cancatenate_new_dukascopy_raw_data():
    
    source_existing_dir = '/mnt/d/source0/'
    source_dir = '/mnt/d/source/'
    target_dir = '/mnt/d/target/'
    
    files = os.listdir(path=source_dir)
    symbols = [file[0:6] for file in files]
    symbols = list(set(symbols))
    
    #iterate over each unique symbol
    for symbol in symbols:
       
        symbol_target_df = None
        source_files = [file for file in files if symbol in file]       
        
        #iterate over the bid and ask file of the matching symbol
        for source_filename in source_files:
                
            #read csv data  
            source_df = pd.read_csv(source_dir + source_filename, index_col=0, parse_dates=True, dtype=np.float32)
            source_df.index.name = "Timestamp"
            source_df.index = pd.to_datetime(source_df.index, errors="coerce")
            
            #remove 'Volume' column
            source_df = source_df.drop(["Volume "], axis=1)
            
            #rename remaining columns
            subscript = source_filename[13:13+3]
            source_df = source_df.rename(columns={"Open":f"{subscript}_Open", "High":f"{subscript}_High", "Low":f"{subscript}_Low", "Close":f"{subscript}_Close"})
            
            #merge bid and ask data frames
            if symbol_target_df is None:
                symbol_target_df = source_df
            else:
                symbol_target_df = pd.merge(source_df, symbol_target_df, how="inner", left_index=True, right_index=True)
        
        symbol_target_df = symbol_target_df.dropna()
        
        #generate open high low close columns (average of bid and ask)
        symbol_target_df["Open"] = (symbol_target_df["Bid_Open"] + symbol_target_df["Ask_Open"]) / 2
        symbol_target_df["High"] = (symbol_target_df["Bid_High"] + symbol_target_df["Ask_High"]) / 2
        symbol_target_df["Low"] = (symbol_target_df["Bid_Low"] + symbol_target_df["Ask_Low"]) / 2
        symbol_target_df["Close"] = (symbol_target_df["Bid_Close"] + symbol_target_df["Ask_Close"]) / 2

        #remove unused columns
        cols_to_keep = ["Open", "High", "Low", "Close"]
        cols_to_remove = [col for col in symbol_target_df.columns if col not in cols_to_keep]
        symbol_target_df = symbol_target_df.drop(cols_to_remove, axis=1)
        
        #load existing file
        target_filename = f"{symbol}_1minute.csv"
        symbol_final_df = pd.read_csv(source_existing_dir + target_filename, index_col=0, parse_dates=True, dtype=np.float32)
        symbol_final_df.index.name = "Timestamp"
        symbol_final_df.index = pd.to_datetime(symbol_final_df.index, errors="coerce")
                
        #merge the two dataframes for symbol
        symbol_df = pd.concat([symbol_final_df, symbol_target_df])
        
        #clean
        symbol_df = symbol_df.dropna()
        
        #store back as csv 
        symbol_df.to_csv(target_dir + target_filename)
             
def covert_dukascopy_to_parquet():
    
    source_dir = '/mnt/d/dukalscopy_raw/'
    target_dir = '/mnt/d/'
    target_filename = 'raw_data_hourly.parquet'
    compression_string = "1H"
    master_df = None
    
    #read target file if it exists
    if os.path.isfile(target_dir + target_filename):
        master_df = pd.read_parquet(target_dir + target_filename)
    
    #iterate over source files
    for filename in os.listdir(path=source_dir):
        if not filename.endswith(".csv"):
            continue
        
        symbol = filename[0:6]
        
        #read raw data into pandas dataframe
        source_df = pd.read_csv(source_dir + filename, index_col=0, parse_dates=True, dtype=np.float32)
        source_df.index.name = "Timestamp"
        source_df.index = pd.to_datetime(source_df.index, errors="coerce")
        source_df = source_df.dropna()
                        
        #compress data
        source_df = resample_dataframe(source_df, compression_string)
        source_df = source_df.dropna()
                        
        #rename columns
        source_df = source_df.rename(columns={"Open":f"{symbol}_Open", "High":f"{symbol}_High", "Low":f"{symbol}_Low", "Close":f"{symbol}_Close"})
        
        #merge dataframes
        if master_df is None:
            master_df = source_df
        else:
            master_df = pd.merge(source_df, master_df, how="inner", left_index=True, right_index=True)     

        #cleanse data
        master_df = master_df.dropna() 

        print(f"Successfully imported {symbol} into pandas dataframe")
            
    #store dataframe in parquet
    master_df.to_parquet(target_dir + target_filename)

def get_data(symbols, path_filename):

    #import raw data if not yet done
    if os.path.isfile(path_filename) == False:
        print(f"Abort - Could not find file {path_filename}")    
        return
        
    df = pd.read_parquet(path_filename)
    
    #create some additional features
    # for symbol in symbols:
    #     master_df['TimeOfDay'] = master_df.index.hour + master_df.index.minute / 60
    #     master_df['DayofWeek'] = master_df.index.weekday
    #     master_df[symbol + '_Sma5'] = talib.SMA(master_df[symbol + '_Close'], timeperiod=5)
    #     master_df[symbol + '_Sma14'] = talib.SMA(master_df[symbol + '_Close'], timeperiod=14)
    #     master_df[symbol + '_Sma30'] = talib.SMA(master_df[symbol + '_Close'], timeperiod=30)
    # master_df = master_df.dropna()
    
    #filter columns (only keep close bar columns)
    df = df[symbols]
    
    #cleanse
    df = df.dropna()
    
    #transform pricing data to percent changes
    train_df = df.pct_change()
        
    #remove first row from master_df to account for first row being NA (pct change)
    df = df[1:]
    train_df = train_df[1:]
            
    #insert datetime index as first column (used for performance attribution)
    df.insert(loc=0, column="Timestamp", value=df.index)
    
    #normalize data
    scaler = StandardScaler()
    standardized_df = pd.DataFrame(scaler.fit_transform(train_df), index=train_df.index, columns=train_df.columns)
            
    return standardized_df, df