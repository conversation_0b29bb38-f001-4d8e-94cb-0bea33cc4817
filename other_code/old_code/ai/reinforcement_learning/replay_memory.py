"""Replay buffer implementation for reinforcement learning."""

import numpy as np

class ReplayBuffer:
    """A replay buffer for storing and sampling experience tuples.
    
    This class implements a circular buffer for storing experience tuples
    (state, action, reward, next_state, done) used in reinforcement learning.
    
    Attributes:
        mem_size (int): Maximum size of the replay buffer
        mem_cntr (int): Current number of stored experiences
        n_actions (int): Number of possible actions in the environment
        input_shape (tuple): Shape of the state representation
        state_memory (np.ndarray): Array storing states
        new_state_memory (np.ndarray): Array storing next states
        action_memory (np.ndarray): Array storing actions
        reward_memory (np.ndarray): Array storing rewards
        terminal_memory (np.ndarray): Array storing terminal flags
    """
    
    def __init__(self, max_size, input_shape, n_actions):
        """Initialize a new ReplayBuffer instance.
        
        Args:
            max_size (int): Maximum number of experiences to store
            input_shape (tuple): Shape of the state representation
            n_actions (int): Number of possible actions in the environment
        """
        self.mem_size = max_size
        self.mem_cntr = 0
        self.n_actions = n_actions
        self.input_shape = input_shape
        
        shape = (self.mem_size, ) + input_shape
        self.state_memory = np.zeros(shape, dtype=np.float32)
        self.new_state_memory = np.zeros(shape, dtype=np.float32)
        self.action_memory = np.zeros(self.mem_size, dtype=np.int32)
        self.reward_memory = np.zeros(self.mem_size, dtype=np.float32)
        self.terminal_memory = np.zeros(self.mem_size, dtype=np.int32)
        
    def store_transition(self, state, action, reward, state_, done):
        """Store a new experience tuple in the buffer.
        
        Args:
            state: Current state observation
            action: Action taken in the current state
            reward: Reward received after taking the action
            state_: Next state observation
            done (bool): Whether the episode ended after this transition
        """
        index = self.mem_cntr % self.mem_size
        self.state_memory[index] = state
        self.new_state_memory[index] = state_
        self.reward_memory[index] = reward
        self.terminal_memory[index] = int(done)
        self.action_memory[index] = action
        self.mem_cntr += 1
        
    def sample_buffer(self, batch_size):
        """Sample a batch of experiences from the buffer.
        
        Args:
            batch_size (int): Number of experiences to sample
            
        Returns:
            tuple: (states, actions, rewards, next_states, terminals)
                - states: Array of current states
                - actions: Array of actions taken
                - rewards: Array of rewards received
                - next_states: Array of next states
                - terminals: Array of terminal flags
        """
        max_mem = min(self.mem_cntr, self.mem_size)
        batch = np.random.choice(max_mem, batch_size)
        
        states = self.state_memory[batch]
        states_ = self.new_state_memory[batch]
        rewards = self.reward_memory[batch]
        actions = self.action_memory[batch]
        terminal = self.terminal_memory[batch]
        
        return states, actions, rewards, states_, terminal