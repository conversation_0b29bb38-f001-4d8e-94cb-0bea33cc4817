{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "import os\n", "import torch\n", "import torch.nn as nn\n", "import torch.optim as optim\n", "from torch.utils.data import Dataset, DataLoader\n", "import torch.version\n", "import torchvision.transforms as transforms\n", "from torchvision.datasets import ImageFolder\n", "import matplotlib.pyplot as plt # For data viz\n", "import pandas as pd\n", "import numpy as np\n", "import timm\n", "from tqdm.notebook import tqdm\n", "\n", "print('Python Version:', sys.version)\n", "print(\"Pytorch Version\", torch.__version__)\n", "print('Numpy version', np.__version__)\n", "print('Pandas version', pd.__version__)\n", "print(\"CUDA Version\", torch.version.cuda)\n", "print(\"cuDNN Version\", torch.backends.cudnn.version())\n", "print(\"GPU Device Name\", torch.cuda.get_device_name(0))\n", "print(\"GPU Device Total Memory (Gigabytes)\", round(torch.cuda.get_device_properties(0).total_memory / 1024**3, 2))\n", "\n", "#set device to gpu\n", "# device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "\n", "#set gpu as default\n", "# torch.set_default_device(device)\n", "\n", "#set default dtype\n", "# torch.set_default_dtype(torch.float32)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#Dataset\n", "class PlayingCardDataset(Dataset):\n", "    def __init__(self, data_dir, transform=None):\n", "        self.data = ImageFolder(data_dir, transform=transform)\n", "    \n", "    def __len__(self):\n", "        return len(self.data)\n", "    \n", "    def __getitem__(self, idx):\n", "        return self.data[idx]\n", "    \n", "    @property\n", "    def classes(self):\n", "        return self.data.classes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#Create a simple classifier for the card game\n", "class SimpleCardClassifer(nn.Module):\n", "    def __init__(self, num_classes=53):\n", "        super(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "        # Where we define all the parts of the model\n", "        self.base_model = timm.create_model('efficientnet_b0', pretrained=False)\n", "\n", "        # Make a feature extractor (used to remove a layer not necessary, need to still figure out why)\n", "        self.features = nn.Sequential(*list(self.base_model.children())[:-1])\n", "\n", "        # Make a classifier\n", "        enet_out_size = 1280\n", "        self.classifier = nn.Sequential(\n", "            nn.<PERSON>(),\n", "            nn.Linear(enet_out_size, num_classes)\n", "        )\n", "    \n", "    def forward(self, x):\n", "        # Connect these parts and return the output\n", "        x = self.features(x)\n", "        output = self.classifier(x)\n", "        return output"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#create directory variable that points to the training directory inside of the data directory\n", "directory = os.path.join(os.getcwd(), 'data/train')\n", "\n", "#create a dictionary that maps the class name to the class number\n", "target_to_class = {v: k for k, v in ImageFolder(directory).class_to_idx.items()}\n", "\n", "#transforms that will be applied to the images\n", "transform = transforms.Compose([transforms.Resize((128, 128)), transforms.ToTensor()])\n", "\n", "#create the dataset\n", "dataset = PlayingCardDataset(data_dir=directory, transform=transform)\n", "\n", "#dataloader (used to batch data)\n", "dataloader = DataLoader(dataset, batch_size=32, shuffle=True)\n", "\n", "#data\n", "train_folder = os.path.join(os.getcwd(), 'data/train')\n", "valid_folder = os.path.join(os.getcwd(), 'data/valid')\n", "test_folder = os.path.join(os.getcwd(), 'data/test')\n", "\n", "train_dataset = PlayingCardDataset(train_folder, transform=transform)\n", "val_dataset = PlayingCardDataset(valid_folder, transform=transform)\n", "test_dataset = PlayingCardDataset(test_folder, transform=transform)\n", "\n", "train_loader = DataLoader(train_dataset, batch_size=32, shuffle=True)\n", "val_loader = DataLoader(val_dataset, batch_size=32, shuffle=False)\n", "test_loader = DataLoader(test_dataset, batch_size=32, shuffle=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#training loop\n", "num_epochs = 20\n", "train_losses, val_losses = [], []\n", "\n", "device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "# device = torch.device(\"cpu\")\n", "\n", "model = SimpleCardClassifer(num_classes=53)\n", "model.to(device)\n", "\n", "criterion = nn.CrossEntropyLoss()\n", "optimizer = optim.<PERSON>(model.parameters(), lr=0.001)\n", "\n", "for epoch in range(num_epochs):\n", "    # Training phase\n", "    model.train()\n", "    running_loss = 0.0\n", "    for images, labels in tqdm(train_loader, desc='Training loop'):\n", "        # Move inputs and labels to the device\n", "        images, labels = images.to(device), labels.to(device)\n", "        \n", "        optimizer.zero_grad()\n", "        outputs = model(images)\n", "        loss = criterion(outputs, labels)\n", "        loss.backward()\n", "        optimizer.step()\n", "        running_loss += loss.item() * labels.size(0)\n", "    train_loss = running_loss / len(train_loader.dataset)\n", "    train_losses.append(train_loss)\n", "    \n", "    # Validation phase\n", "    model.eval()\n", "    running_loss = 0.0\n", "    with torch.no_grad():\n", "        for images, labels in tqdm(val_loader, desc='Validation loop'):\n", "            # Move inputs and labels to the device\n", "            images, labels = images.to(device), labels.to(device)\n", "         \n", "            outputs = model(images)\n", "            loss = criterion(outputs, labels)\n", "            running_loss += loss.item() * labels.size(0)\n", "    val_loss = running_loss / len(val_loader.dataset)\n", "    val_losses.append(val_loss)\n", "    print(f\"Epoch {epoch+1}/{num_epochs} - Train loss: {train_loss}, Validation loss: {val_loss}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#plot results\n", "plt.plot(train_losses, label='Training loss')\n", "plt.plot(val_losses, label='Validation loss')\n", "plt.legend()\n", "plt.title(\"Loss over epochs\")\n", "plt.show()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 2}