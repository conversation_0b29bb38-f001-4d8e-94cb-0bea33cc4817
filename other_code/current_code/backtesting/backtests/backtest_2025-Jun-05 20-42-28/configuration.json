{"starting_balance": 100000, "base_currency": "USD", "symbols_data": ["AUDUSD", "AUDNZD", "AUDCAD", "AUDCHF", "AUDJPY", "EURAUD", "GBPAUD", "NZDUSD", "NZDCAD", "NZDCHF", "NZDJPY", "EURNZD", "GBPNZD", "CADCHF", "CADJPY", "EURCAD", "GBPCAD", "USDCAD", "CHFJPY", "EURCHF", "GBPCHF", "USDCHF", "EURJPY", "EURUSD", "EURGBP", "GBPJPY", "GBPUSD", "USDJPY"], "symbols_traded": ["AUDUSD", "AUDNZD", "AUDCAD", "AUDCHF", "AUDJPY", "EURAUD", "GBPAUD", "NZDUSD", "NZDCAD", "NZDCHF", "NZDJPY", "EURNZD", "GBPNZD", "CADCHF", "CADJPY", "EURCAD", "GBPCAD", "USDCAD", "CHFJPY", "EURCHF", "GBPCHF", "USDCHF", "EURJPY", "EURUSD", "EURGBP", "GBPJPY", "GBPUSD", "USDJPY"], "execution_plugin_type": "SimulatedExecutionEngine", "data_parameters": {"data_directory": "/mnt/sambashare/nas/storage/Share2_FinancialData1/Dukascopy/Currencies/dukascopy_fx_polars_db/", "dataset_name": "dukascopy_daily_fx", "sma_window_size": 30, "start_dt": "2000-05-03T00:00:00", "end_dt": "2023-12-31T00:00:00"}, "strategy_parameters": {"symbol_id": "USDJPY", "order_size": 100000, "is_target_size": true, "profit_target_percent": 0.002, "stop_loss_percent": 0.05, "is_strategy_excel_logging": true}, "track_performance": true, "visualize_performance": true, "logging_enabled": true, "log_to_console": false, "log_to_file": true, "backtest_archiving": true, "statistics_excel_logging": true, "remove_all_previous_backtest_data": true, "backtests_base_directory": "/home/<USER>/development/python_development/backtesting/backtests/"}