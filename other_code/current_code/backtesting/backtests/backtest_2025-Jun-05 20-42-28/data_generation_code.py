import polars as pl
from mattlibrary.datamanagement.polars_db import PolarsDataFrameManager
from mattlibrary.trading.backtest_configuration import BacktestConfiguration

def generate_data(configuration: BacktestConfiguration) -> pl.DataFrame:

    #data parameters
    data_directory = configuration.data_parameters["data_directory"]
    dataset_name = configuration.data_parameters["dataset_name"]
    sma_window_size = configuration.data_parameters["sma_window_size"]
    start_dt = configuration.data_parameters["start_dt"]
    end_dt = configuration.data_parameters["end_dt"]

    manager = PolarsDataFrameManager(base_directory=str(data_directory))
    filter_expr = pl.col("symbol").is_in(configuration.symbols_data) & (pl.col("datetime").is_between(start_dt, end_dt))
    df = manager.read_data(dataset_name=dataset_name, filter_expr=filter_expr)
    data_source = df.sort("symbol", "datetime").with_columns(sma=pl.col("close").rolling_mean(window_size=sma_window_size).over("symbol")).drop_nulls().sort("datetime")
    return data_source