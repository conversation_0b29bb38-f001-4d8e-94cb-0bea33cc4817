from typing import List
from mattlibrary.trading.backtest_configuration import BacktestConfiguration
from mattlibrary.trading.strategy_base import StrategyBase
from mattlibrary.trading.trading_strategies.sma_cross_over.sma_cross_over import SmaCrossoverStrategy

def generate_strategies(configuration: BacktestConfiguration) -> List[StrategyBase]:
    strategies = []
    for symbol_id in configuration.symbols_traded:
        configuration.strategy_parameters["symbol_id"] = symbol_id
        strategy = SmaCrossoverStrategy(configuration.strategy_parameters)
        strategies.append(strategy)
    return strategies