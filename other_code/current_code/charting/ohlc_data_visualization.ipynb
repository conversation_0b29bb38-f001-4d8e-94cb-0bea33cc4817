{"cells": [{"cell_type": "code", "execution_count": null, "id": "b4ada1a2", "metadata": {}, "outputs": [], "source": ["import polars as pl"]}, {"cell_type": "code", "execution_count": null, "id": "d2695460", "metadata": {}, "outputs": [], "source": ["file_path = \"sample_fx_daily_data.parquet\"\n", "df = pl.read_parquet(file_path)"]}, {"cell_type": "code", "execution_count": null, "id": "9983c26a", "metadata": {}, "outputs": [], "source": ["#chart the data in df\n", "from mattlibrary.visualizations.candlestick_chart import plot_candlestick_chart\n", "plot_candlestick_chart(df, [\"datetime\", \"open\", \"high\", \"low\", \"close\"], \"AUDUSD\", \"Date\", \"Price\", \"0,0.00000\")"]}], "metadata": {"kernelspec": {"display_name": "python_development", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 5}