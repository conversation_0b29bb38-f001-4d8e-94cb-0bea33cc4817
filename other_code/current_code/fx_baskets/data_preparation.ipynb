{"cells": [{"cell_type": "code", "execution_count": null, "id": "45234534", "metadata": {}, "outputs": [], "source": ["%reload_ext autoreload\n", "%autoreload 2\n", "\n", "import os\n", "import datetime\n", "import polars as pl\n", "import pandas as pd\n", "import polars.selectors as cs\n", "import lightgbm as lgb\n", "from mattlibrary.trading.basket import Basket\n", "\n", "symbols_ids=['AUDUSD', 'AUDNZD', 'AUDCAD', 'AUDCHF', 'AUDJPY', 'EURAUD', 'GBPAUD',\n", "                'NZDUSD', 'NZDCAD', 'NZDCHF', 'NZDJPY', 'EURNZD', 'GBPNZD', \n", "                'CADCHF', 'CADJPY', 'EURCAD', 'GBPCAD', 'USDCAD',\n", "                'CHFJPY', 'EURCHF', 'GBPCHF', 'USDCHF',\n", "                'EURJPY', 'EURUSD', 'EURGBP', \n", "                'GBPJPY', 'GBPUSD',\n", "                'USDJPY']\n", "\n", "baskets = {\n", "    \"EUR\": Basket(\"EUR\", [\"EURUSD\", \"EURJPY\", \"EURGBP\", \"EURAUD\", \"EURNZD\", \"EURCAD\", \"EURCHF\"]),\n", "    \"GBP\": Basket(\"GBP\", [\"GBPUSD\", \"GBPJPY\", \"EURGBP\", \"GBPAUD\", \"GBPNZD\", \"GBPCAD\", \"GBPCHF\"]),\n", "    \"AUD\": Basket(\"AUD\", [\"AUDUSD\", \"AUDJPY\", \"EURAUD\", \"GBPAUD\", \"AUDNZD\", \"AUDCAD\", \"AUDCHF\"]),\n", "    \"NZD\": Basket(\"NZD\", [\"NZDUSD\", \"NZDJPY\", \"EURNZD\", \"GBPNZD\", \"AUDNZD\", \"NZDCAD\", \"NZDCHF\"]),\n", "    \"USD\": Basket(\"USD\", [\"USDJPY\", \"USDCAD\", \"USDCHF\", \"EURUSD\", \"GBPUSD\", \"AUDUSD\", \"NZDUSD\"]),\n", "    \"CAD\": Basket(\"CAD\", [\"CADJPY\", \"USDCAD\", \"EURCAD\", \"GBPCAD\", \"AUDCAD\", \"NZDCAD\", \"CADCHF\"]),\n", "    \"CHF\": Basket(\"CHF\", [\"CHFJPY\", \"USDCHF\", \"EURCHF\", \"GBPCHF\", \"AUDCHF\", \"NZDCHF\", \"CADCHF\"]),\n", "    \"JPY\": Basket(\"JPY\", [\"USDJPY\", \"EURJPY\", \"GBPJPY\", \"AUDJPY\", \"NZDJPY\", \"CADJPY\", \"CHFJPY\"])\n", "}\n", "\n", "path_fx_pairs = \"/home/<USER>/development/python_development/data/fx_pairs/dukascopy_fx_pairs_ohlc.parquet\"\n", "path_bonds = \"/home/<USER>/development/python_development/data/bond_data/bond_data.parquet\"\n", "path_equity_indexes = \"/home/<USER>/development/python_development/data/equity_indexes/equity_indexes.parquet\"\n", "path_commodities = \"/home/<USER>/development/python_development/data/commodities/commodities.parquet\"\n", "path_econ_indicators = \"/home/<USER>/development/python_development/data/econ_indicators/econ_indicators.parquet\""]}, {"cell_type": "code", "execution_count": null, "id": "c2267a23", "metadata": {}, "outputs": [], "source": ["#raw fx pairs pricing data\n", "df_fx_prices = pl.read_parquet(path_fx_pairs).select(\n", "    pl.col(\"datetime\"),\n", "    pl.col(pl.Float32, pl.Float64).cast(pl.Float64))"]}, {"cell_type": "code", "execution_count": null, "id": "4431622a", "metadata": {}, "outputs": [], "source": ["df_baskets = None\n", "\n", "#convert data in dataframe to log returns\n", "for basket_id, basket in baskets.items():\n", "        \n", "    temp_df = df_fx_prices.select(pl.col(\"datetime\"))\n", "    basket_series = basket.build_basket_series_from_prices(df_fx_prices, prefix=\"fx_\", suffix=\"_close\").alias(f\"basket_{basket_id}\")\n", "    temp_df = temp_df.with_columns(basket_series)\n", "\n", "    if df_baskets is None:\n", "        df_baskets = temp_df\n", "    else:\n", "        df_baskets= df_baskets.join(temp_df, on=\"datetime\", how=\"full\", coalesce=True).sort(\"datetime\")"]}, {"cell_type": "code", "execution_count": null, "id": "29792cc5", "metadata": {}, "outputs": [], "source": ["#log return between t and t-1\n", "def log_return_at_t_expr(column_name:str, shift:int) -> pl.Expr:\n", "    return (pl.col(column_name).shift(shift) / pl.col(column_name).shift(shift+1)).log()\n", "\n", "#log return between two columns\n", "def log_return_two_columns_expr(column_name_from:str, column_name_to:str, shift_from:int, shift_to:int) -> pl.Expr:\n", "    return (pl.col(column_name_to).shift(shift_to) / pl.col(column_name_from).shift(shift_from)).log()\n", "\n", "#rolling standard deviation (optionally convert to log returns first)\n", "def rolling_std_dev_expr(column_name:str, window_size:int, convert_to_log_returns:bool) -> pl.Expr:\n", "    if convert_to_log_returns:\n", "        return (pl.col(column_name) / pl.col(column_name).shift(1)).log().rolling_std(window_size=window_size)\n", "    else:   \n", "        return pl.col(column_name).rolling_std(window_size=window_size)\n", "    \n", "#log difference between column and its rolling mean of a given window size\n", "def log_deviation_from_windowed_mean_expr(column_name:str, window_size:int) -> pl.Expr:\n", "    return (pl.col(column_name) / pl.col(column_name).rolling_mean(window_size=window_size)).log()"]}, {"cell_type": "code", "execution_count": null, "id": "6322d7a3", "metadata": {}, "outputs": [], "source": ["#fx symbols features\n", "df_fx_features = df_fx_prices.select(pl.col(\"datetime\"))\n", "series = []\n", "\n", "for symbol_id in symbols_ids:\n", "    col_open = f\"fx_{symbol_id}_open\"\n", "    col_high = f\"fx_{symbol_id}_high\"\n", "    col_low = f\"fx_{symbol_id}_low\"\n", "    col_close = f\"fx_{symbol_id}_close\"\n", "\n", "    #log returns\n", "    for t_lookback in [0, 1, 2, 3, 4]:\n", "        series.append(df_fx_prices.select(log_return_at_t_expr(col_close, t_lookback).alias(f\"{symbol_id}_log_return_t{t_lookback}\")).to_series())\n", "\n", "    #high-low log return and open-close\n", "    series.append(df_fx_prices.select(log_return_two_columns_expr(col_low, col_high, 0, 0).alias(f\"{symbol_id}_high_low\")).to_series())\n", "    series.append(df_fx_prices.select(log_return_two_columns_expr(col_open, col_close, 0, 0).alias(f\"{symbol_id}_open_close\")).to_series())\n", "\n", "    #standard deviation\n", "    series.append(df_fx_prices.select(rolling_std_dev_expr(col_close, 20, True).alias(f\"{symbol_id}_stdev_20\")).to_series())\n", "\n", "    #log difference between close and rolling_mean of x days\n", "    for window in [5, 14, 30]:\n", "        series.append(df_fx_prices.select(log_deviation_from_windowed_mean_expr(col_close, window).alias(f\"{symbol_id}_mean_{window}\")).to_series())\n", "\n", "#add all computed series to dataframe\n", "df_fx_features = df_fx_features.hstack(series)"]}, {"cell_type": "code", "execution_count": null, "id": "7d86de1f", "metadata": {}, "outputs": [], "source": ["#basket features\n", "df_basket_features = df_baskets.select(pl.col(\"datetime\"))\n", "series = []\n", "\n", "for basket_id in baskets.keys():\n", "    col = f\"basket_{basket_id}\"\n", "\n", "    #log returns\n", "    for t_lookback in [0, 1, 2, 3, 4]:\n", "        series.append(df_baskets.select(log_return_at_t_expr(col, t_lookback).alias(f\"{basket_id}_log_return_t{t_lookback}\")).to_series())\n", "\n", "    #standard deviation\n", "    for window in [5, 10, 20]:\n", "        series.append(df_baskets.select(rolling_std_dev_expr(col, window, True).alias(f\"{basket_id}_stdev_{window}\")).to_series())\n", "\n", "    #log difference between close and rolling_mean of x days\n", "    for window in [5, 14, 30]:\n", "        series.append(df_baskets.select(log_deviation_from_windowed_mean_expr(col, window).alias(f\"{basket_id}_mean_{window}\")).to_series())\n", "\n", "#add all computed series to dataframe\n", "df_basket_features = df_basket_features.hstack(series)"]}, {"cell_type": "code", "execution_count": null, "id": "ee8848e2", "metadata": {}, "outputs": [], "source": ["#commodities\n", "df_commodities = pl.read_parquet(path_commodities)\n", "df_commodity_features = df_commodities.select(pl.col(\"datetime\"))\n", "series = []\n", "\n", "for commodity_id in df_commodities.columns[1:]:\n", "    col = commodity_id\n", "\n", "    #log returns\n", "    for t_lookback in [0, 1, 2, 3, 4]:\n", "        series.append(df_commodities.select(log_return_at_t_expr(col, t_lookback).alias(f\"{commodity_id}_log_return_t{t_lookback}\")).to_series())\n", "\n", "    #standard deviation\n", "    for window in [5, 10, 20]:\n", "        series.append(df_commodities.select(rolling_std_dev_expr(col, window, True).alias(f\"{commodity_id}_stdev_{window}\")).to_series())\n", "\n", "    #log difference between close and rolling_mean of x days\n", "    for window in [5, 14, 30]:\n", "        series.append(df_commodities.select(log_deviation_from_windowed_mean_expr(col, window).alias(f\"{commodity_id}_mean_{window}\")).to_series())\n", "\n", "#add all computed series to dataframe\n", "df_commodity_features = df_commodity_features.hstack(series)\n"]}, {"cell_type": "code", "execution_count": null, "id": "ddcfafc5", "metadata": {}, "outputs": [], "source": ["#equity indexes\n", "df_equity_indexes = pl.read_parquet(path_equity_indexes)\n", "df_equity_index_features = df_equity_indexes.select(pl.col(\"datetime\"))\n", "series = []\n", "\n", "for equity_index_id in df_equity_indexes.columns[1:]:\n", "    col = equity_index_id\n", "\n", "    #log returns\n", "    for t_lookback in [0, 1, 2, 3, 4]:\n", "        series.append(df_equity_indexes.select(log_return_at_t_expr(col, t_lookback).alias(f\"{equity_index_id}_log_return_t{t_lookback}\")).to_series())\n", "\n", "    #standard deviation\n", "    for window in [5, 10, 20]:\n", "        series.append(df_equity_indexes.select(rolling_std_dev_expr(col, window, True).alias(f\"{equity_index_id}_stdev_{window}\")).to_series())\n", "\n", "    #log difference between close and rolling_mean of x days\n", "    for window in [5, 14, 30]:\n", "        series.append(df_equity_indexes.select(log_deviation_from_windowed_mean_expr(col, window).alias(f\"{equity_index_id}_mean_{window}\")).to_series())\n", "\n", "#add all computed series to dataframe\n", "df_equity_index_features = df_equity_index_features.hstack(series)\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "3706bf6a", "metadata": {}, "outputs": [], "source": ["#bonds\n", "df_bonds = pl.read_parquet(path_bonds)\n", "df_bond_features = df_bonds.select(pl.col(\"datetime\"))\n", "series = []\n", "\n", "for bond_id in df_bonds.columns[1:]:\n", "    col = bond_id\n", "\n", "    #differences\n", "    for t_lookback in [0, 1, 2, 3, 4]:\n", "        series.append(df_bonds.select((pl.col(col).shift(t_lookback) - pl.col(col).shift(t_lookback+1)).alias(f\"{bond_id}_diff_t{t_lookback}\")).to_series())\n", "\n", "    #standard deviation\n", "    for window in [5, 10, 20]:\n", "        series.append(df_bonds.select((pl.col(col).rolling_std(window_size=window)).alias(f\"{bond_id}_stdev_{window}\")).to_series())\n", "\n", "    #difference between current value and rolling_mean of x days\n", "    for window in [5, 14, 30]:\n", "        series.append(df_bonds.select((pl.col(col) - pl.col(col).rolling_mean(window_size=window)).alias(f\"{bond_id}_mean_{window}\")).to_series())\n", "\n", "#add all computed series to dataframe\n", "df_bond_features = df_bond_features.hstack(series)"]}, {"cell_type": "code", "execution_count": null, "id": "e4e1e747", "metadata": {}, "outputs": [], "source": ["#econ indicators\n", "df_econ = pl.read_parquet(path_econ_indicators)\n", "df_econ_features = df_econ.clone()"]}, {"cell_type": "code", "execution_count": null, "id": "f3d21cdd", "metadata": {}, "outputs": [], "source": ["#join, keep all columns of all features\n", "df = df_fx_features.join(\n", "    df_basket_features, on=\"datetime\", how=\"full\", coalesce=True).join(\n", "        df_commodity_features, on=\"datetime\", how=\"full\", coalesce=True).join(\n", "            df_equity_index_features, on=\"datetime\", how=\"full\", coalesce=True).join(\n", "                df_bond_features, on=\"datetime\", how=\"full\", coalesce=True).join(\n", "                    df_econ_features, on=\"datetime\", how=\"full\", coalesce=True).sort(\"datetime\")"]}, {"cell_type": "code", "execution_count": null, "id": "3061d48e", "metadata": {}, "outputs": [], "source": ["# obtain columns that dont have data before a certain cutoff date\n", "datetime_cutoff = datetime.datetime(2005, 1, 1)\n", "columns_to_be_removed = {}\n", "timestamp_column_name = \"datetime\"\n", "\n", "for col_name in df.columns:\n", "    if col_name == timestamp_column_name:\n", "        continue\n", "\n", "    # Filter the DataFrame to include only rows where the current feature column is not null\n", "    # Then select the timestamp column from this filtered view and get its first value\n", "    coinciding_datetime = df.filter(pl.col(col_name).is_not_null()).select(\n", "        pl.col(timestamp_column_name).first()\n", "    ).item() # .item() extracts the single scalar value from the resulting Series\n", "\n", "    if coinciding_datetime >= datetime_cutoff:\n", "        columns_to_be_removed[col_name] = coinciding_datetime\n", "\n", "# Print the collected columns_to_be_removed\n", "for col, dt in columns_to_be_removed.items():\n", "    if dt is None:\n", "        print(f\"Remocing Column '{col}': No coinciding datetime (either column is all null, or '{timestamp_column_name}' is null at that point).\")\n", "    else:\n", "        print(f\"Removing Column '{col}': {dt}\")\n", "\n", "#Remove all columns that have data that do not go back far enough\n", "df = df.drop(columns_to_be_removed.keys())    "]}, {"cell_type": "code", "execution_count": null, "id": "8d94546f", "metadata": {}, "outputs": [], "source": ["#fill forward econ data (has to happen with all dates in dataframe)\n", "df = df.select(~cs.contains(\"econ_\"), cs.contains(\"econ_\").fill_null(strategy=\"forward\"))"]}, {"cell_type": "code", "execution_count": null, "id": "71e772c4", "metadata": {}, "outputs": [], "source": ["#generate feature dataframes for each currency pair\n", "forecast_horizon = [1, 2, 3, 4, 5]\n", "features_targets = dict()\n", "\n", "#features for fx pair training\n", "for currency_pair in symbols_ids:\n", "    #currency symbols\n", "    currency1 = currency_pair[0:3]\n", "    currency2 = currency_pair[3:6]\n", "\n", "    #filter columns that contain one of the two currency symbols\n", "    currency_features = df.select(\n", "        pl.col(\"datetime\"), \n", "        cs.contains(\"commodity_\"),\n", "        cs.contains(currency1, currency2)).sort(\"datetime\")\n", "\n", "    #generate targets\n", "    currency_targets = df_fx_prices.select(pl.col(\"datetime\"))\n", "    series = []\n", "\n", "    for horizon in forecast_horizon:\n", "        series.append(df_fx_prices.select(\n", "            ((pl.col(f\"fx_{currency_pair}_close\") / pl.col(f\"fx_{currency_pair}_close\").shift(1)).log()).shift(-horizon).alias(f\"target_{horizon}\")).to_series())\n", "        \n", "    currency_targets = currency_targets.hstack(series)\n", "    currency_features = currency_features.join(currency_targets, on=\"datetime\", how=\"left\")\n", "    \n", "    #add currency features to dictionary\n", "    features_targets[currency_pair] = currency_features.drop_nulls().sort(\"datetime\")\n", "\n", "#features for currency basket training\n", "for basket_id in baskets.keys():\n", "    basket_features = df.select(\n", "        pl.col(\"datetime\"), \n", "        cs.contains(\"commodity_\"),\n", "        cs.contains(basket_id)).sort(\"datetime\")\n", "    \n", "    #generate targets\n", "    basket_targets = df_baskets.select(pl.col(\"datetime\"))\n", "    series = []\n", "\n", "    for horizon in forecast_horizon:\n", "        series.append(df_baskets.select(\n", "            (pl.col(f\"basket_{basket_id}\") / pl.col(f\"basket_{basket_id}\").shift(1)).log().shift(-horizon).alias(f\"target_{horizon}\")).to_series())\n", "        \n", "    basket_targets = basket_targets.hstack(series)\n", "    basket_features = basket_features.join(basket_targets, on=\"datetime\", how=\"left\")\n", "\n", "    #add basket features to dictionary\n", "    features_targets[basket_id] = basket_features.drop_nulls().sort(\"datetime\")"]}, {"cell_type": "code", "execution_count": null, "id": "565e1003", "metadata": {}, "outputs": [], "source": ["#split data into train and test\n", "ratio = 0.8\n", "\n", "data = dict()\n", "\n", "for symbol in features_targets.keys():\n", "\n", "    #split into training and test data\n", "    row_count = features_targets[symbol].height\n", "    split_idx = int(ratio * row_count)\n", "    train_df = features_targets[symbol].slice(0, split_idx)\n", "    test_df = features_targets[symbol].slice(split_idx, row_count - split_idx)\n", "    \n", "    #split into features and targets\n", "    train_features = train_df.select(~cs.contains(\"target_\")).drop(\"datetime\")\n", "    train_targets = train_df.select(cs.contains(\"target_\"))\n", "    test_features = test_df.select(~cs.contains(\"target_\")).drop(\"datetime\")\n", "    test_targets = test_df.select(cs.contains(\"target_\"))\n", "\n", "    #add to dictionary\n", "    data[symbol] = {\"train_features\": train_features, \"train_targets\": train_targets, \"test_features\": test_features, \"test_targets\": test_targets}"]}, {"cell_type": "code", "execution_count": null, "id": "1afebcd2", "metadata": {}, "outputs": [], "source": ["#training, finally!!!\n", "symbol = \"JPY\"\n", "target_windows = [1, 2, 3, 4, 5]\n", "models = {}\n", "predictions = {}\n", "\n", "#iterate over target columns and train separate models\n", "for target_window in target_windows:\n", "\n", "    train_features = data[symbol][\"train_features\"].to_pandas()\n", "    train_targets = data[symbol][\"train_targets\"].select(f\"target_{target_window}\").to_pandas()\n", "    test_features = data[symbol][\"test_features\"].to_pandas()\n", "    test_targets = data[symbol][\"test_targets\"].select(f\"target_{target_window}\").to_pandas()\n", "\n", "    # #train model\n", "    model = lgb.LGBMRegressor(objective='regression', random_state=42)\n", "    model.fit(train_features, train_targets)\n", "    models[f\"{symbol}_{target_window}\"] = model\n", "\n", "    #predict\n", "    predictions[f\"{symbol}_{target_window}\"] = model.predict(test_features)\n", "\n", "    #add actual values to dictionary\n", "    predictions[f\"{symbol}_{target_window}_actual\"] = test_targets.to_numpy().flatten()\n", "\n", "#create a polars dataframe that contains the different windows as columns\n", "df_predictions = pl.DataFrame(predictions)"]}, {"cell_type": "code", "execution_count": null, "id": "289f0e31", "metadata": {}, "outputs": [], "source": ["#output for each window how many predictions are correct (as defined by matching sign)\n", "for window in target_windows:\n", "\n", "    prediction = df_predictions[f\"{symbol}_{window}\"]\n", "    actual = df_predictions[f\"{symbol}_{window}_actual\"]\n", "    correct = (prediction * actual > 0).sum()\n", "    total = prediction.len()\n", "    print(f\"Window {window}: {correct} / {total} = {correct / total:.2%}\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "eba5bc46", "metadata": {}, "outputs": [], "source": ["model = models[\"AUD_1\"]\n", "\n", "feature_importances = pd.Series(model.feature_importances_, index=model.feature_name_).sort_values(ascending=False)\n", "print(\"\\nFeature Importances (sorted):\")\n", "print(feature_importances.head(50))"]}], "metadata": {"kernelspec": {"display_name": "python_development (3.13.3)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 5}