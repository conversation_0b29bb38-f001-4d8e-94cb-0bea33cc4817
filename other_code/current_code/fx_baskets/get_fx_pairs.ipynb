{"cells": [{"cell_type": "code", "execution_count": null, "id": "484e110e", "metadata": {}, "outputs": [], "source": ["import datetime\n", "import polars as pl\n", "from mattlibrary.datamanagement.polars_db import PolarsDataFrameManager\n", "\n", "data_directory = \"/mnt/sambashare/nas/storage/Share2_FinancialData1/Dukascopy/Currencies/dukascopy_fx_polars_db/\"\n", "dataset_name = \"dukascopy_daily_fx\"\n", "start_dt = datetime.datetime(2000, 5, 3)\n", "end_dt = datetime.datetime(2025, 12, 31)\n", "symbols=['AUDUSD', 'AUDNZD', 'AUDCAD', 'AUDCHF', 'AUDJPY', 'EURAUD', 'GBPAUD',\n", "                'NZDUSD', 'NZDCAD', 'NZDCHF', 'NZDJPY', 'EURNZD', 'GBPNZD', \n", "                'CADCHF', 'CADJPY', 'EURCAD', 'GBPCAD', 'USDCAD',\n", "                'CHFJPY', 'EURCHF', 'GBPCHF', 'USDCHF',\n", "                'EURJPY', 'EURUSD', 'EURGBP', \n", "                'GBPJPY', 'GBPUSD',\n", "                'USDJPY']\n", "\n", "manager = PolarsDataFrameManager(base_directory=data_directory)"]}, {"cell_type": "code", "execution_count": null, "id": "6875306e", "metadata": {}, "outputs": [], "source": ["#iterate over each symbol and create symbol_open, symbol_high, symbol_low, symbol_close columns\n", "final_df = None\n", "for symbol in symbols: \n", "    count += 1\n", "    filter_expr = (pl.col(\"symbol\") == symbol) & (pl.col(\"datetime\").is_between(start_dt, end_dt))    \n", "    raw_df = manager.read_data(dataset_name=dataset_name, filter_expr=filter_expr)\n", "    df = raw_df.select(pl.col(\"datetime\"), pl.col(\"open\").alias(f\"{symbol}_open\"), pl.col(\"high\").alias(f\"{symbol}_high\"), pl.col(\"low\").alias(f\"{symbol}_low\"), pl.col(\"close\").alias(f\"{symbol}_close\"))\n", "    if final_df is None:\n", "        final_df = df\n", "    else:\n", "        final_df = final_df.join(df, on=\"datetime\", how=\"full\", coalesce=True)\n", "\n", "#save to parquet\n", "final_df"]}, {"cell_type": "code", "execution_count": 14, "id": "54f7c976", "metadata": {}, "outputs": [], "source": ["#save to parquet\n", "final_df.write_parquet(\"dukascopy_fx_pairs_ohlc.parquet\")"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 5}