{"cells": [{"cell_type": "code", "execution_count": null, "id": "ffb5c7df", "metadata": {}, "outputs": [], "source": ["%reload_ext autoreload\n", "%autoreload 2\n", "\n", "import datetime\n", "import polars as pl\n", "from mattlibrary.visualizations.scatter_chart import plot_scatter_chart\n", "from mattlibrary.trading.basket import Basket\n", "\n", "symbols_ids=['AUDUSD', 'AUDNZD', 'AUDCAD', 'AUDCHF', 'AUDJPY', 'EURAUD', 'GBPAUD',\n", "                'NZDUSD', 'NZDCAD', 'NZDCHF', 'NZDJPY', 'EURNZD', 'GBPNZD', \n", "                'CADCHF', 'CADJPY', 'EURCAD', 'GBPCAD', 'USDCAD',\n", "                'CHFJPY', 'EURCHF', 'GBPCHF', 'USDCHF',\n", "                'EURJPY', 'EURUSD', 'EURGBP', \n", "                'GBPJPY', 'GBPUSD',\n", "                'USDJPY']\n", "\n", "baskets = {\n", "    \"EUR\": Basket(\"EUR\", [\"EURUSD\", \"EURJPY\", \"EURGBP\", \"EURAUD\", \"EURNZD\", \"EURCAD\", \"EURCHF\"]),\n", "    \"GBP\": Basket(\"GBP\", [\"GBPUSD\", \"GBPJPY\", \"EURGBP\", \"GBPAUD\", \"GBPNZD\", \"GBPCAD\", \"GBPCHF\"]),\n", "    \"AUD\": Basket(\"AUD\", [\"AUDUSD\", \"AUDJPY\", \"EURAUD\", \"GBPAUD\", \"AUDNZD\", \"AUDCAD\", \"AUDCHF\"]),\n", "    \"NZD\": Basket(\"NZD\", [\"NZDUSD\", \"NZDJPY\", \"EURNZD\", \"GBPNZD\", \"AUDNZD\", \"NZDCAD\", \"NZDCHF\"]),\n", "    \"USD\": Basket(\"USD\", [\"USDJPY\", \"USDCAD\", \"USDCHF\", \"EURUSD\", \"GBPUSD\", \"AUDUSD\", \"NZDUSD\"]),\n", "    \"CAD\": Basket(\"CAD\", [\"CADJPY\", \"USDCAD\", \"EURCAD\", \"GBPCAD\", \"AUDCAD\", \"NZDCAD\", \"CADCHF\"]),\n", "    \"CHF\": Basket(\"CHF\", [\"CHFJPY\", \"USDCHF\", \"EURCHF\", \"GBPCHF\", \"AUDCHF\", \"NZDCHF\", \"CADCHF\"]),\n", "    \"JPY\": Basket(\"JPY\", [\"USDJPY\", \"EURJPY\", \"GBPJPY\", \"AUDJPY\", \"NZDJPY\", \"CADJPY\", \"CHFJPY\"])\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "1a384c27", "metadata": {}, "outputs": [], "source": ["#read data\n", "df_raw_prices = pl.read_parquet(\"/home/<USER>/development/python_development/data/fx_pairs/dukascopy_fx_pairs_ohlc.parquet\")\n", "\n", "#convert all data columns to float64\n", "df_raw_prices = df_raw_prices.select(pl.col(\"datetime\"),pl.col(pl.Float32, pl.Float64).cast(pl.Float64))"]}, {"cell_type": "code", "execution_count": null, "id": "aba5d846", "metadata": {}, "outputs": [], "source": ["#convert data in dataframe to log returns\n", "df_raw_log = df_raw_prices.select(\n", "    pl.col(\"datetime\"), \n", "    (pl.col(pl.Float32, pl.Float64) / pl.col(pl.Float32, pl.Float64).shift(1)).log()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "eff5a4dc", "metadata": {}, "outputs": [], "source": ["print(df_raw_prices)\n", "print(df_raw_log)"]}, {"cell_type": "code", "execution_count": null, "id": "7eadd46c", "metadata": {}, "outputs": [], "source": ["#compare an fx basket built from log returns vs fx basket built from prices\n", "fx_basket_symbol = \"AUD\"\n", "basket = baskets[fx_basket_symbol]\n", "\n", "#generate price and log df\n", "df_price = df_raw_prices.select(pl.col(\"datetime\"), basket.build_basket_series_from_prices(df_raw_prices).alias(f\"{fx_basket_symbol}_price\")).drop_nulls()\n", "df_log = df_raw_log.select(pl.col(\"datetime\"), basket.build_basket_series_from_log_returns(df_raw_log).cum_sum().alias(f\"{fx_basket_symbol}_log\")).drop_nulls()\n", "\n", "#normnalize \n", "df_price = df_price.select(pl.col(\"datetime\"), (pl.col(f\"{fx_basket_symbol}_price\")))\n", "df_log = df_log.select(pl.col(\"datetime\"), (pl.col(f\"{fx_basket_symbol}_log\")))\n", "\n", "#build df\n", "df = df_price.join(df_log, on=\"datetime\", how=\"full\", coalesce=True).drop_nulls().sort(\"datetime\")"]}, {"cell_type": "code", "execution_count": null, "id": "a4fef79f", "metadata": {}, "outputs": [], "source": ["df"]}, {"cell_type": "code", "execution_count": null, "id": "cfd12a4e", "metadata": {}, "outputs": [], "source": ["plot_scatter_chart(df, [f\"{fx_basket_symbol}_price\"], [\"orange\"], \"Fx Basket Log vs Price Returns\", \"Date\", \"Log Returns\", \"0.00000\", plot_width=1000, plot_height=600, omit_line=True)\n", "plot_scatter_chart(df, [f\"{fx_basket_symbol}_log\"], [\"orange\"], \"Fx Basket Log vs Price Returns\", \"Date\", \"Log Returns\", \"0.00000\", plot_width=1000, plot_height=600, omit_line=True)\n"]}], "metadata": {"kernelspec": {"display_name": "python_development", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 5}