{"cells": [{"cell_type": "code", "execution_count": null, "id": "497c0992", "metadata": {}, "outputs": [], "source": ["import lightgbm as lgb\n", "import numpy as np\n", "from sklearn.model_selection import train_test_split\n", "import matplotlib.pyplot as plt\n", "import pandas as pd"]}, {"cell_type": "code", "execution_count": null, "id": "9a7bf5bf", "metadata": {}, "outputs": [], "source": ["random_seed = None\n", "n_count = 10000\n", "np.random.seed(random_seed)\n", "\n", "# Create a single feature 'Feature_A'\n", "X_data = 5 * np.random.rand(n_count, 1) # Still 2D for consistency\n", "feature_names = ['Feature_A'] # Define your feature name(s)\n", "\n", "X = pd.DataFrame(np.sort(X_data, axis=0), columns=feature_names) # Convert to DataFrame with column name\n", "y = np.sin(X['Feature_A']).to_numpy() + np.random.normal(0, 0.1, X.shape[0]) * 3\n", "y[::5] += 3 * (0.5 - np.random.rand(X.shape[0] // 5)) # Add some heteroscedasticity\n", "\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=random_seed)\n", "\n", "# --- 2. Train LightGBM Models with Feature Names (implicitly handled by DataFrame) ---\n", "common_params = {\n", "    'objective': 'quantile',\n", "    'metric': 'quantile',\n", "    'n_estimators': 100,\n", "    'learning_rate': 0.1,\n", "    'verbose': -1,\n", "    'n_jobs': -1\n", "}\n", "\n", "alpha_lower = 0.05\n", "alpha_median = 0.50\n", "alpha_upper = 0.95\n", "\n", "model_lower = lgb.LGBMRegressor(**common_params, alpha=alpha_lower)\n", "model_lower.fit(X_train, y_train) # Pass DataFrame directly\n", "\n", "model_median = lgb.LGBMRegressor(**common_params, alpha=alpha_median)\n", "model_median.fit(X_train, y_train)\n", "\n", "model_upper = lgb.LGBMRegressor(**common_params, alpha=alpha_upper)\n", "model_upper.fit(X_train, y_train)\n", "\n", "# You can check the feature names recognized by the model\n", "print(f\"Feature names recognized by model_median: {model_median.feature_name_}\")\n", "print(f\"Number of features recognized: {model_median.n_features_}\\n\")\n", "\n", "\n", "# --- 3. Making a prediction for new data, *also using feature names* ---\n", "\n", "# 1. Define your new data point(s) using a DataFrame\n", "# This is where including feature names becomes explicit for prediction.\n", "new_X_single_df = pd.DataFrame([[2.5]], columns=feature_names)\n", "\n", "# Or for multiple new data points:\n", "new_X_multiple_df = pd.DataFrame([[0.1], [1.5], [3.8], [4.9]], columns=feature_names)\n", "\n", "# 2. Make predictions using each of your trained quantile models\n", "# LightGBM can directly handle Pandas DataFrames for prediction\n", "pred_lower_single = model_lower.predict(new_X_single_df)\n", "pred_median_single = model_median.predict(new_X_single_df)\n", "pred_upper_single = model_upper.predict(new_X_single_df)\n", "\n", "pred_lower_multiple = model_lower.predict(new_X_multiple_df)\n", "pred_median_multiple = model_median.predict(new_X_multiple_df)\n", "pred_upper_multiple = model_upper.predict(new_X_multiple_df)\n", "\n", "# 3. Generate the prediction interval and print\n", "print(f\"--- Prediction for single new data point ({feature_names[0]} = {new_X_single_df.iloc[0, 0]}) ---\")\n", "print(f\"Predicted Lower Bound (5th percentile): {pred_lower_single[0]:.3f}\")\n", "print(f\"Predicted Median (50th percentile):     {pred_median_single[0]:.3f}\")\n", "print(f\"Predicted Upper Bound (95th percentile): {pred_upper_single[0]:.3f}\")\n", "print(f\"90% Prediction Interval: [{pred_lower_single[0]:.3f}, {pred_upper_single[0]:.3f}]\\n\")\n", "\n", "\n", "print(\"--- Predictions and 90% Prediction Intervals for multiple new data points ---\")\n", "for i, x_val in enumerate(new_X_multiple_df[feature_names[0]]):\n", "    print(f\"For {feature_names[0]} = {x_val:.2f}:\")\n", "    print(f\"  Median Prediction: {pred_median_multiple[i]:.3f}\")\n", "    print(f\"  90% PI: [{pred_lower_multiple[i]:.3f}, {pred_upper_multiple[i]:.3f}]\")\n", "    print(\"-\" * 30)\n", "\n", "# --- Optional: Visualize the new predictions on the test set plot ---\n", "plt.figure(figsize=(10, 6))\n", "plt.scatter(X_test['Feature_A'], y_test, color='gray', label='Actual Test Values', alpha=0.7)\n", "plt.plot(X_test['Feature_A'], model_median.predict(X_test), color='blue', label='Median Prediction (Test Set)')\n", "plt.fill_between(X_test['Feature_A'].values.flatten(), model_lower.predict(X_test), model_upper.predict(X_test), color='skyblue', alpha=0.3, label='90% Prediction Interval (Test Set)')\n", "\n", "# Plot the new single prediction\n", "plt.scatter(new_X_single_df['Feature_A'], pred_median_single, color='red', marker='X', s=100, label=f'New Prediction ({feature_names[0]}={new_X_single_df.iloc[0,0]:.1f}) Median')\n", "plt.plot([new_X_single_df['Feature_A'].iloc[0], new_X_single_df['Feature_A'].iloc[0]], [pred_lower_single[0], pred_upper_single[0]], color='red', linestyle='--', linewidth=2, label='New Prediction Interval')\n", "plt.scatter(new_X_single_df['Feature_A'], pred_lower_single, color='red', marker='_', s=200)\n", "plt.scatter(new_X_single_df['Feature_A'], pred_upper_single, color='red', marker='_', s=200)\n", "\n", "# Plot the new multiple predictions\n", "plt.scatter(new_X_multiple_df['Feature_A'], pred_median_multiple, color='purple', marker='o', s=50, label='Multiple New Predictions Median')\n", "for i in range(len(new_X_multiple_df)):\n", "    plt.plot([new_X_multiple_df['Feature_A'].iloc[i], new_X_multiple_df['Feature_A'].iloc[i]], [pred_lower_multiple[i], pred_upper_multiple[i]], color='purple', linestyle=':', linewidth=1)\n", "\n", "\n", "plt.xlabel(feature_names[0]) # Use the feature name for the label\n", "plt.ylabel('y')\n", "plt.title('LightGBM Prediction Interval with New Data (with Feature Names)')\n", "plt.legend()\n", "plt.grid(True)\n", "plt.show()"]}], "metadata": {"kernelspec": {"display_name": "python_development", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 5}