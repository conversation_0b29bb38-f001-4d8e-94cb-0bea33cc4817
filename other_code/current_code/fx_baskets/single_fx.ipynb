{"cells": [{"cell_type": "code", "execution_count": 1, "id": "c4027b25", "metadata": {}, "outputs": [], "source": ["import datetime\n", "import polars as pl\n", "from mattlibrary.visualizations.scatter_chart import plot_scatter_chart"]}, {"cell_type": "code", "execution_count": 2, "id": "1148cc6c", "metadata": {}, "outputs": [], "source": ["#read data\n", "df = pl.read_parquet(\"daily_fx_data.parquet\")\n", "\n", "#sample plot\n", "# plot_scatter_chart(df, [\"NZDUSD\"], [\"orange\"], \"Single Symbol Price Chart\", \"Date\", \"Price\", \"0,0.00000\", plot_width=1000, plot_height=600)"]}, {"cell_type": "code", "execution_count": 3, "id": "8001372c", "metadata": {}, "outputs": [], "source": ["#convert data in dataframe to log returns\n", "df_log = df.select(pl.col(\"datetime\"), (pl.col(pl.Float32) / pl.col(pl.Float32).shift(1)).log().name.suffix(\"_log\")).drop_nulls()\n", "\n", "#sample plot\n", "# plot_scatter_chart(df_log, [\"NZDUSD_log\"], [\"orange\"], \"Log Returns of one symbol\", \"Date\", \"Log Returns\", \"0.00000\", plot_width=1000, plot_height=600, omit_line=True)"]}, {"cell_type": "code", "execution_count": null, "id": "d64cbe39", "metadata": {}, "outputs": [], "source": ["df_metrics = df_log.with_columns(\n", "    sum_of_logs=pl.sum_horizontal(pl.col(pl.Float32)),\n", "    sum_of_logs_cum=pl.sum_horizontal(pl.col(pl.Float32)).cum_sum(),\n", "    avg_of_logs_cum=pl.mean_horizontal(pl.col(pl.Float32)).cum_sum()\n", "    )\n", "\n", "#sample plot\n", "plot_scatter_chart(df_metrics, [\"sum_of_logs\"], [\"blue\"], \"Chart of all log returns summed\", \"Date\", \"Sum of all Log Returns\", \"0.00%\", plot_width=1000, plot_height=600)"]}, {"cell_type": "code", "execution_count": null, "id": "101fcaa9", "metadata": {}, "outputs": [], "source": ["df_metrics"]}], "metadata": {"kernelspec": {"display_name": "python_development", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 5}