{"cells": [{"cell_type": "code", "execution_count": 1, "id": "9c2da4de", "metadata": {}, "outputs": [], "source": ["%reload_ext autoreload\n", "%autoreload 2\n", "\n", "import os\n", "import sys\n", "from datetime import datetime, timedelta\n", "from zoneinfo import ZoneInfo\n", "\n", "#config for jupyter cell output\n", "# import polars as pl\n", "# pl.Config.set_tbl_rows(20)\n", "\n", "from mattlibrary.logging.logging_config import setup_logging\n", "from mattlibrary.trading.trading_engine import TradingEngine\n", "from mattlibrary.datamanagement.interactive_brokers import InteractiveBrokersAPI\n", "from mattlibrary.trading.symbol import Symbol, Forex, Stock\n", "from mattlibrary.trading.parent_orders.parent_order_standard import ParentOrderStandard\n", "from mattlibrary.trading.child_order_type import ChildOrderType\n", "from config.settings import settings\n", "\n", "#logging config\n", "setup_logging(True, \"INFO\", True, True, log_file=\"development.log\", clean_log_file=True)"]}, {"cell_type": "code", "execution_count": 2, "id": "e179ad91", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-08-15 15:15:41,352 - mattlibrary.trading.execution_data_engines.simulated_execution_engine - INFO - SimulatedExecutionEngine initialized\n", "2025-08-15 15:15:41,402 - mattlibrary.trading.fx_converter - INFO - Loaded fx conversion data (4553 rows) from data/fx_converter_data.parquet\n", "2025-08-15 15:15:41,411 - mattlibrary.trading.symbol_database - INFO - Loaded 28 symbols from data/symbol_database.parquet\n", "2025-08-15 15:15:41,412 - mattlibrary.trading.trading_engine - INFO - 1 Positions loaded from disk: data/positions.json\n", "2025-08-15 15:15:41,413 - mattlibrary.trading.trading_engine - INFO - Initialized Trading Engine [base_currency=USD, starting_balance=100000 USD, track_performance=True, execution_plugin_type=SimulatedExecutionEngine, static_data_directory=data/]\n"]}], "source": ["#trading engine\n", "trading_engine = TradingEngine(\n", "        settings.starting_balance, \n", "        settings.base_currency, \n", "        settings.track_performance, \n", "        settings.execution_plugin_type,\n", "        settings.static_data_directory,\n", "        settings.read_positions_from_disk,\n", "        settings.write_positions_to_disk\n", "    )"]}, {"cell_type": "code", "execution_count": 4, "id": "4046f4db", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-08-15 15:15:59,313 - mattlibrary.trading.fx_converter - INFO - Source FX Conversion Data is up to date. No new data to fetch.\n"]}], "source": ["#refresh fx conversion data\n", "trading_engine.fx_converter.refresh_data_source(settings.ib_address, settings.ib_port, settings.ib_client_id, settings.is_async_environment)"]}, {"cell_type": "code", "execution_count": 4, "id": "aa3eae88", "metadata": {}, "outputs": [], "source": ["import polars as pl\n", "\n", "#load fx data from parquet file (manually here)\n", "data = pl.read_parquet(\"data/fx_converter_data.parquet\")"]}, {"cell_type": "code", "execution_count": 5, "id": "c8ec2ff6", "metadata": {}, "outputs": [{"data": {"text/html": ["<div><style>\n", ".dataframe > thead > tr,\n", ".dataframe > tbody > tr {\n", "  text-align: right;\n", "  white-space: pre-wrap;\n", "}\n", "</style>\n", "<small>shape: (4_553, 8)</small><table border=\"1\" class=\"dataframe\"><thead><tr><th>Date</th><th>FOREX_AUDUSD</th><th>FOREX_NZDUSD</th><th>FOREX_USDCAD</th><th>FOREX_USDCHF</th><th>FOREX_EURUSD</th><th>FOREX_GBPUSD</th><th>FOREX_USDJPY</th></tr><tr><td>date</td><td>f64</td><td>f64</td><td>f64</td><td>f64</td><td>f64</td><td>f64</td><td>f64</td></tr></thead><tbody><tr><td>2007-11-02</td><td>0.9232</td><td>0.76545</td><td>0.9347</td><td>1.1541</td><td>1.450475</td><td>2.08955</td><td>114.86</td></tr><tr><td>2007-11-09</td><td>0.911575</td><td>0.835</td><td>0.94465</td><td>1.12255</td><td>1.467825</td><td>2.090275</td><td>110.695</td></tr><tr><td>2007-11-29</td><td>0.88135</td><td>0.7708</td><td>0.99825</td><td>1.11795</td><td>1.4744</td><td>2.0613</td><td>109.935</td></tr><tr><td>2007-11-30</td><td>0.88435</td><td>0.7643</td><td>0.99865</td><td>1.13205</td><td>1.46335</td><td>2.0563</td><td>111.2375</td></tr><tr><td>2007-12-03</td><td>0.8807</td><td>0.76375</td><td>0.99925</td><td>1.12735</td><td>1.46675</td><td>2.065875</td><td>110.465</td></tr><tr><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td></tr><tr><td>2025-08-08</td><td>0.6518</td><td>0.595805</td><td>1.375835</td><td>0.80872</td><td>1.164005</td><td>1.34508</td><td>147.736</td></tr><tr><td>2025-08-11</td><td>0.6513</td><td>0.593865</td><td>1.3779</td><td>0.81248</td><td>1.16152</td><td>1.343145</td><td>148.152</td></tr><tr><td>2025-08-12</td><td>0.652945</td><td>0.59545</td><td>1.37725</td><td>0.806635</td><td>1.167475</td><td>1.34995</td><td>147.846</td></tr><tr><td>2025-08-13</td><td>0.654545</td><td>0.59718</td><td>1.375935</td><td>0.805785</td><td>1.17057</td><td>1.35749</td><td>147.3805</td></tr><tr><td>2025-08-14</td><td>0.649435</td><td>0.591465</td><td>1.381705</td><td>0.80751</td><td>1.16475</td><td>1.353125</td><td>147.7555</td></tr></tbody></table></div>"], "text/plain": ["shape: (4_553, 8)\n", "┌────────────┬────────────┬────────────┬───────────┬───────────┬───────────┬───────────┬───────────┐\n", "│ Date       ┆ FOREX_AUDU ┆ FOREX_NZDU ┆ FOREX_USD ┆ FOREX_USD ┆ FOREX_EUR ┆ FOREX_GBP ┆ FOREX_USD │\n", "│ ---        ┆ SD         ┆ SD         ┆ CAD       ┆ CHF       ┆ USD       ┆ USD       ┆ JPY       │\n", "│ date       ┆ ---        ┆ ---        ┆ ---       ┆ ---       ┆ ---       ┆ ---       ┆ ---       │\n", "│            ┆ f64        ┆ f64        ┆ f64       ┆ f64       ┆ f64       ┆ f64       ┆ f64       │\n", "╞════════════╪════════════╪════════════╪═══════════╪═══════════╪═══════════╪═══════════╪═══════════╡\n", "│ 2007-11-02 ┆ 0.9232     ┆ 0.76545    ┆ 0.9347    ┆ 1.1541    ┆ 1.450475  ┆ 2.08955   ┆ 114.86    │\n", "│ 2007-11-09 ┆ 0.911575   ┆ 0.835      ┆ 0.94465   ┆ 1.12255   ┆ 1.467825  ┆ 2.090275  ┆ 110.695   │\n", "│ 2007-11-29 ┆ 0.88135    ┆ 0.7708     ┆ 0.99825   ┆ 1.11795   ┆ 1.4744    ┆ 2.0613    ┆ 109.935   │\n", "│ 2007-11-30 ┆ 0.88435    ┆ 0.7643     ┆ 0.99865   ┆ 1.13205   ┆ 1.46335   ┆ 2.0563    ┆ 111.2375  │\n", "│ 2007-12-03 ┆ 0.8807     ┆ 0.76375    ┆ 0.99925   ┆ 1.12735   ┆ 1.46675   ┆ 2.065875  ┆ 110.465   │\n", "│ …          ┆ …          ┆ …          ┆ …         ┆ …         ┆ …         ┆ …         ┆ …         │\n", "│ 2025-08-08 ┆ 0.6518     ┆ 0.595805   ┆ 1.375835  ┆ 0.80872   ┆ 1.164005  ┆ 1.34508   ┆ 147.736   │\n", "│ 2025-08-11 ┆ 0.6513     ┆ 0.593865   ┆ 1.3779    ┆ 0.81248   ┆ 1.16152   ┆ 1.343145  ┆ 148.152   │\n", "│ 2025-08-12 ┆ 0.652945   ┆ 0.59545    ┆ 1.37725   ┆ 0.806635  ┆ 1.167475  ┆ 1.34995   ┆ 147.846   │\n", "│ 2025-08-13 ┆ 0.654545   ┆ 0.59718    ┆ 1.375935  ┆ 0.805785  ┆ 1.17057   ┆ 1.35749   ┆ 147.3805  │\n", "│ 2025-08-14 ┆ 0.649435   ┆ 0.591465   ┆ 1.381705  ┆ 0.80751   ┆ 1.16475   ┆ 1.353125  ┆ 147.7555  │\n", "└────────────┴────────────┴────────────┴───────────┴───────────┴───────────┴───────────┴───────────┘"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["data"]}, {"cell_type": "code", "execution_count": null, "id": "796e9113", "metadata": {}, "outputs": [], "source": ["parent_order = ParentOrderStandard(\n", "    strategy_id=\"test\",\n", "    symbol_id=\"FOREX_USDJPY\",\n", "    size=100_000,\n", "    order_type=ChildOrderType.MARKET\n", ")\n", "\n", "trading_engine.submit_parent_order(parent_order)"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 5}