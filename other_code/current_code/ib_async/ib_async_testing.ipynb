{"cells": [{"cell_type": "code", "execution_count": null, "id": "56a54882", "metadata": {}, "outputs": [], "source": ["%reload_ext autoreload\n", "%autoreload 2\n", "\n", "import os\n", "import sys\n", "import datetime\n", "from ib_async import *\n", "\n", "notebook_dir = os.path.dirname(os.path.abspath('__file__')) if '__file__' in globals() else os.getcwd()\n", "project_root = os.path.abspath(os.path.join(notebook_dir, '..', '..'))\n", "if project_root not in sys.path:\n", "    sys.path.append(project_root)\n", "    print(f\"Added '{project_root}' to sys.path\")\n", "else:\n", "    print(f\"'{project_root}' already in sys.path\")\n", "\n", "\n", "import mattlibrary.trading.execution_data_engines.interactive_brokers_api as ib_api\n", "import mattlibrary.logging.logging_config as logging_config\n", "from mattlibrary.trading.symbol import Symbol\n", "\n", "#logging config\n", "logging_config.setup_logging(True, \"INFO\", True, True, log_file=\"development.log\", clean_log_file=True)\n", "\n", "clientId = 1\n", "ib_api = ib_api.InteractiveBrokersAPI(\"127.0.0.1\", 7497, clientId, True)\n", "ib_api.connect()"]}, {"cell_type": "code", "execution_count": null, "id": "c3da3dda", "metadata": {}, "outputs": [], "source": ["#historical data\n", "symbol = Symbol(\"EURUSD\", \"FX\", \"USD\")\n", "end_datetime = datetime.datetime.now()\n", "duration = \"5 d\"\n", "bar_size = \"1 day\"\n", "\n", "a = ib_api.get_historical_data(symbol, end_datetime, duration, bar_size)\n", "a"]}, {"cell_type": "code", "execution_count": null, "id": "79b5b663", "metadata": {}, "outputs": [], "source": ["#contract details\n", "symbol = Symbol(\"EURUSD\", \"FX\", \"USD\")\n", "a = ib_api.get_contract_details(symbol)\n", "a"]}, {"cell_type": "code", "execution_count": null, "id": "9fd946b8", "metadata": {}, "outputs": [], "source": ["#matching symbols\n", "symbol_string = \"yhoo\"\n", "a = ib_api.get_matching_symbols(symbol_string)\n", "a"]}, {"cell_type": "code", "execution_count": null, "id": "57b99cc3", "metadata": {}, "outputs": [], "source": ["#get ticker\n", "symbol = Symbol(\"SPX\", \"INDEX\", \"USD\")\n", "a = ib_api.get_ticker(symbol)\n", "a"]}, {"cell_type": "code", "execution_count": null, "id": "8d358b97", "metadata": {}, "outputs": [], "source": ["ib = ib_api.ib\n", "spx = Index(\"SPX\", \"CBOE\")\n", "\n", "spx = ib.qualifyContracts(Index(\"SPX\", \"CBOE\"))[0]"]}, {"cell_type": "code", "execution_count": null, "id": "a7235093", "metadata": {}, "outputs": [], "source": ["a = Forex(\"EURUSD\")\n", "b = ib.qualifyContracts(a)\n", "print(b)\n", "ib.reqTickers(b[0])"]}, {"cell_type": "code", "execution_count": null, "id": "a3f8d06b", "metadata": {}, "outputs": [], "source": ["ib_api.disconnect()"]}, {"cell_type": "code", "execution_count": null, "id": "01760c29", "metadata": {}, "outputs": [], "source": ["%reload_ext autoreload\n", "%autoreload 2\n", "\n", "import asyncio\n", "import mattlibrary.logging.logging_config as logging_config\n", "from mattlibrary.trading.execution_data_engines.interactive_brokers_api import InteractiveBrokersAPI\n", "\n", "#logging config\n", "logging_config.setup_logging(True, \"INFO\", True, True, log_file=\"development.log\", clean_log_file=True)\n", "\n", "api = InteractiveBrokersAPI(\"127.0.0.1\", 7497, 1, True)"]}, {"cell_type": "code", "execution_count": null, "id": "c812c0cb", "metadata": {}, "outputs": [], "source": ["api.disconnect()"]}], "metadata": {"kernelspec": {"display_name": "python_development", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 5}