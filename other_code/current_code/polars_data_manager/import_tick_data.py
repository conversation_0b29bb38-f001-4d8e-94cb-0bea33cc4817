import os
import polars as pl
import logging
from mattlibrary.datamanagement.polars_db import PolarsDataFrameManager
from mattlibrary.datamanagement.dukascopy import read_csv_file_to_dataframe
import mattlibrary.logging.logging_config as logging_config

# Using a temporary directory for example, change as needed
base_directory = "/mnt/c/users/matt/downloads/polars_db/"
manager = PolarsDataFrameManager(base_directory=str(base_directory))
# manager.reset_database()

log_file = os.path.join("logs", "polars_db.log")
logging_config.setup_logging(logging_enabled=True, log_level=logging.DEBUG, log_file=log_file, log_to_console=False, clean_log_file=True)

#store tick fx data in polars database
source_base_path = "/mnt/sambashare/nas/Storage/Share2_FinancialData1/Dukascopy/Currencies/Tick data/Data 2000-2021/"
dataset_name = "dukascopy_ticks_fx"
from_timezone = "UTC"
to_timezone = "UTC"
partition_strategy = ["symbol", "year", "month"]
column_mappings = [
            {"source_column_name": f"Time ({from_timezone})", "target_column_name": "datetime", "dtype": pl.Datetime, "datetime_format": "%Y.%m.%d %H:%M:%S.%3f"},
            {"source_column_name": "Bid", "target_column_name": "bid", "dtype": pl.Float32},
            {"source_column_name": "Ask", "target_column_name": "ask", "dtype": pl.Float32}
        ]

#iterate over each file in the directory and import it into the database
file_counter = 0
list_dir = os.listdir(source_base_path)

for file in list_dir:

    file_counter += 1
    print(f"Processing file {file_counter} of {len(list_dir)} - file: {file}")

    if file.endswith(".csv"):
        file_path = os.path.join(source_base_path, file)

        def process_tick_df(df: pl.DataFrame):

            symbol = file[0:6]
            dt_from = df["datetime"][0]
            dt_to = df["datetime"][-1]

            print(f"Read tick data for symbol {symbol} from {dt_from} to {dt_to}")
            df = df.with_columns(pl.lit(file[0:6]).alias("symbol"))
            df = df.select(["datetime", "symbol", "bid", "ask"])
            manager.write_data(dataset_name=dataset_name, partition_strategy=partition_strategy, df=df)
            print(f"Stored tick data for symbol {symbol} from {dt_from} to {dt_to}")
        
        read_csv_file_to_dataframe(file_path, column_mappings, from_timezone, to_timezone, process_tick_df, return_dataframe=False)

print("Done with all writing operations")