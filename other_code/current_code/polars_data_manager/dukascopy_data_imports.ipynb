{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#used to import ohlc bid and ask files in csv format to one single polars dataframe\n", "import polars as pl\n", "from mattlibrary.datamanagement.dukascopy import ohlc_bidask_directory_to_polars_dataframe\n", "\n", "source_directory = \"/mnt/sambashare/nas/Storage/Share2_FinancialData1/DukasCopy/Currencies/1 Minute Bars/2003_2023-10-31/\"\n", "target_directory = \"/home/<USER>/development/python_development/data/dukascopy_1min_fx/\"\n", "source_timezone = \"EET\"\n", "target_timezone = \"UTC\"\n", "column_mappings = [\n", "    {\"source_column_name\": \"Time (EET)\", \"target_column_name\": \"datetime\", \"dtype\": pl.Datetime, \"datetime_format\": \"%Y.%m.%d %H:%M:%S\"},\n", "    {\"source_column_name\": \"Open\", \"target_column_name\": \"open\", \"dtype\": pl.Float32},\n", "    {\"source_column_name\": \"High\", \"target_column_name\": \"high\", \"dtype\": pl.Float32},\n", "    {\"source_column_name\": \"Low\", \"target_column_name\": \"low\", \"dtype\": pl.Float32},\n", "    {\"source_column_name\": \"Close\", \"target_column_name\": \"close\", \"dtype\": pl.Float32}\n", "]\n", "\n", "df = ohlc_bidask_directory_to_polars_dataframe(source_directory, source_timezone, target_timezone, column_mappings)\n", "\n", "# #store in parquet file\n", "df.write_parquet(target_directory + \"dukascopy_1min_fx.parquet\")\n", "\n", "# #also write to csv file (comma delimited)\n", "df.write_csv(target_directory + \"dukascopy_1min_fx.csv\")"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"ename": "ModuleNotFoundError", "evalue": "No module named 'mat<PERSON><PERSON><PERSON>'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mModuleNotFoundError\u001b[39m                       <PERSON><PERSON> (most recent call last)", "\u001b[36m<PERSON>ell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[1]\u001b[39m\u001b[32m, line 1\u001b[39m\n\u001b[32m----> \u001b[39m\u001b[32m1\u001b[39m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mmattlibrary\u001b[39;00m\n", "\u001b[31mModuleNotFoundError\u001b[39m: No module named 'mattlib<PERSON>'"]}], "source": ["import mattlibrary"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#used to read an existing parquet file and import additional bid and ask ohlc files in csv format.\n", "import polars as pl\n", "from mattlibrary.datamanagement.dukascopy import ohlc_bidask_directory_to_polars_dataframe, merge_dataframes\n", "\n", "parquet_file = \"/home/<USER>/development/python_development/data/dukascopy_daily_fx/dukascopy_daily_fx.parquet\"\n", "source_directory_csv_files = \"/mnt/sambashare/nas/Storage/Share2_FinancialData1/DukasCopy/Currencies/Daily/\"\n", "target_directory = \"/home/<USER>/development/python_development/data/dukascopy_daily_fx/\"\n", "source_timezone = \"UTC\"\n", "target_timezone = \"UTC\"\n", "column_mappings = [\n", "    {\"source_column_name\": \"Time (UTC)\", \"target_column_name\": \"datetime\", \"dtype\": pl.Datetime, \"datetime_format\": \"%Y.%m.%d %H:%M:%S\"},\n", "    {\"source_column_name\": \"Open\", \"target_column_name\": \"open\", \"dtype\": pl.Float32},\n", "    {\"source_column_name\": \"High\", \"target_column_name\": \"high\", \"dtype\": pl.Float32},\n", "    {\"source_column_name\": \"Low\", \"target_column_name\": \"low\", \"dtype\": pl.Float32},\n", "    {\"source_column_name\": \"Close\", \"target_column_name\": \"close\", \"dtype\": pl.Float32}\n", "]\n", "\n", "#get dataframe from parquet file\n", "df_parquet = pl.read_parquet(parquet_file)\n", "\n", "#get dataframe from imported csv files\n", "df_csv = ohlc_bidask_directory_to_polars_dataframe(source_directory_csv_files, source_timezone, target_timezone, column_mappings)\n", "\n", "#merge the two dataframes\n", "df_merged = merge_dataframes(df_parquet, df_csv)\n", "\n", "print(f\"Parquet file has {len(df_parquet)} rows\")\n", "print(f\"CSV files have {len(df_csv)} rows\")\n", "print(f\"Merged dataframe has {len(df_merged)} rows\")\n", "\n", "#store in parquet file\n", "df_merged.write_parquet(target_directory + \"dukascopy_daily_fx.parquet\")\n", "\n", "#also write to csv file (comma delimited)\n", "df_merged.write_csv(target_directory + \"dukascopy_daily_fx.csv\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#read tick data and store them in a structured target directory (symbol-year)\n", "import polars as pl\n", "from mattlibrary.datamanagement.dukascopy import process_csv_files_by_year\n", "\n", "# Define directories\n", "source_dir = '/mnt/c/users/matt/downloads/dukas/'\n", "target_dir = '/mnt/c/users/matt/downloads/dukascopy_sorted/'\n", "\n", "# Define timezones\n", "source_timezone = 'UTC'\n", "target_timezone = 'UTC'\n", "\n", "# Define column mappings\n", "column_mappings = [\n", "    {\"source_column_name\": \"Time (UTC)\", \"target_column_name\": \"datetime\", \"dtype\": pl.Datetime, \"datetime_format\": \"%Y.%m.%d %H:%M:%S.%3f\"},\n", "    {\"source_column_name\": \"Bid\", \"target_column_name\": \"bid\", \"dtype\": pl.Float32},\n", "    {\"source_column_name\": \"Ask\", \"target_column_name\": \"ask\", \"dtype\": pl.Float32}\n", "]\n", "\n", "# Process the CSV files\n", "process_csv_files_by_year(\n", "    source_dir=source_dir,\n", "    target_dir=target_dir,\n", "    column_mappings=column_mappings,\n", "    source_timezone=source_timezone,\n", "    target_timezone=target_timezone,\n", "    batch_size=4_000_000  # Optional: adjust batch size based on your memory constraints\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#import 1-minute data bid/ask csv file and join with existing parquet file and store in parquet file\n", "import polars as pl\n", "from mattlibrary.datamanagement.dukascopy import ohlc_bidask_directory_to_polars_dataframe, merge_dataframes\n", "\n", "parquet_file = \"/home/<USER>/development/python_development/financial_data/dukascopy_1min_fx/dukascopy_1min_fx.parquet\"\n", "source_directory_csv_files = \"/mnt/sambashare/nas/Storage/Share2_FinancialData1/Dukascopy/Currencies/1 Minute Bars/2023-10-31_2025-05-01/\"\n", "target_directory = \"/home/<USER>/development/python_development/financial_data/dukascopy_1min_fx/\"\n", "source_timezone = \"UTC\"\n", "target_timezone = \"UTC\"\n", "column_mappings = [\n", "    {\"source_column_name\": \"Time (UTC)\", \"target_column_name\": \"datetime\", \"dtype\": pl.Datetime, \"datetime_format\": \"%Y.%m.%d %H:%M:%S\"},\n", "    {\"source_column_name\": \"Open\", \"target_column_name\": \"open\", \"dtype\": pl.Float32},\n", "    {\"source_column_name\": \"High\", \"target_column_name\": \"high\", \"dtype\": pl.Float32},\n", "    {\"source_column_name\": \"Low\", \"target_column_name\": \"low\", \"dtype\": pl.Float32},\n", "    {\"source_column_name\": \"Close\", \"target_column_name\": \"close\", \"dtype\": pl.Float32}\n", "]\n", "\n", "#get dataframe from parquet file\n", "df_parquet = pl.read_parquet(parquet_file)\n", "\n", "#get dataframe from imported csv files\n", "df_csv = ohlc_bidask_directory_to_polars_dataframe(source_directory_csv_files, source_timezone, target_timezone, column_mappings)\n", "\n", "#merge the two dataframes\n", "df_merged = merge_dataframes(df_parquet, df_csv)\n", "\n", "print(f\"Parquet file has {len(df_parquet)} rows\")\n", "print(f\"CSV files have {len(df_csv)} rows\")\n", "print(f\"Merged dataframe has {len(df_merged)} rows\")\n", "\n", "#store in parquet file\n", "df_merged.write_parquet(target_directory + \"dukascopy_1min_fx.parquet\")\n", "\n", "#also write to csv file (comma delimited)\n", "df_merged.write_csv(target_directory + \"dukascopy_1min_fx.csv\")"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 2}