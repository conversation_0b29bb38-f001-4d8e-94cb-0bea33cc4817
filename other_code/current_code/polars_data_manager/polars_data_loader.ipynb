{"cells": [{"cell_type": "code", "execution_count": null, "id": "b92feb2f", "metadata": {}, "outputs": [], "source": ["import datetime\n", "import polars as pl\n", "from mattlibrary.datamanagement.polars_db import PolarsDataFrameManager"]}, {"cell_type": "code", "execution_count": null, "id": "c19ddfde", "metadata": {}, "outputs": [], "source": ["data_directory = \"/mnt/sambashare/nas/storage/Share2_FinancialData1/Dukascopy/Currencies/dukascopy_fx_polars_db/\"\n", "dataset_name = \"dukascopy_daily_fx\"\n", "start_dt = datetime.datetime(2020, 1, 1)\n", "end_dt = datetime.datetime(2025, 12, 31)"]}, {"cell_type": "code", "execution_count": null, "id": "0516bc6c", "metadata": {}, "outputs": [], "source": ["manager = PolarsDataFrameManager(base_directory=data_directory)\n", "filter_expr = (pl.col(\"datetime\").is_between(start_dt, end_dt)) & (pl.col(\"symbol\") == \"AUDUSD\")    \n", "df = manager.read_data(dataset_name=dataset_name, filter_expr=filter_expr)"]}, {"cell_type": "code", "execution_count": null, "id": "45fcfb71", "metadata": {}, "outputs": [], "source": ["df"]}], "metadata": {"kernelspec": {"display_name": "python_development", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 5}