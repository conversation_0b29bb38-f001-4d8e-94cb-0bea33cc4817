{"cells": [{"cell_type": "code", "execution_count": null, "id": "df6202e8", "metadata": {}, "outputs": [], "source": ["import os\n", "import polars as pl\n", "import logging\n", "from mattlibrary.datamanagement.polars_db import PolarsDataFrameManager\n", "from mattlibrary.datamanagement.dukascopy import read_csv_file_to_dataframe\n", "import mattlibrary.logging.logging_config as logging_config\n", "\n", "# Using a temporary directory for example, change as needed\n", "base_directory = \"/mnt/sambashare/nas/storage/Share2_FinancialData1/Dukascopy/Currencies/dukascopy_fx_polars_db/\"\n", "manager = PolarsDataFrameManager(base_directory=str(base_directory))\n", "# manager.reset_database()\n", "\n", "log_file = os.path.join(\"logs\", \"polars_db.log\")\n", "logging_config.setup_logging(logging_enabled=True, log_level=logging.DEBUG, log_file=log_file, console_output=False, clean_log_file=True)"]}, {"cell_type": "code", "execution_count": null, "id": "4b372f0f", "metadata": {}, "outputs": [], "source": ["#store daily fx data in polars database\n", "file_path = \"/home/<USER>/development/python_development/financial_data/dukascopy_daily_fx/dukascopy_daily_fx.parquet\"\n", "source_df = pl.read_parquet(file_path)\n", "\n", "dataset_name = \"dukascopy_daily_fx\"\n", "partition_strategy = []\n", "manager.write_data(dataset_name=dataset_name, partition_strategy=partition_strategy, df=source_df)"]}, {"cell_type": "code", "execution_count": null, "id": "195682cd", "metadata": {}, "outputs": [], "source": ["#store 1-minute fx data in polars database\n", "file_path = \"/home/<USER>/development/python_development/financial_data/dukascopy_1min_fx/dukascopy_1min_fx.parquet\"\n", "source_df = pl.read_parquet(file_path)\n", "\n", "dataset_name = \"dukascopy_1min_fx\"\n", "partition_strategy = [\"symbol\"]\n", "manager.write_data(dataset_name=dataset_name, partition_strategy=partition_strategy, df=source_df)"]}, {"cell_type": "code", "execution_count": null, "id": "1fda64b9", "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "#store tick fx data in polars database\n", "source_base_path = \"/mnt/sambashare/nas/Storage/Share2_FinancialData1/Dukascopy/Currencies/Tick data/Data 2022 - 2023/\"\n", "dataset_name = \"dukascopy_ticks_fx\"\n", "from_timezone = \"EET\"\n", "to_timezone = \"UTC\"\n", "partition_strategy = [\"symbol\", \"year\", \"month\"]\n", "column_mappings = [\n", "            {\"source_column_name\": f\"Time ({from_timezone})\", \"target_column_name\": \"datetime\", \"dtype\": pl.Datetime, \"datetime_format\": \"%Y.%m.%d %H:%M:%S.%3f\"},\n", "            {\"source_column_name\": \"Bid\", \"target_column_name\": \"bid\", \"dtype\": pl.Float32},\n", "            {\"source_column_name\": \"Ask\", \"target_column_name\": \"ask\", \"dtype\": pl.Float32}\n", "        ]\n", "\n", "#iterate over each file in the directory and import it into the database\n", "for file in os.listdir(source_base_path):\n", "    if file.endswith(\".csv\"):\n", "        file_path = os.path.join(source_base_path, file)\n", "\n", "        def process_tick_df(df: pl.DataFrame):\n", "\n", "            symbol = file[0:6]\n", "            dt_from = df[\"datetime\"][0]\n", "            dt_to = df[\"datetime\"][-1]\n", "\n", "            print(f\"Read tick data for symbol {symbol} from {dt_from} to {dt_to}\")\n", "            df = df.with_columns(pl.lit(file[0:6]).alias(\"symbol\"))\n", "            df = df.select([\"datetime\", \"symbol\", \"bid\", \"ask\"])\n", "            manager.write_data(dataset_name=dataset_name, partition_strategy=partition_strategy, df=df)\n", "            print(f\"Stored tick data for symbol {symbol} from {dt_from} to {dt_to}\")\n", "        \n", "        read_csv_file_to_dataframe(file_path, column_mappings, from_timezone, to_timezone, process_tick_df, return_dataframe=False)\n", "\n", "print(\"Done with all writing operations\")"]}, {"cell_type": "code", "execution_count": null, "id": "52cda4ed", "metadata": {}, "outputs": [], "source": ["%%time\n", "#read data from the database\n", "dataset_name = \"dukascopy_daily_fx\"\n", "filter_expr = pl.col(\"symbol\").is_in([\"EURUSD\", \"AUDUSD\"]) & (pl.col(\"datetime\").is_between(pl.datetime(2020, 12, 25, 0, 0, 0), pl.datetime(2025, 1, 10, 0, 0, 0)))\n", "df = manager.read_data(dataset_name=dataset_name, filter_expr=filter_expr)\n", "df"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 5}