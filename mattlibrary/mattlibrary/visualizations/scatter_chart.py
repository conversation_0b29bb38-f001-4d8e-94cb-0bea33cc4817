from datetime import timedelta
import polars as pl
from bokeh.plotting import figure, show, output_notebook, ColumnDataSource
from bokeh.models import HoverTool, CrosshairTool, NumeralTickFormatter, Span, CustomJS, Arrow, VeeHead
from bokeh.io import curdoc
from bokeh.events import LODEnd
from bokeh.palettes import Category10

def plot_scatter_chart(df: pl.DataFrame,
                    series_names: list[str],
                    series_colors: list[str],
                    title: str,
                    x_axis_label: str,
                    y_axis_label: str,
                    yaxis_format: str,
                    plot_width: int = 600,
                    plot_height: int = 400,
                    omit_line: bool = False,
                    annotations: pl.DataFrame = None):
    """Create a 2D line/scatter chart with multiple series using Bokeh.

    Args:
        df: Polars DataFrame containing the data. First column must be x-values (datetime or numeric),
            subsequent columns must be y-values for each series.
        series_names: A list of strings representing the column names to be used for each time series.
        series_colors: A list of strings representing the colors for each series, mapping to series_names
                      by position in the list. If not provided for a series, it will use a default color.
        title: Chart title.
        x_axis_label: Label for x-axis.
        y_axis_label: Label for y-axis.
        yaxis_format: Format string for y-axis labels (e.g., '0,0.00').
        plot_width: Width of the plot in pixels.
        plot_height: Height of the plot in pixels.
        omit_line: If True, only scatter points are shown without connecting lines.
    """
    # --- Input Validation ---
    if len(df.columns) < 2:
        raise ValueError("DataFrame must have at least 2 columns (x values and at least one series)")
    if len(series_colors) != len(series_names):
        raise ValueError(f"Number of series colors ({len(series_colors)}) must match number of series names ({len(series_names)})")

    # --- Determine X-axis type ---
    x_values = df.get_column(df.columns[0])
    x_axis_type = "auto"
    if pl.Series(x_values).dtype in [pl.Date, pl.Datetime]:
        x_axis_type = "datetime"
        tooltip_x_format = "@x{%Y-%m-%d %H:%M:%S.%3N}"
        hover_formatters = {'@x': 'datetime'}
    elif pl.Series(x_values).dtype in [pl.Int64, pl.Float64]:
        tooltip_x_format = "@x"
        hover_formatters = {}
    else:
        raise ValueError("x_values must be numeric or datetime")

    # --- Tools ---
    # Crosshair tool
    width_span = Span(dimension="width", line_width=1, line_color="grey", line_dash='dashed')
    height_span = Span(dimension="height", line_width=1, line_color="grey", line_dash='dashed')
    crosshairtool = CrosshairTool(overlay=[width_span, height_span])

    # --- Figure ---
    fig = figure(
        title=title,
        x_axis_type=x_axis_type,
        width=plot_width,
        height=plot_height,
        tools=[crosshairtool, "pan", "wheel_zoom", "box_zoom", "reset", "save"]
    )

    # add scatter/lines to renderer
    scatter_renderers = []
    for i, series_name in enumerate(series_names):
        color = series_colors[i]
        y_values = df.get_column(series_name)
        
        # Add scatter points
        scatter = fig.scatter(
            x=x_values, 
            y=y_values, 
            color=color, 
            size=5, 
            marker="circle", 
            legend_label=series_name, 
            name=series_name
        )

        scatter_renderers.append(scatter)
        
        # Add line if not omitted
        if not omit_line:
            fig.line(
                x=x_values, 
                y=y_values, 
                color=color, 
                line_width=2, 
                legend_label=series_name, 
                name=series_name
            )  

    #add annotations if supplied
    if annotations is not None:
        
        for row in annotations.iter_rows(named=True):

            if row['size'] > 0 and row['annotation'] == 'core_order':
                #up
                arrow_color = 'green'
                arrow_head_size = 15 # pixels
                arrow = Arrow(
                    x_start=row['x'], y_start=row['y'],
                    x_end=row['x'], y_end=row['y'] * 1.003,
                    end=VeeHead(size=arrow_head_size, fill_color=arrow_color, line_color=arrow_color),
                    line_color=arrow_color, line_width=2
                )
            elif row['size'] < 0 and row['annotation'] == 'core_order':
                #down
                arrow_color = 'red'
                arrow_head_size = 15 # pixels
                arrow = Arrow(
                    x_start=row['x'], y_start=row['y'],
                    x_end=row['x'], y_end=row['y'] * 0.997,
                    end=VeeHead(size=arrow_head_size, fill_color=arrow_color, line_color=arrow_color),
                    line_color=arrow_color, line_width=2
                )
            elif row['annotation'] == 'profit_target':
                arrow_color = 'green'
                arrow_head_size = 15 # pixels
                arrow = Arrow(
                    x_start=row['x'], y_start=row['y'],
                    x_end=row['x'] + timedelta(days=2), y_end=row['y'],
                    end=VeeHead(size=arrow_head_size, fill_color=arrow_color, line_color=arrow_color),
                    line_color=arrow_color, line_width=2
                )
            elif row['annotation'] == 'stop_loss':
                arrow_color = 'red'
                arrow_head_size = 15 # pixels
                arrow = Arrow(
                    x_start=row['x'], y_start=row['y'],
                    x_end=row['x'] + timedelta(days=2), y_end=row['y'],
                    end=VeeHead(size=arrow_head_size, fill_color=arrow_color, line_color=arrow_color),
                    line_color=arrow_color, line_width=2
                )
            else:
                continue

            fig.add_layout(arrow)


    
    #add hovertool
    hover_tool = HoverTool(
        tooltips=[
            ('Series', '$name'),
            ('X', tooltip_x_format),
            ('Y', '@y{' + yaxis_format + '}')
        ],
        formatters=hover_formatters,
        renderers=scatter_renderers,
        mode='mouse')         # Show the nearest point on the line)
    
    fig.add_tools(hover_tool)

    # legend
    fig.legend.location = "bottom_left"
    fig.legend.orientation = "horizontal"
    fig.legend.click_policy = "hide"

    # axes labels and formatters
    fig.xaxis.axis_label = x_axis_label
    fig.yaxis.axis_label = y_axis_label
    fig.yaxis.formatter = NumeralTickFormatter(format=yaxis_format)    

    # Callback for y-axis auto-scaling (only if one series)
    if len(series_names) == 1:

        callback_args=dict(
            x_data=x_values.to_list(),
            y_data=df[series_names[0]].to_list(),
            x_range=fig.x_range,
            y_range=fig.y_range)
        
        callback_code = f"""
            // Get data arrays from arguments
            const x = x_data; // Directly use the argument name
            const y = y_data; // Directly use the argument name

            const start = x_range.start;
            const end = x_range.end;
            var min_y = Infinity;
            var max_y = -Infinity;

            // Iterate through the data points
            for (let i = 0; i < x.length; i++) {{
                // Check if the x-coordinate is within the visible range
                if (x[i] >= start && x[i] <= end) {{
                    // Update min and max y values if this point is within range
                    min_y = Math.min(min_y, y[i]);
                    max_y = Math.max(max_y, y[i]);
                }}
            }}

            // Check if any points were found in the visible range
            if (min_y !== Infinity && max_y !== -Infinity) {{
                // Calculate padding (e.g., 5% of the range)
                const range = max_y - min_y;
                var padding;
                if (range <= 0) {{
                    padding = Math.abs(max_y * 0.1) || 0.1; // 10% or 0.1 fallback
                }} else {{
                    padding = range * 0.05; // 5% padding
                }}

                const new_start = min_y - padding;
                const new_end = max_y + padding;

                if (new_start < new_end) {{
                y_range.start = new_start;
                y_range.end = new_end;
                }} else {{
                y_range.start = min_y - 0.1; // Fallback
                y_range.end = max_y + 0.1;
                }}
                // y_range.change.emit(); // Optional trigger
            }} else {{
                // console.log("No data points in visible x-range.");
            }}
        """
        
        # fig.js_on_event(LODEnd, callback)
        callback = CustomJS(args=callback_args, code=callback_code)
        # fig.x_range.js_on_change('start', callback)
        fig.x_range.js_on_change('end', callback)

    # --- Theme ---
    curdoc().theme = "dark_minimal"

    # --- Output ---
    output_notebook()
    show(fig)