import polars as pl
import numpy as np
import plotly.graph_objects as go
from typing import Union

def plot_3D_surface(title:str, x_name:str, y_name:str, z_name:str,
                     data:Union[pl.DataFrame, dict], width:int=800, height:int=600,
                     colorscale:str="Viridis"):
    """Create an interactive 3D surface plot using Plotly.

    Args:
        title (str): Plot title
        x_name (str): Name of x-axis variable
        y_name (str): Name of y-axis variable
        z_name (str): Name of z-axis variable
        data (Union[pl.DataFrame, dict]): Polars DataFrame with columns for x, y, z coordinates,
                                         or dictionary containing x, y, and z data
        width (int, optional): Plot width in pixels. Defaults to 800.
        height (int, optional): Plot height in pixels. Defaults to 600.
        colorscale (str, optional): Colorscale for the surface. Defaults to "Viridis".

    Returns:
        plotly.graph_objects.Figure: The created Plotly figure object
    """

    # Extract data
    if isinstance(data, pl.DataFrame):
        # Extract data from Polars DataFrame
        x = data[x_name].to_numpy()
        y = data[y_name].to_numpy()
        z = data[z_name].to_numpy()

        # Get unique x and y values to create the grid
        unique_x = np.unique(x)
        unique_y = np.unique(y)

        # Create a grid of z values
        z_grid = np.zeros((len(unique_x), len(unique_y)))

        # Map each (x,y,z) point to the grid
        for i, xi in enumerate(unique_x):
            for j, yi in enumerate(unique_y):
                # Find the z value for this (x,y) pair
                mask = (x == xi) & (y == yi)
                if np.any(mask):
                    z_grid[i, j] = z[mask][0]

        # Use the grid for plotting
        x = unique_x
        y = unique_y
        z = z_grid
    else:
        # Original dictionary-based extraction
        x = data[x_name]
        y = data[y_name]
        z = data[z_name].reshape((len(x), len(y)))

    # Create hover text template
    hovertemplate = (
        f"{x_name}: %{{x:.3f}}<br>" +
        f"{y_name}: %{{y:.3f}}<br>" +
        f"{z_name}: %{{z:.3f}}<extra></extra>"
    )

    # Create the 3D surface plot
    fig = go.Figure(data=[go.Surface(
        x=x,
        y=y,
        z=z,
        colorscale=colorscale,
        hovertemplate=hovertemplate
    )])

    # Update layout with interactive features
    fig.update_layout(
        title=title,
        width=width,
        height=height,
        scene=dict(
            xaxis_title=x_name,
            yaxis_title=y_name,
            zaxis_title=z_name,
            # Add camera controls
            camera=dict(
                up=dict(x=0, y=0, z=1),
                center=dict(x=0, y=0, z=0),
                eye=dict(x=1.25, y=1.25, z=1.25),
                projection=dict(
                    type="perspective"
                )
            ),
            aspectratio=dict(x=1, y=1, z=0.7),
            xaxis=dict(
                showbackground=True,
                gridcolor="white",
                showgrid=True
            ),
            yaxis=dict(
                showbackground=True,
                gridcolor="white",
                showgrid=True
            ),
            zaxis=dict(
                showbackground=True,
                gridcolor="white",
                showgrid=True
            ),
            dragmode="turntable"
        ),
        # Add buttons for different views
        updatemenus=[
            dict(
                type="buttons",
                direction="left",
                buttons=[
                    dict(
                        args=[{'scene.camera.eye': dict(x=1.25, y=1.25, z=1.25)}],
                        label="3D View",
                        method="relayout"
                    ),
                    dict(
                        args=[{'scene.camera.eye': dict(x=0, y=0, z=2.5)}],
                        label="Top View",
                        method="relayout"
                    ),
                    dict(
                        args=[{'scene.camera.eye': dict(x=2.5, y=0, z=0)}],
                        label="Side View",
                        method="relayout"
                    )
                ],
                pad={"r": 10, "t": 10},
                showactive=True,
                x=0.11,
                xanchor="left",
                y=1.1,
                yanchor="top",
                # Apply the same styling that worked for candlestick chart
                bgcolor="rgb(30, 30, 30)",  # Darker background
                font=dict(color="rgb(200, 200, 200)")  # Light gray text
            ),
        ]
    )

    #render
    fig.show()