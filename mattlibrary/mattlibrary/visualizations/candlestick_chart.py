import polars as pl
import pandas as pd
from bokeh.plotting import figure, show, output_notebook, ColumnDataSource
from bokeh.models import HoverTool, CrosshairTool, NumeralTickFormatter, Span, CustomJS
from bokeh.io import curdoc
from bokeh.events import LODEnd

def plot_candlestick_chart(df: pl.DataFrame,
                          column_names: list[str],
                          title: str,
                          x_axis_label: str,
                          y_axis_label: str,
                          yaxis_format: str,
                          plot_width: int = 600,
                          plot_height: int = 400,
                          increasing_color: str = "green",
                          decreasing_color: str = "red"):
    """Create a candlestick chart using Bokeh.

    Args:
        df: Polars DataFrame containing the data. Must have 5 columns in this order:
            - datetime (x-axis)
            - open
            - high
            - low
            - close
        title: Chart title.
        x_axis_label: Label for x-axis.
        y_axis_label: Label for y-axis.
        yaxis_format: Format string for y-axis labels (e.g., '0,0.00').
        plot_width: Width of the plot in pixels.
        plot_height: Height of the plot in pixels.
        increasing_color: Color for increasing candles (close > open).
        decreasing_color: Color for decreasing candles (close < open).
    """

    # Extract data columns and create a ColumnDataSource
    date_values = df[column_names[0]].to_list()
    open_values = df[column_names[1]].to_list()
    high_values = df[column_names[2]].to_list()
    low_values = df[column_names[3]].to_list()
    close_values = df[column_names[4]].to_list()
    
    # --- Tools ---
    # Crosshair tool
    width_span = Span(dimension="width", line_width=1, line_color="grey", line_dash='dashed')
    height_span = Span(dimension="height", line_width=1, line_color="grey", line_dash='dashed')
    crosshairtool = CrosshairTool(overlay=[width_span, height_span])
    
    # Hovertool for candlestick segments (high to low)   
    candle_hover = HoverTool(
        tooltips=[
            ('Date', '@date{%F}'),
            ('Open', '@open{' + yaxis_format + '}'),
            ('High', '@high{' + yaxis_format + '}'),
            ('Low', '@low{' + yaxis_format + '}'),
            ('Close', '@close{' + yaxis_format + '}')
        ],
        formatters={'@date': 'datetime'},
        mode='vline',
        renderers=[] # Will be set after renderers are created
    )
    
    # --- Figure ---
    fig = figure(
        title=title,
        x_axis_type="datetime",
        width=plot_width,
        height=plot_height,
        tools=[crosshairtool, "pan", "wheel_zoom", "box_zoom", "reset", "save"]
    )
    
    # Set axis formatters
    fig.yaxis.formatter = NumeralTickFormatter(format=yaxis_format)
    
    # --- Candlestick Glyphs ---
    # Create separate data sources for increasing and decreasing candles
    inc_indices = [i for i, (c, o) in enumerate(zip(close_values, open_values)) if c >= o]
    dec_indices = [i for i, (c, o) in enumerate(zip(close_values, open_values)) if c < o]
    
    # Create increasing/decreasing specific data sources with all OHLC data
    inc_source = ColumnDataSource(data={
        'date': [date_values[i] for i in inc_indices],
        'open': [open_values[i] for i in inc_indices],
        'high': [high_values[i] for i in inc_indices],
        'low': [low_values[i] for i in inc_indices],
        'close': [close_values[i] for i in inc_indices]
    })
    
    dec_source = ColumnDataSource(data={
        'date': [date_values[i] for i in dec_indices],
        'open': [open_values[i] for i in dec_indices],
        'high': [high_values[i] for i in dec_indices],
        'low': [low_values[i] for i in dec_indices],
        'close': [close_values[i] for i in dec_indices]
    })
    
    # Draw the candle wicks (high to low) for each set
    fig.segment(x0='date', y0='high', x1='date', y1='low', source=inc_source, color='white')
    fig.segment(x0='date', y0='high', x1='date', y1='low', source=dec_source, color='white')
    
    # Draw the candle bodies with width as a timedelta
    candle_width = (df[column_names[0]][1] - df[column_names[0]][0]).total_seconds() * 1000 * 0.9
    inc_candle = fig.vbar(x='date', width=candle_width, top='close', bottom='open', source=inc_source, 
                         fill_color=increasing_color, line_color="black")
    dec_candle = fig.vbar(x='date', width=candle_width, top='open', bottom='close', source=dec_source, 
                         fill_color=decreasing_color, line_color="black")
                         
    # Add the hover tools with specific renderers
    candle_hover.renderers = [inc_candle, dec_candle]
    fig.add_tools(candle_hover)
    
    # --- Axes Labels ---
    fig.xaxis.axis_label = x_axis_label
    fig.yaxis.axis_label = y_axis_label
    
    # --- Callback for y-axis auto-scaling ---
    callback = CustomJS(
        args=dict(
            inc_source=inc_source,
            dec_source=dec_source,
            x_range=fig.x_range,
            y_range=fig.y_range
        ),
        code="""
            // Get visible ranges
            const visible_x_min = x_range.start;
            const visible_x_max = x_range.end;
            let min = Infinity;
            let max = -Infinity;
            
            // Process increasing candles
            const inc_dates = inc_source.data.date;
            const inc_highs = inc_source.data.high;
            const inc_lows = inc_source.data.low;
            
            for (let i=0; i < inc_dates.length; ++i) {
                if (inc_dates[i] >= visible_x_min && inc_dates[i] <= visible_x_max) {
                    min = Math.min(inc_lows[i], min);
                    max = Math.max(inc_highs[i], max);
                }
            }
            
            // Process decreasing candles
            const dec_dates = dec_source.data.date;
            const dec_highs = dec_source.data.high;
            const dec_lows = dec_source.data.low;
            
            for (let i=0; i < dec_dates.length; ++i) {
                if (dec_dates[i] >= visible_x_min && dec_dates[i] <= visible_x_max) {
                    min = Math.min(dec_lows[i], min);
                    max = Math.max(dec_highs[i], max);
                }
            }
            
            // Add padding
            const padding = (max - min) * 0.1;
            y_range.start = min - padding;
            y_range.end = max + padding;
        """
    )
    
    # Attach callback to LODEnd event (fires after user finishes zoom/pan)
    fig.x_range.js_on_change('end', callback)
    
    # --- Theme ---
    curdoc().theme = "dark_minimal"
    
    # --- Output ---
    output_notebook()
    show(fig)