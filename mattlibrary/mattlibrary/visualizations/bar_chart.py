from bokeh.plotting import figure, show, output_notebook, ColumnDataSource
from bokeh.models import HoverTool, NumeralTickFormatter, DatetimeTickFormatter
from bokeh.io import curdoc
import polars as pl

def plot_bar_chart(df: pl.DataFrame,
                   title: str,
                   y_axis_label: str,
                   yaxis_format: str,
                   chart_width: int = 600,
                   chart_height: int = 400):
    """
    Creates a bar chart using Bokeh based on a Polars DataFrame with three columns. The first column contains the category names, the second column contains the category colors, and the third column contains the category values.

    Args:
        df: A Polars DataFrame with three columns (in this order):
            - First column: Category names (str)
            - Second column: Category colors (str)
            - Third column: Category values (numeric)
        title: The title of the chart.
        y_axis_label: The label for the Y-axis.
        yaxis_format: Format string for y-axis tick labels (e.g., '0,0.00').
        chart_width: The width of the chart in pixels.
        chart_height: The height of the chart in pixels.
    """

    # --- Input Validation ---
    if not isinstance(df, pl.DataFrame):
        raise ValueError("df must be a Polars DataFrame.")
    
    if len(df.columns) != 3:
        raise ValueError("DataFrame must have exactly three columns.")
    
    if df.is_empty():
        raise ValueError("DataFrame cannot be empty.")
       
    # --- Prepare Data Source ---
    source = ColumnDataSource(data={
        'x_value': df.select(df.columns[0]).to_series().to_list(),
        'y_value': df.select(df.columns[2]).to_series().to_list(),
        'color': df.select(df.columns[1]).to_series().to_list()
    })

    hovertool = HoverTool(
        tooltips=[
            ('Category', '@x_value'),
            ('Value', '@y_value{' + yaxis_format + '}') # Use y-axis format for tooltip
        ],
        mode='vline' # Could also be 'vline' if preferred for bars
    )
        
    fig = figure(
        x_axis_type="auto",
        x_range = df.select(df.columns[0]).to_series().to_list(),
        height=chart_height,
        width=chart_width,
        title=title,
        toolbar_location="right", # Or None
        tools=[hovertool, "pan", "wheel_zoom", "box_zoom", "reset", "save"] # Add standard tools
    )
        
    fig.vbar(x='x_value',       # Field name for categories
            top='y_value',      # Field name for bar heights
            width=0.9,          # Relative width of bars
            source=source,      # Use the prepared data source
            color='color',      # Use the color field from the source
    )

    # --- Appearance ---
    fig.xgrid.grid_line_color = None  # Remove vertical grid lines
    fig.yaxis.axis_label = y_axis_label
    fig.yaxis.formatter = NumeralTickFormatter(format=yaxis_format)
    fig.xaxis.major_label_orientation = 1.2
    
    # --- Theme ---
    curdoc().theme = "dark_minimal" # Match the line chart theme

    # --- Output ---
    output_notebook()  # Display in Jupyter environments
    show(fig)
