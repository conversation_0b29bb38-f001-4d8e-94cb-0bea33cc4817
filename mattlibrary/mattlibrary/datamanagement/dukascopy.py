"""Client for interacting with Dukas<PERSON> data."""
import os
import polars as pl

def ohlc_bidask_directory_to_polars_dataframe(source_dir: str, timezone_from: str, timezone_to: str, column_mappings: list[dict]) -> pl.DataFrame:
    """Generate a Polars DataFrame from Dukascopy bid and ask CSV files in a directory.

    Args:
        source_dir (str): Directory containing the Dukascopy CSV files
        timezone_from (str): Timezone of the source files
        timezone_to (str): Timezone to convert the data to
        column_mappings (list[dict]): List of dictionaries defining column mappings with keys:
            - source_column_name (str): Column name in the source CSV
            - target_column_name (str): Column name to use in the final DataFrame
            - dtype (pl.DataType): Polars datatype to cast the column to
            - datetime_format (str, optional): Format string for datetime parsing

    Returns:
        pl.DataFrame: A Polars DataFrame containing the OHLC data for all symbols
    """
    # Get all filenames in source directory
    files_dir1 = os.listdir(source_dir)

    # Find all unique symbols in the filenames
    symbols = set([file[0:6] for file in files_dir1])

    # Centralized dataframe that holds the data of all symbols
    master_df = None

    for symbol in symbols:
        # Find the bid and ask files for the current symbol
        bid_file = next((file for file in files_dir1 if symbol in file and "Bid" in file), None)
        ask_file = next((file for file in files_dir1 if symbol in file and "Ask" in file), None)
        if bid_file and ask_file:
                        
            # Import csv files to a polars dataframe
            df = ohlc_bidask_files_to_dataframe(
                source_dir + bid_file, 
                source_dir + ask_file, 
                symbol, 
                timezone_from, 
                timezone_to,
                column_mappings
            )

            # Add dataframe to master_df
            if master_df is None:
                master_df = df
            else:
                master_df = pl.concat([master_df, df])
        else:
            print(f"Could not find bid and ask files for symbol {symbol}")
            continue
    
    # Retain unique entries by datetime and symbol
    master_df = master_df.unique(subset=["datetime", "symbol"])

    # Sort the dataframe by datetime and then symbols
    master_df = master_df.sort(["datetime", "symbol"])

    print(f"Generated dataframe with {len(symbols)} symbols and {len(master_df)} rows")

    return master_df


def ohlc_bidask_files_to_dataframe(bid_file: str, ask_file: str, symbol: str, timezone_from: str, timezone_to: str, column_mappings: list[dict]) -> pl.DataFrame:
    """Read OHLC data from specific Dukascopy CSV bid and ask files into a single Polars DataFrame.
    
    Args:
        bid_file (str): Path to the bid CSV file
        ask_file (str): Path to the ask CSV file
        symbol (str): Currency pair symbol (e.g., 'EURUSD')
        timezone_from (str): Timezone of the source files (e.g., 'EET')
        timezone_to (str): Timezone to convert the data to (e.g., 'UTC')
        column_mappings (list[dict]): List of dictionaries defining column mappings with keys:
            - source_column_name (str): Column name in the source CSV
            - target_column_name (str): Column name to use in the final DataFrame
            - dtype (pl.DataType): Polars datatype to cast the column to
            - datetime_format (str, optional): Format string for datetime parsing
    
    Returns:
        pl.DataFrame: A Polars DataFrame containing the OHLC data with columns as defined
                     in column_mappings plus a 'symbol' column
    """
    # Create bid-specific mappings
    bid_mappings = []
    for mapping in column_mappings:
        bid_mapping = mapping.copy()
        if mapping["target_column_name"] != "datetime":
            bid_mapping["target_column_name"] = f"{mapping['target_column_name']}_bid"
        bid_mappings.append(bid_mapping)
    
    # Read bid file
    print(f"Reading bid file for symbol {symbol}")
    df_bid = read_csv_file_to_dataframe(bid_file, bid_mappings, timezone_from, timezone_to, return_dataframe=True)
    
    # Create ask-specific mappings
    ask_mappings = []
    for mapping in column_mappings:
        ask_mapping = mapping.copy()
        if mapping["target_column_name"] != "datetime":
            ask_mapping["target_column_name"] = f"{mapping['target_column_name']}_ask"
        ask_mappings.append(ask_mapping)
    
    # Read ask file
    print(f"Reading ask file for symbol {symbol}")
    df_ask = read_csv_file_to_dataframe(ask_file, ask_mappings, timezone_from, timezone_to, return_dataframe=True)
    
    # Join bid and ask dataframes and calculate mid prices
    print(f"Joining bid and ask dataframes for symbol {symbol}")
    
    # Prepare expressions for the select operation
    select_expressions = [
        pl.col("datetime"),
        pl.lit(symbol).alias("symbol")
    ]
    
    # Add expressions for calculating mid prices for all non-datetime columns
    for mapping in column_mappings:
        col_name = mapping["target_column_name"]
        if col_name != "datetime":
            select_expressions.append(
                pl.mean_horizontal(f"{col_name}_bid", f"{col_name}_ask")
                .alias(col_name)
                .cast(mapping["dtype"])
            )
    
    joined_df = df_bid.join(df_ask, on="datetime", how="inner").select(select_expressions).sort("datetime")
    
    return joined_df


def read_csv_file_to_dataframe(file_path: str, column_mappings: list[dict], source_timezone: str, target_timezone: str, callback: callable = None, return_dataframe: bool = False):
    """Read a CSV file into a Polars DataFrame with advanced column mapping and type conversion.
    
    Args:
        file_path (str): Path to the CSV file
        column_mappings (list[dict]): List of dictionaries with the following keys:
            - source_column_name (str): Column name in the source CSV
            - target_column_name (str): Column name to use in the DataFrame
            - dtype (pl.DataType, optional): Polars datatype to cast the column to
            - datetime_format (str, optional): Format string for datetime parsing
        source_timezone (str): Source timezone for datetime columns
        target_timezone (str): Target timezone to convert datetime columns to
        callback (callable, optional): Function to call for each processed batch. The callback receives one argument:
            - processed_batch: The processed Polars DataFrame batch
        return_dataframe (bool, optional): If True, returns the complete DataFrame. Defaults to False.
        
    Returns:
        pl.DataFrame or None: If return_dataframe is True, returns a Polars DataFrame with renamed and 
                             converted columns. Otherwise returns None.
    """
    
    # Validate column mappings have required fields
    for mapping in column_mappings:
        if 'source_column_name' not in mapping or 'target_column_name' not in mapping or 'dtype' not in mapping:
            raise ValueError("Each column mapping must contain 'source_column_name', 'target_column_name', and 'dtype'")
    
    # Apply transformations and rename columns
    expressions = []
    
    for mapping in column_mappings:
        source_col = mapping['source_column_name']
        target_col = mapping['target_column_name']
        col_expr = pl.col(source_col)
        
        # Handle datetime conversion if specified
        if 'datetime_format' in mapping:
            col_expr = col_expr.str.to_datetime(
                mapping['datetime_format'], 
                time_zone=source_timezone,
                strict=True
            )
            
            # Convert timezone if source_timezone and target_timezone are different
            if source_timezone != target_timezone:
                col_expr = col_expr.dt.convert_time_zone(target_timezone)
                
        # Apply datatype casting
        col_expr = col_expr.cast(mapping['dtype'])
            
        expressions.append(col_expr.alias(target_col))

    # Extract source column names for initial CSV reading
    source_columns = [mapping['source_column_name'] for mapping in column_mappings]

    # Get column name of the target_column_name that has a dtype of pl.Datetime
    datetime_col_name = next((mapping['target_column_name'] for mapping in column_mappings if mapping['dtype'] == pl.Datetime), None)
    
    # If return_dataframe is True, read the entire file at once
    if return_dataframe:
        # Read the entire CSV file
        df = pl.read_csv(
            file_path,
            has_header=True,
            columns=source_columns
        )
        
        # Apply all transformations and rename columns
        df = df.select(expressions)
        
        # Sort by datetime if available
        df = df.sort(datetime_col_name)
            
        return df
    
    else:
        # If return_dataframe is False, process in batches with callback
        # Read the CSV file in batches
        reader = pl.read_csv_batched(
            file_path,
            has_header=True,
            columns=source_columns
        )
            
        # Iterate over batches    
        while (batches := reader.next_batches(15)):
        
            batch_df = pl.concat(batches)

            # Apply all transformations and rename columns
            processed_batch = batch_df.select(expressions)

            # Sort by datetime
            if datetime_col_name and processed_batch is not None:
                processed_batch = processed_batch.sort(datetime_col_name)
                
            # Invoke callback if provided
            if callback:
                callback(processed_batch)


def merge_dataframes(df1: pl.DataFrame, df2: pl.DataFrame) -> pl.DataFrame:
    """Merge two Dukascopy dataframes based on the datetime column.
    
    This function takes two Polars DataFrames containing Dukascopy data with identical
    schema but potentially different time ranges, and merges them into a single DataFrame.
    The resulting DataFrame is sorted by datetime in ascending order.
    
    Args:
        df1 (pl.DataFrame): First Dukascopy dataframe
        df2 (pl.DataFrame): Second Dukascopy dataframe with same schema as df1
        
    Returns:
        pl.DataFrame: A merged dataframe containing all rows from both input dataframes,
                     sorted by datetime in ascending order
    """
    # Verify both dataframes have the same schema
    if df1.schema != df2.schema:
        raise ValueError("Input dataframes must have identical schemas")
    
    # Check if datetime column exists in both dataframes
    if "datetime" not in df1.columns or "datetime" not in df2.columns:
        raise ValueError("Both dataframes must contain a 'datetime' column")
    
    # Merge the dataframes using concat (union)
    print(f"Merging dataframes for symbol {df1.select('symbol').unique().to_series()[0]}...")
    merged_df = pl.concat([df1, df2])
    
    # Remove duplicates based on datetime and symbol columns
    merged_df = merged_df.unique(subset=["datetime", "symbol"])
    
    # Sort by datetime in ascending order
    merged_df = merged_df.sort("datetime")
    
    return merged_df