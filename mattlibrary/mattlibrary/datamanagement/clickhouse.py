"""ClickHouse database client for managing market data."""

import datetime
from clickhouse_driver import Client
import polars as pl

class ClickHouseClient:
    """A client for interacting with ClickHouse database.
    
    This class provides methods for connecting to, querying, and managing data in a
    ClickHouse database, particularly focused on market data operations.
    
    Attributes:
        host (str): ClickHouse server hostname
        port (int): ClickHouse server port
        client (Client): ClickHouse driver client instance
    """
    
    def __init__(self, host="localhost", port=9000):
        """Initialize a new ClickHouseClient instance.
        
        Args:
            host (str, optional): ClickHouse server hostname. Defaults to "localhost"
            port (int, optional): ClickHouse server port. Defaults to 9000
        """
        self.host = host
        self.port = port
        # self.settings = {"use_numpy": True}
        #connect to clickhouse
        self.connect()
        
    def connect(self):
        """Establish connection to the ClickHouse database."""
        try:
            self.client = Client(host=self.host, port=self.port, compression=True)
        except Exception as e:
            print(f"Error connecting to clickhouse: {e}")
        
    def disconnect(self):
        """Close the connection to the ClickHouse database."""
        self.client.disconnect()
        
    def execute_command(self, command):
        """Execute a SQL command on the ClickHouse database.
        
        Args:
            command (str): SQL command to execute
            
        Returns:
            Result of the command execution
        """
        return self.client.execute(command)
    
    def stream(self, query, callback, max_block_size:int=10_000_000):
        """Stream query results using a callback function.
        
        Args:
            query (str): SQL query to execute
            callback (callable): Function to call for each row of results
            max_block_size (int, optional): Maximum size of data blocks to process. Defaults to 10_000_000
        """
        iterator = self.client.execute_iter(query, settings={"max_block_size": max_block_size})
        for row in iterator:
            callback(row)
                              
    def parse_data(self, input):
        """Parse the result of a ClickHouse query.
        
        Args:
            input: Query result to parse
            
        Returns:
            Parsed data in appropriate format
        """
        if len(input) == 0:
            return []
        elif len(input) == 1:
            return input[0][0]
        else:
            data = []
            for i in input:
                data.append(i[0])
            return data
    
    def get_table_stats(self):
        """Get statistics about tables in the current database.
        
        Returns:
            DataFrame containing table statistics including:
            - Table name
            - Row count
            - Latest modification time
            - Disk size
            - Primary key size
            - Engine type
            - Compression statistics
        """
        query = """
            SELECT
                parts.*,
                columns.compressed_size,
                columns.uncompressed_size,
                columns.ratio
            FROM
            (
                SELECT
                    table,
                    formatReadableSize(sum(data_uncompressed_bytes)) AS uncompressed_size,
                    formatReadableSize(sum(data_compressed_bytes)) AS compressed_size,
                    sum(data_compressed_bytes) / sum(data_uncompressed_bytes) AS ratio
                FROM system.columns
                WHERE database = currentDatabase()
                GROUP BY table
            ) AS columns
            RIGHT JOIN
            (
                SELECT
                    table,
                    sum(rows) AS rows,
                    max(modification_time) AS latest_modification,
                    formatReadableSize(sum(bytes)) AS disk_size,
                    formatReadableSize(sum(primary_key_bytes_in_memory)) AS primary_keys_size,
                    any(engine) AS engine,
                    sum(bytes) AS bytes_size
                FROM system.parts
                WHERE active AND (database = currentDatabase())
                GROUP BY
                    database,
                    table
            ) AS parts ON columns.table = parts.table
            ORDER BY parts.bytes_size DESC;
        """
        return self.client.query_dataframe(query)        
         
    def get_tables(self):

        """Get list of tables in the current database.
        
        Returns:
            list: List of table names
        """

        print("fetching tables")

        tables = self.execute_command("SHOW TABLES")
        return self.parse_data(tables)
        
    def describe_table(self, table_name:str):
        """Get schema information for a specific table.
        
        Args:
            table_name (str): Name of the table to describe
            
        Returns:
            DataFrame containing table schema information
        """
        return self.client.query_dataframe(f"DESCRIBE TABLE {table_name}")
    
    def get_duplicate_row_count(self, table_name:str):
        """Count duplicate rows in a table.
        
        Args:
            table_name (str): Name of the table to check
            
        Returns:
            int: Number of duplicate rows
        """
        unparsed = self.client.execute(f"SELECT count(*) - uniqExact(*) from {table_name}")
        return self.parse_data(unparsed)
    
    def create_table(self, table_name:str, column_names_types:list[tuple], engine:str, partition_by:str, order_by:str):
        """Create a new table in the database.
        
        Args:
            table_name (str): Name of the table to create
            column_names_types (list[tuple]): List of (column_name, type) tuples
            engine (str): ClickHouse table engine to use
            partition_by (str): Partitioning expression
            order_by (str): Ordering expression (also used as primary key)
        """
        try:
        
            #schema
            schema = f'({", ".join(f"{name} {type}" for name, type in column_names_types)})'
            
            #engine name
            # engine_name = "MergeTree"
            # engine_name = "ReplacingMergeTree()"
            
            #partion by
            # partition_by = "toYYYYMM(datetime)"
            
            #order by (used as primary key as well)
            # order_by = "(symbol, datetime)"
                        
            #create table
            if partition_by == "":
                self.execute_command(f"CREATE TABLE {table_name} {schema} Engine={engine} ORDER BY {order_by};")
            else:
                self.execute_command(f"CREATE TABLE {table_name} {schema} Engine={engine} ORDER BY {order_by} PARTITION BY {partition_by};")
            
        except Exception as e:
            print(f"Error creating table {table_name}: {e}")
            
    def delete_table(self, table_name:str):
        """Delete a table from the database.
        
        Args:
            table_name (str): Name of the table to delete
        """
        try:
            self.execute_command(f"DROP TABLE {table_name} no delay;")
        except Exception as e:
            print(f"Error deleting table {table_name}: {e}")
            
    def read_dataframe(self, query:str) -> pl.DataFrame:
        """Read data from the database into a Polars DataFrame.
        
        Args:
            query (str): SQL query to execute
            
        Returns:
            pl.DataFrame: Query results as a Polars DataFrame
        """
        try:
            df = self.client.query_dataframe(query)
            return pl.from_pandas(df)
        except Exception as e:
            print(f"Error reading dataframe from table: {e}") 
            return pl.DataFrame() 
           
    def request_marketdata(self, table_name:str, symbols:list, start_dt:datetime.datetime, end_dt:datetime.datetime, compression:str=""):
        """Request market data from the database.
        
        Args:
            table_name (str): Name of the table containing market data
            symbols (list): List of symbols to retrieve
            start_dt (datetime): Start datetime for the data
            end_dt (datetime): End datetime for the data
            compression (str, optional): Time interval for data compression. Defaults to ""
                Examples: "10 second", "5 minute", "2 hour", "3 day"
            
        Returns:
            pl.DataFrame: Market data as a Polars DataFrame
            
        Raises:
            Exception: If table name doesn't contain 'ticks' or 'ohlc'
        """
        #datetime in string format (eg, '20210101 00:00:00')
        #compression examples
        #10 second, 5 minute, 2 hour, 3 day
        
        #compose filter by symbols if any symbols supplied
        if len(symbols) == 0:
            symbol_selection = ""
        else:
            requested_symbols = (str(symbols)).replace('[', '(').replace(']', ')')    
            symbol_selection = f"AND (symbol IN {requested_symbols})"
                
        if compression == "":
            query = f"""
                SELECT *
                FROM {table_name}
                WHERE (datetime BETWEEN toDateTime64('{start_dt}', 3, 'UTC') AND toDateTime64('{end_dt}', 3, 'UTC')) {symbol_selection}
                ORDER BY 
                    datetime ASC,
                    symbol ASC
                """
        else:
            if "ticks" in table_name:
                query = f"""
                    SELECT 
                        symbol,
                        dt,
                        argMin((bid+ask)/2, datetime) AS open,
                        max((bid+ask)/2) AS high,
                        min((bid+ask)/2) AS low,
                        argMax((bid+ask)/2, datetime) AS close
                        FROM {table_name}
                    WHERE (toYYYYMMDD(datetime) BETWEEN toDateTime64('{start_dt}', 3, 'UTC') AND toDateTime64('{end_dt}', 3, 'UTC')) {symbol_selection})
                    GROUP BY symbol, toStartOfInterval(datetime, INTERVAL {compression}) AS dt
                    ORDER BY 
                        symbol ASC,
                        dt ASC
                    """
            elif "ohlc" in table_name:
                query = f"""
                    SELECT 
                        symbol,
                        dt,
                        argMin(open, datetime) AS open,
                        max(high) AS high,
                        min(low) AS low,
                        argMax(close, datetime) AS close
                        FROM {table_name}
                    WHERE (toYYYYMMDD(datetime) BETWEEN toDateTime64('{start_dt}', 3, 'UTC') AND toDateTime64('{end_dt}', 3, 'UTC')) {symbol_selection})
                    GROUP BY symbol, toStartOfInterval(datetime, INTERVAL {compression}) AS dt
                    ORDER BY 
                        symbol ASC,
                        dt ASC
                    """
            else:
                raise Exception("Could not find 'ticks' or 'ohlc' in table name")
                                   
        df = self.client.query_dataframe(query)
        return pl.from_pandas(df)
                                    
    def write_dataframe(self, table_name:str, df:pl.DataFrame):
        """Write a Polars DataFrame to the database.
        
        Args:
            table_name (str): Name of the target table
            df (pl.DataFrame): DataFrame to write
        """
        try:
            self.client.insert_dataframe(f"INSERT INTO {table_name} VALUES", df.to_pandas(), settings={"use_numpy": True, "max_partitions_per_insert_block": 1000})
        except Exception as e:
            print(f"Error writing dataframe into table {table_name}: {e}")
            
    def optimize_table(self, table_name:str):
        """Optimize a table's storage.
        
        Args:
            table_name (str): Name of the table to optimize
        """
        try:
            self.execute_command(f"OPTIMIZE TABLE {table_name} FINAL;")
        except Exception as e:
            print(f"Error optimizing table {table_name}: {e}")
       