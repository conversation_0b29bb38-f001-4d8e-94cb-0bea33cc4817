"""Backtest configuration module."""

import datetime
import json


class BacktestConfiguration:
    """Configuration class for backtest parameters."""
    
    def __init__(self, 
                 
        #backtest parameters
        starting_balance: float,
        base_currency: str,
        execution_plugin_type: str,
        symbols_data: list,
        symbols_traded: list,
        
        #data and strategy parameters
        data_parameters: dict,
        strategy_parameters: dict,

        #performance tracking parameters
        track_performance: bool,
        visualize_performance: bool,

        #logging parameters
        logging_enabled: bool,
        log_to_console: bool,
        log_to_file: bool,
        backtest_archiving: bool,
        statistics_excel_logging: bool,
        remove_all_previous_backtest_data: bool,
        backtests_base_directory: str):
        
        #backtest parameters
        self.starting_balance = starting_balance
        self.base_currency = base_currency
        self.symbols_data = symbols_data
        self.symbols_traded = symbols_traded
        self.execution_plugin_type = execution_plugin_type

        #data and strategy parameters
        self.data_parameters = data_parameters
        self.strategy_parameters = strategy_parameters

        #performance tracking
        self.track_performance = track_performance
        self.visualize_performance = visualize_performance

        #logging
        self.logging_enabled = logging_enabled
        self.log_to_console = log_to_console
        self.log_to_file = log_to_file
        self.backtest_archiving = backtest_archiving
        self.statistics_excel_logging = statistics_excel_logging
        self.remove_all_previous_backtest_data = remove_all_previous_backtest_data
        self.backtests_base_directory = backtests_base_directory


    def to_json(self, indent: int = 4) -> str:
        """
        Serializes the BacktestConfiguration instance to a JSON string.
        Handles datetime objects by converting them to ISO 8601 strings.

        Args:
            indent: The indentation level for the JSON output.

        Returns:
            A JSON string representation of the configuration.
        """
        # Create a dictionary from the instance's attributes
        config_dict = self.__dict__.copy()
        
        # Convert datetime objects to ISO 8601 strings
        def convert_datetime_recursive(obj):
            if isinstance(obj, dict):
                return {k: convert_datetime_recursive(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_datetime_recursive(item) for item in obj]
            elif isinstance(obj, datetime.datetime):
                return obj.isoformat()
            return obj
            
        config_dict = convert_datetime_recursive(config_dict)
        
        # Return the JSON string
        return json.dumps(config_dict, indent=indent)


    @classmethod
    def from_json(cls, json_string: str):
        """
        Deserializes a JSON string back into a BacktestConfiguration instance.
        Handles ISO 8601 datetime strings by converting them back to datetime objects.

        Args:
            json_string: A JSON string representing the configuration.

        Returns:
            An instance of BacktestConfiguration.
        """
        def datetime_parser(dct):
            """
            Custom object hook for json.loads() to convert datetime strings recursively.
            """
            def convert_datetime_recursive(obj):
                if isinstance(obj, dict):
                    return {k: convert_datetime_recursive(v) for k, v in obj.items()}
                elif isinstance(obj, list):
                    return [convert_datetime_recursive(item) for item in obj]
                elif isinstance(obj, str):
                    try:
                        # Attempt to parse as ISO 8601 datetime
                        return datetime.datetime.fromisoformat(obj)
                    except ValueError:
                        # If it's not a datetime string, keep it as is
                        return obj
                return obj
            
            return convert_datetime_recursive(dct)

        # Load the JSON string into a dictionary, using the custom object hook
        loaded_data = json.loads(json_string, object_hook=datetime_parser)
        
        # Reconstruct the BacktestConfiguration object from the loaded dictionary
        # using dictionary unpacking (**loaded_data)
        return cls(**loaded_data)
    

    def __repr__(self):
        """Return a nicely formatted output of the BacktestConfiguration."""
        return self.to_json()
        
        
        

