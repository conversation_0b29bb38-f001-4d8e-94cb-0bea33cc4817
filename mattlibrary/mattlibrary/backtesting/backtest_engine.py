"""Backtest engine module for running trading strategies on historical data."""


import logging
import datetime as dt
import os
import shutil
import polars as pl
import inspect
from mattlibrary.trading.strategy_base import StrategyBase
from mattlibrary.trading.engine_oms import TradingEngine
from mattlibrary.backtesting.backtest_configuration import BacktestConfiguration
from mattlibrary.logging import logging_config
from mattlibrary.logging.excel_logging import excel_logger
from mattlibrary.trading.performance_metrics import PerformanceMetrics
from mattlibrary.helpers.custom_enums import CustomBool


class BacktestEngine:
    """Engine for running backtests on trading strategies with historical data."""
    

    def __init__(self, configuration: BacktestConfiguration, 
                 strategy_generator_pathfilename: str,
                 data_generator_pathfilename: str,
                 override_delete_previous_backtests_metadata: CustomBool = CustomBool.none):
        """Initialize the backtest engine.
        
        Args:
            configuration: Backtest configuration
            strategy_generator_pathfilename: Path to the strategy generator code
            data_generator_pathfilename: Path to the data generator code
            override_delete_previous_backtests_metadata: Whether to delete previous backtest metadata
        """
        
        # Store configuration
        self.configuration = configuration

        # Initialize the trading engine
        self.trading_engine = TradingEngine(self.configuration.starting_balance, self.configuration.base_currency, self.configuration.track_performance, self.configuration.execution_plugin_type)
                
        # generate strategies and attach to trading engine
        with open(strategy_generator_pathfilename, "r") as f:
            code = f.read()
        custom_namespace = {}
        exec(code, custom_namespace)
        self.strategies = custom_namespace['generate_strategies'](configuration)
        for strategy in self.strategies:
            self.trading_engine.add_strategy(strategy)

        # generate data source
        with open(data_generator_pathfilename, "r") as f:
            code = f.read()
        custom_namespace = {}
        exec(code, custom_namespace)
        self.data_source = custom_namespace['generate_data'](configuration)
                
        #validate data source
        if not isinstance(self.data_source, pl.DataFrame):
            self.logger.error(f"Invalid data source type: {type(self.data_source)}. Expected Polars DataFrame")
            raise TypeError("Data source must be a Polars DataFrame")
            
        if len(self.data_source) == 0:
            self.logger.error("Empty data source provided")
            raise ValueError("Data source contains no rows")

        #validate strategies
        if len(self.strategies) == 0:
            self.logger.error("No strategies provided")
            raise ValueError("At least one strategy must be provided")
        for strategy in self.strategies:
            if not isinstance(strategy, StrategyBase):
                self.logger.error(f"Invalid strategy type: {type(strategy)}. Expected StrategyBase object")
                raise TypeError("Strategies must be a list of StrategyBase objects")

        # Configure directories
        self._configure_directories()

        # Configure logging
        self._configure_logging()

        #store meta data
        self._store_meta_data(strategy_generator_pathfilename, data_generator_pathfilename, override_delete_previous_backtests_metadata)

        #log message
        self.logger.info(f"Initialized BacktestEngine [logging_engabled={self.configuration.logging_enabled}, statistics_excel_logging={self.configuration.statistics_excel_logging}, track_performance={self.configuration.track_performance}]")


    def start_backtest(self):
        """Start the backtest."""
        row_count = len(self.data_source)
        strategy_count = len(self.trading_engine.strategies)

        self.logger.info(f"Starting data iteration [rows={row_count}, active_strategies={strategy_count}]")
        
        for row in self.data_source.iter_rows(named=True):
            # Process each row through the trading engine
            self.trading_engine.process_market_data(row)
        
        self.logger.info(f"Data iteration complete [processed_rows={row_count}, total_strategies={strategy_count}]")

        #finalize backtest
        self.logger.info("Finalizing backtest")
        self.trading_engine.finalize()

        #generate performance statistics and store results on disk
        self._process_results(row_count)

        #log message
        self.logger.info(f"Backtest successfully finalized")


    @classmethod
    def load_backtest(cls, backtest_path: str, override_delete_previous_backtests_metadata: CustomBool):
        """Load a backtest from a saved directory.
        
        Args:
            backtest_path: Path to the backtest directory
            override_delete_previous_backtests_metadata: Whether to delete previous backtest metadata
        """
        #check if backtest path exists
        if not os.path.exists(backtest_path):
            raise ValueError(f"Backtest path does not exist: {backtest_path}")

        # Load configuration
        configuration_path = os.path.join(backtest_path, "configuration.json")
        with open(configuration_path, "r") as f:
            configuration = BacktestConfiguration.from_json(f.read())

        # path_filenames for strategy and data generation code
        strategy_generation_code_path = os.path.join(backtest_path, "strategy_generation_code.py")
        data_generation_code_path = os.path.join(backtest_path, "data_generation_code.py")

        # Create a new backtest engine with the loaded configuration
        engine = cls(configuration, strategy_generation_code_path, data_generation_code_path , override_delete_previous_backtests_metadata)
        
        #log message
        engine.logger.info(f"Loaded existing backtest from {backtest_path}")
        
        return engine
    

    def _configure_directories(self):

        self.backtest_name = f"backtest_{dt.datetime.now().strftime('%Y-%b-%d %H-%M-%S')}"
        self.base_directory = self.configuration.backtests_base_directory

        #if backtest archiving is disabled, do not configure directories
        if not self.configuration.backtest_archiving:
            return
            
        # Store configuration and backtest name and directory
        self.backtest_directory = os.path.join(self.base_directory, self.backtest_name)
        self.engine_log_directory = os.path.join(self.backtest_directory, "logs")
        self.excel_directory = os.path.join(self.backtest_directory, "excel")
        self.strategy_directory = os.path.join(self.backtest_directory, "strategies")
        self.performance_directory = os.path.join(self.backtest_directory, "performance")

        #create backtests directory if it does not yet exist
        if not os.path.exists(self.base_directory):
            os.makedirs(self.base_directory)

        #create directory and subdirectories for this backtest
        os.makedirs(self.backtest_directory)
        os.makedirs(self.engine_log_directory)
        os.makedirs(self.excel_directory)
        os.makedirs(self.strategy_directory)
        os.makedirs(self.performance_directory)


    def _configure_logging(self):
        #register local logger
        self.logger = logging.getLogger(__name__)

        #configure logging 
        logging_config.setup_logging(
            logging_enabled=self.configuration.logging_enabled, 
            log_level=logging.DEBUG, 
            log_to_file=self.configuration.log_to_file,
            log_file=os.path.join(self.engine_log_directory, "engine.log") if self.configuration.backtest_archiving else "engine.log", 
            log_to_console=self.configuration.log_to_console, 
            clean_log_file=True)

        #configure strategy excel logger
        if self.configuration.backtest_archiving:
            excel_logger.configure_base_directory(self.excel_directory)


    def _store_meta_data(self, strategy_generator_pathfilename: str, data_generator_pathfilename: str, override_delete_previous_backtests_metadata: CustomBool):
        
        #do not store meta data if backtest archiving is disabled
        if not self.configuration.backtest_archiving:
            return
        
        #store configuration
        with open(os.path.join(self.backtest_directory, "configuration.json"), "w") as f:
            f.write(self.configuration.to_json())

        #store strategy generation code
        shutil.copy(strategy_generator_pathfilename, os.path.join(self.backtest_directory, "strategy_generation_code.py"))

        #store data generation code
        shutil.copy(data_generator_pathfilename, os.path.join(self.backtest_directory, "data_generation_code.py"))
        
        #store the actual code of the strategies, used
        unique_strategy_types = set([type(x) for x in self.strategies])
        for strategy_type in unique_strategy_types:
            strategy_path_filename = inspect.getfile(strategy_type)
            strategy_name = os.path.basename(strategy_path_filename)
            shutil.copy(strategy_path_filename, os.path.join(self.strategy_directory, strategy_name))

         #if requested, delete all subdirectories under base_directory (including all directories that contain files)
        is_delete_previous_backtest_metadata = self.configuration.remove_all_previous_backtest_data
        if override_delete_previous_backtests_metadata is CustomBool.true:
            is_delete_previous_backtest_metadata = True
        elif override_delete_previous_backtests_metadata is CustomBool.false:
            is_delete_previous_backtest_metadata = False
        
        if is_delete_previous_backtest_metadata:
            #delete all immediate subdirectories under base_directory except backtest_directory
            for item in os.listdir(self.base_directory):
                item_path = os.path.join(self.base_directory, item)
                if os.path.isdir(item_path):  # Check if it's a directory
                    if item_path != self.backtest_directory:
                        shutil.rmtree(item_path)
        

    def _process_results(self, row_count: int):
        """Store the results of the backtest on disk."""
        
        #do not store results if performance tracking is disabled
        if not self.configuration.track_performance:
            return

        #generate performance statistics
        parent_orders, trades, positions = self.trading_engine.get_orders_trades_positions()
        self.results = PerformanceMetrics(self.configuration)
        self.results.generate_statistics(row_count, parent_orders, trades, positions)

        #if backtest archiving is enabled, store all results on disk
        if self.configuration.backtest_archiving:
            #write performance statistics (in text format) to disk
            with pl.Config(tbl_rows=-1, tbl_cols=-1): #rows=-1 and cols=-1 to show all rows and columns
                metrics_string = self.results.statistics_string_format.__repr__()
            with open(os.path.join(self.performance_directory, "performance_statistics.txt"), "w") as f:
                f.write(metrics_string)

            #write other dataframes to disk
            self.results.statistics_string_format.write_parquet(os.path.join(self.performance_directory, "performance_statistics.parquet"))
            self.results.data_series.write_parquet(os.path.join(self.performance_directory, "data_series.parquet"))
            self.results.weekly_pnl_series.write_parquet(os.path.join(self.performance_directory, "weekly_pnl_series.parquet"))
            self.results.monthly_pnl_series.write_parquet(os.path.join(self.performance_directory, "monthly_pnl_series.parquet"))
            self.results.annual_pnl_series.write_parquet(os.path.join(self.performance_directory, "annual_pnl_series.parquet"))
            self.results.trades.write_parquet(os.path.join(self.performance_directory, "trades.parquet"))
            self.results.positions.write_parquet(os.path.join(self.performance_directory, "positions.parquet"))
            self.results.child_orders.write_parquet(os.path.join(self.performance_directory, "child_orders.parquet"))
            
            #export statistics to excel
            if self.configuration.statistics_excel_logging:
                self.results.export_statistics_to_excel(self.excel_directory)

        #store raw performance statistics in a central dataframe for all backtests
        backtest_to_add = self.results.statistics_numeric_format.insert_column(0, pl.Series("backtest_name", [self.backtest_name]))
        
        if self.configuration.remove_all_previous_backtest_data:
            backtest_to_add.write_parquet(os.path.join(self.base_directory, "backtest_results.parquet"))
        else:
            backtest_results = pl.read_parquet(os.path.join(self.base_directory, "backtest_results.parquet"))
            backtest_results = backtest_results.vstack(backtest_to_add)
            backtest_results.write_parquet(os.path.join(self.base_directory, "backtest_results.parquet"))

        #visualize performance
        if self.configuration.visualize_performance:
            self.results.generate_visuals()