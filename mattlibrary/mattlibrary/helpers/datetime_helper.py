"""Helper module for datetime operations and formatting."""

from datetime import timed<PERSON><PERSON>

def format_duration(delta: timedelta) -> str:
    """Formats a timedelta object into a concise string like '1y-2d-3hrs-5mins-2secs-234ms'."""
    total_seconds = int(delta.total_seconds())
    milliseconds = delta.microseconds // 1000

    seconds_in_year = 365 * 24 * 3600
    seconds_in_day = 24 * 3600
    seconds_in_hour = 3600
    seconds_in_minute = 60

    years, remainder = divmod(total_seconds, seconds_in_year)
    days, remainder = divmod(remainder, seconds_in_day)
    hours, remainder = divmod(remainder, seconds_in_hour)
    minutes, seconds = divmod(remainder, seconds_in_minute)

    parts = []
    if years:
        parts.append(f"{years}y")
    if days:
        parts.append(f"{days}d")
    if hours:
        parts.append(f"{hours}hrs")
    if minutes:
        parts.append(f"{minutes}mins")
    if seconds:
        parts.append(f"{seconds}secs")
    if milliseconds:
        parts.append(f"{milliseconds}ms")
    
    # If all values are zero, return "0ms"
    return "-".join(parts) if parts else "0ms"