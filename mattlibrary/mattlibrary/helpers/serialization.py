"""Helper functions for data serialization and deserialization."""

import pickle

def serialize(filename:str, data):
    """Serialize data to a file using pickle.
    
    This function saves Python objects to a file using pickle serialization.
    It can handle any Python object that can be pickled.
    
    Args:
        filename (str): Path to the file where data should be saved
        data: Any Python object that can be pickled
    """
    with open(filename, "wb") as fp:
        pickle.dump(data, fp)    

def deserialize(filename:str):
    """Load and deserialize data from a pickle file.
    
    This function loads Python objects from a file that was previously
    serialized using pickle.
    
    Args:
        filename (str): Path to the pickle file to load
        
    Returns:
        The deserialized Python object
        
    Raises:
        FileNotFoundError: If the specified file doesn't exist
        pickle.UnpicklingError: If the file contains invalid pickle data
    """
    with open(filename, "rb") as fp:
        data = pickle.load(fp)
        return data
