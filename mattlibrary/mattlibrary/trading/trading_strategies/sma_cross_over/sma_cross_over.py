"""Simple Moving Average (SMA) crossover trading strategy implementation."""
from mattlibrary.logging.excel_logging import excel_logger
from mattlibrary.trading.parent_orders.parent_order_bracket import ParentOrderBracket
from mattlibrary.trading.parent_orders.parent_order_standard import Parent<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from mattlibrary.trading.child_order_type import Child<PERSON><PERSON>rType
from mattlibrary.trading.strategy_base import StrategyBase

class SmaCrossoverStrategy(StrategyBase):
    """SMA Crossover Strategy that generates buy/sell signals when price crosses SMA."""
    
    def __init__(self, strategy_parameters: dict):
        """
        Initialize the strategy.
        
        Args:
            symbol_id: Symbol to trade
            sma_window: Window size for SMA calculation
            order_size: Size of orders to submit
            is_target_size: Whether to use target size
            enable_excel_logging: Whether to enable Excel logging
        """
        # Initialize the strategy parameters
        self.symbol_id = strategy_parameters["symbol_id"]
        self.order_size = strategy_parameters["order_size"]
        self.is_target_size = strategy_parameters["is_target_size"]
        self.profit_target_percent = strategy_parameters["profit_target_percent"]
        self.stop_loss_percent = strategy_parameters["stop_loss_percent"]
        self.is_strategy_excel_logging = strategy_parameters["is_strategy_excel_logging"]

        # Initialize the base strategy
        super().__init__(f"SMA Crossover Strategy for {self.symbol_id}", [self.symbol_id])
        
        #configure excel logging        
        if self.is_strategy_excel_logging:
            excel_logger.configure_worksheet(
                "strategy_log",
                "sma_crossover",
                [
                    ("timestamp", "yyyy-mm-dd"),
                    ("symbol", ""),
                    ("side", ""),
                    ("current_price", "0.00000"),
                    ("current_sma", "0.00000"),
                    ("previous_price", "0.00000"),
                    ("previous_sma", "0.00000")
                ]
            )

        self.previous_price = 0.0
        self.previous_sma = 0.0
        self.is_initialized = False
               

    def process_data(self, row):
        """
        Process a single data row and generate trading signals.
        Irrelevant symbol related data is filtered out before reaching this function.
        
        Args:
            row: Data row containing market data
        """
        
        # Initialize the strategy unless it has already been initialized
        if not self.is_initialized:
            self.previous_price = row["close"]
            self.previous_sma = row["sma"]
            self.is_initialized = True
            return
        
        time_stamp = row["datetime"]
        current_price = row["close"]
        current_sma = row["sma"]
        
        if current_price > current_sma and self.previous_price < self.previous_sma:
            # Submit buy order log message
            if self.is_strategy_excel_logging:
                excel_logger.log("strategy_log","sma_crossover", [time_stamp, self.symbol_id, "BUY", current_price, current_sma, self.previous_price, self.previous_sma])
            # Submit buy order
            self._submit_order(self.order_size)
            
        elif current_price < current_sma and self.previous_price > self.previous_sma:
            # Submit sell order log message
            if self.is_strategy_excel_logging:
                excel_logger.log("strategy_log", "sma_crossover", [time_stamp, self.symbol_id, "SELL", current_price, current_sma, self.previous_price, self.previous_sma])
            # Submit sell order
            self._submit_order(-self.order_size)

        # Update previous price and sma
        self.previous_price = row["close"]
        self.previous_sma = row["sma"]


    def finalize(self):
        """Finalize the strategy."""
        # Finalize the workbook
        if self.is_strategy_excel_logging:
            excel_logger.finalize_workbook("strategy_log")
    

    def _submit_order(self, size: int):
        """
        Submit a parent order and make adjustements as function of whether target_size is enabled or not (use position to determine the target size)
        
        Args:
            size: Size of the order (positive for buy, negative for sell)
        """

        #cancel all other working parent orders
        # for parent_order_id in list(self.working_parent_orders.keys()):
        #     self.working_parent_orders[parent_order_id].cancel_order()
            
        # Determine the final order size as function of current position size
        # final_order_size = size
        # if self.is_target_size:
        #     old_size= self.positions[self.symbol_id].size
        #     final_order_size = size - old_size
            
        final_order_size = size

        # Only submit order if there's a non-zero size to trade
        if final_order_size != 0:
            parent_order = ParentOrderBracket(
                strategy_id=self.strategy_id,
                symbol_id=self.symbol_id,
                core_size = final_order_size,
                bracket_size = -size,
                profit_target_percent=self.profit_target_percent,
                stop_loss_percent=self.stop_loss_percent,
                core_order_type=ChildOrderType.MARKET
            )
            
            self._submit_parent_order(parent_order)