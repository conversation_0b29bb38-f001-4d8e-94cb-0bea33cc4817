
class Symbol:
    """A class representing a financial instrument symbol.
    This class encapsulates the basic information about a financial instrument
    including its identifier, asset class, and base currency.
    """
    symbolId : str
    underlying_symbol: str
    assetClass : str
    baseCurrency : str
    exchange : str
    tradingClass : str
    localSymbol : str
    lastTradeDateOrContractMonth : str
    putCall : str
    strike : float
    multiplier: int

    def __init__(self,
        symbolId:str, 
        underlying_symbol:str,
        assetClass:str, 
        baseCurrency:str, 
        exchange:str="", 
        tradingClass:str="", 
        localSymbol:str="", 
        lastTradeDateOrContractMonth:str="", 
        putCall:str="", 
        strike:float=0.0, 
        multiplier:int=0):
        
        self.symbolId = symbolId
        self.underlying_symbol = underlying_symbol
        self.assetClass = assetClass
        self.baseCurrency = baseCurrency
        self.exchange = exchange
        self.tradingClass = tradingClass
        self.localSymbol = localSymbol
        self.lastTradeDateOrContractMonth = lastTradeDateOrContractMonth
        self.putCall = putCall
        self.strike = strike
        self.multiplier = multiplier

    def __repr__(self):
        if self.assetClass == "STOCK":
            return f"STOCK(symbolId={self.symbolId}, underlying_symbol={self.underlying_symbol}, baseCurrency={self.baseCurrency}, exchange={self.exchange})"
        elif self.assetClass == "FOREX":
            return f"FOREX(symbolId={self.symbolId}, underlying_symbol={self.underlying_symbol}, baseCurrency={self.baseCurrency}, exchange={self.exchange})"
        elif self.assetClass == "OPTION":
            return f"OPTION(symbolId={self.symbolId}, underlying_symbol={self.underlying_symbol}, baseCurrency={self.baseCurrency}, exchange={self.exchange}, lastTradeDateOrContractMonth={self.lastTradeDateOrContractMonth}, putCall={self.putCall}, strike={self.strike}, multiplier={self.multiplier})"
        elif self.assetClass == "FUTURE":
            return f"FUTURE(symbolId={self.symbolId}, underlying_symbol={self.underlying_symbol}, baseCurrency={self.baseCurrency}, exchange={self.exchange}, lastTradeDateOrContractMonth={self.lastTradeDateOrContractMonth}, multiplier={self.multiplier})"
        elif self.assetClass == "CONTFUTURE":
            return f"CONTFUTURE(symbolId={self.symbolId}, underlying_symbol={self.underlying_symbol}, baseCurrency={self.baseCurrency}, exchange={self.exchange}, multiplier={self.multiplier})"
        elif self.assetClass == "FUTURESOPTION":
            return f"FUTURESOPTION(symbolId={self.symbolId}, underlying_symbol={self.underlying_symbol}, baseCurrency={self.baseCurrency}, exchange={self.exchange}, lastTradeDateOrContractMonth={self.lastTradeDateOrContractMonth}, putCall={self.putCall}, strike={self.strike}, multiplier={self.multiplier})"
        elif self.assetClass == "INDEX":
            return f"INDEX(symbolId={self.symbolId}, underlying_symbol={self.underlying_symbol}, baseCurrency={self.baseCurrency}, exchange={self.exchange})"
        else:
            return f"UNKNOWN(symbolId={self.symbolId}, underlying_symbol={self.underlying_symbol}, baseCurrency={self.baseCurrency}, exchange={self.exchange})"
        

class Stock(Symbol):
    def __init__(self, underlying_symbol:str, baseCurrency:str, exchange:str):
        symbolId = f"STOCK_{underlying_symbol}_{exchange}_{baseCurrency}"
        super().__init__(symbolId, underlying_symbol, "STOCK", baseCurrency, exchange=exchange)


class Forex(Symbol):
    def __init__(self, underlying_symbol:str):
        symbolId = f"FOREX_{underlying_symbol}"
        super().__init__(symbolId, underlying_symbol, "FOREX", symbolId[-3:])
                

class Option(Symbol):
    def __init__(self, underlying_symbol:str, baseCurrency:str, exchange:str, lastTradeDateOrContractMonth:str, putCall:str, strike:float, multiplier:int, localSymbol:str="", tradingClass:str=""):
        localSymbol_tradingClass = f"_{localSymbol}" if localSymbol else "" + f"_{tradingClass}" if tradingClass else ""
        symbolId = f"OPTION_{underlying_symbol}_{exchange}_{baseCurrency}_{lastTradeDateOrContractMonth}_{putCall}_{strike}_{multiplier}{localSymbol_tradingClass}"
        super().__init__(symbolId, underlying_symbol, "OPTION", baseCurrency, exchange=exchange, lastTradeDateOrContractMonth=lastTradeDateOrContractMonth, putCall=putCall, strike=strike, multiplier=multiplier, localSymbol=localSymbol, tradingClass=tradingClass)
        

class Future(Symbol):
    def __init__(self, underlying_symbol:str, baseCurrency:str, exchange:str, lastTradeDateOrContractMonth:str, multiplier:int, localSymbol:str="", tradingClass:str=""):
        localSymbol_tradingClass = f"_{localSymbol}" if localSymbol else "" + f"_{tradingClass}" if tradingClass else ""
        symbolId = f"FUTURE_{underlying_symbol}_{exchange}_{baseCurrency}_{lastTradeDateOrContractMonth}_{multiplier}{localSymbol_tradingClass}"
        super().__init__(symbolId, underlying_symbol, "FUTURE", baseCurrency, exchange=exchange, lastTradeDateOrContractMonth=lastTradeDateOrContractMonth, multiplier=multiplier, localSymbol=localSymbol, tradingClass=tradingClass)


class ContinuousFuture(Symbol):
    def __init__(self, underlying_symbol:str, baseCurrency:str, exchange:str, multiplier:int, localSymbol:str="", tradingClass:str=""):
        localSymbol_tradingClass = f"_{localSymbol}" if localSymbol else "" + f"_{tradingClass}" if tradingClass else ""
        symbolId = f"CONTFUTURE_{underlying_symbol}_{exchange}_{baseCurrency}_{multiplier}{localSymbol_tradingClass}"
        super().__init__(symbolId, underlying_symbol, "CONTFUTURE", baseCurrency, exchange=exchange, multiplier=multiplier, localSymbol=localSymbol, tradingClass=tradingClass)


class FuturesOption(Symbol):
    def __init__(self, underlying_symbol:str, baseCurrency:str, exchange:str, lastTradeDateOrContractMonth:str, putCall:str, strike:float, multiplier:int, localSymbol:str="", tradingClass:str=""):
        localSymbol_tradingClass = f"_{localSymbol}" if localSymbol else "" + f"_{tradingClass}" if tradingClass else ""
        symbolId = f"FUTURESOPTION_{underlying_symbol}_{exchange}_{baseCurrency}_{lastTradeDateOrContractMonth}_{putCall}_{strike}_{multiplier}{localSymbol_tradingClass}"
        super().__init__(symbolId, underlying_symbol, "FUTURESOPTION", baseCurrency, exchange=exchange, lastTradeDateOrContractMonth=lastTradeDateOrContractMonth, putCall=putCall, strike=strike, multiplier=multiplier, localSymbol=localSymbol, tradingClass=tradingClass)


class Index(Symbol):
    def __init__(self, underlying_symbol:str, baseCurrency:str, exchange:str):
        symbolId = f"INDEX_{underlying_symbol}_{exchange}_{baseCurrency}"
        super().__init__(symbolId, underlying_symbol, "INDEX", baseCurrency, exchange=exchange)
