"""Market Parent Order class for representing market orders at parent level."""
from datetime import datetime
from typing import Callable, Dict

from mattlibrary.trading.parent_order_type import ParentOrderType
from mattlibrary.trading.parent_order_status import ParentOrderStatus
from mattlibrary.trading.child_order import <PERSON><PERSON>rde<PERSON>
from mattlibrary.trading.child_order_type import Child<PERSON><PERSON>rType
from mattlibrary.trading.child_order_status import Child<PERSON><PERSON>r<PERSON>tatus
from mattlibrary.trading.parent_order_base import ParentOrderBase


class ParentOrderStandard(ParentOrderBase):
    """Represents a standard parent order that supports market, limit, stop, and stop-limit order types.
    
    This class automatically determines the order type based on the presence of limit_price 
    and stop_price parameters, creating the appropriate child order when market conditions are met.
    """
    
    def __init__(self, 
                 strategy_id: str,
                 symbol_id: str,
                 size: int,
                 order_type: ChildOrderType,
                 limit_price: float = None,
                 stop_price: float = None):
        """Initialize a new ParentOrderStandard instance.
        
        Args:
            strategy_id: Identifier for the strategy submitting the order
            symbol_id: Trading symbol identifier  
            size: Order size (positive for buy, negative for sell)
            order_type: Type of child order
            limit_price: Optional limit price for limit/stop-limit orders
            stop_price: Optional stop price for stop/stop-limit orders
        """
        super().__init__(
            strategy_id=strategy_id,
            symbol_ids=[symbol_id],
            order_type=ParentOrderType.STANDARD
        )

        #create the child order here
        self.child_order = ChildOrder(
            strategy_id=strategy_id,
            parent_order_id=self.order_id,
            symbol_id=symbol_id,
            size=size,
            order_type=order_type,
            limit_price=limit_price,
            stop_price=stop_price
        )


    def process_market_data(self, symbol_id: str, timestamp: datetime, bid: float, ask: float):
        """Process market data and create child order when conditions are met.
        
        For NEW orders, this method will transition the order to WORKING status and 
        create an appropriate child order based on the order type.
        
        Args:
            symbol_id: Symbol identifier for the market data
            timestamp: Market data timestamp
            bid: Current bid price
            ask: Current ask price
        """
        if self.order_status == ParentOrderStatus.NEW:
            
            # Update parent order status
            self.order_status = ParentOrderStatus.WORKING

            #store child order in the dictionary
            self.child_orders[self.child_order.order_id] = self.child_order
            
            # Submit child order
            self.child_order_submission_callback(self.child_order)


    def process_fill(self, child_order: ChildOrder):
        """Process a fill report from the execution engine.
        
        Updates the child order status and marks the parent order as completed
        when the child order is fully filled.
        
        Args:
            child_order: Child order with updated fill information
        """
        # Update the child order in our dictionary
        self.child_orders[child_order.order_id] = child_order
        
        # Check if all child orders are filled
        if child_order.status == ChildOrderStatus.FILLED or child_order.status == ChildOrderStatus.CANCELED:
            self.order_status = ParentOrderStatus.COMPLETED


    def cancel_order(self):
        """Cancel the parent order."""
        if self.order_status != ParentOrderStatus.COMPLETED:
            self.order_status = ParentOrderStatus.CANCELATION_REQUESTED

            #cancel child order if this parent order is not completed yet
            #check if child order is not filled
            if self.child_order.status != ChildOrderStatus.FILLED:
                #set order status to cancelation requested
                self.child_order.status = ChildOrderStatus.CANCELATION_REQUESTED
                #submit child order for cancellation
                self.child_order_submission_callback(self.child_order)


        else:
            raise ValueError(f"Cannot cancel completed parent order with id:{self.order_id}")
        