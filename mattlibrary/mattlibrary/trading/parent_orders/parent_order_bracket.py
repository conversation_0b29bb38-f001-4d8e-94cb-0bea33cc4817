"""Bracket Parent Order class for representing bracket orders with profit target and stop loss."""
from datetime import datetime

from mattlibrary.trading.parent_order_type import ParentOrderType
from mattlibrary.trading.parent_order_status import ParentOrderStatus
from mattlibrary.trading.child_order import <PERSON><PERSON><PERSON><PERSON>
from mattlibrary.trading.child_order_type import Child<PERSON><PERSON>rType
from mattlibrary.trading.child_order_status import Child<PERSON><PERSON>r<PERSON>tat<PERSON>
from mattlibrary.trading.parent_order_base import ParentOrderBase


class ParentOrderBracket(ParentOrderBase):
    """Represents a bracket parent order that places a market order with profit target and stop loss.
    
    This class creates three child orders:
    1. Initial market order for the specified size
    2. Profit target order (limit for buy, stop for sell) 
    3. Stop loss order (stop for buy, limit for sell)
    """
    
    def __init__(self, 
                 strategy_id: str,
                 symbol_id: str,
                 core_size: int,
                 bracket_size: int,
                 profit_target_percent: float,
                 stop_loss_percent: float,
                 core_order_type: ChildOrderType,
                 limit_price: float = None,
                 stop_price: float = None):
        """Initialize a new ParentOrderBracket instance.
        
        Args:
            strategy_id: Identifier for the strategy submitting the order
            symbol_id: Trading symbol identifier  
            core_size: Order size (positive for buy, negative for sell)
            bracket_size: Order size (positive for buy, negative for sell)
            core_order_type: Type of core order
            profit_target_percent: Percentage above/below entry for profit target
            stop_loss_percent: Percentage below/above entry for stop loss
            limit_price: Optional limit price for the core order
            stop_price: Optional stop price for the core order
        """
        super().__init__(
            strategy_id=strategy_id,
            symbol_ids=[symbol_id],
            order_type=ParentOrderType.BRACKET 
        )
        
        # Create the core order (will be submitted later)
        self.core_order = ChildOrder(
            strategy_id=strategy_id,
            parent_order_id=self.order_id,
            symbol_id=symbol_id,
            size=core_size,
            order_type=core_order_type,
            limit_price=limit_price,
            stop_price=stop_price, 
            annotation="core_order"
        )

        # Create the profit target and stop loss orders (will be submitted later)
        self.profit_target_order = ChildOrder(
            strategy_id=strategy_id,
            parent_order_id=self.order_id,
            symbol_id=symbol_id,
            size=bracket_size,
            order_type=ChildOrderType.LIMIT,
            limit_price= (1 + profit_target_percent) if core_size > 0 else (1 - profit_target_percent), #will be multiplied by core_order filled price when core order is filled
            stop_price=None,
            annotation="profit_target"
        )

        # Create the stop loss order (will be submitted later)
        self.stop_loss_order = ChildOrder(
            strategy_id=strategy_id,
            parent_order_id=self.order_id,
            symbol_id=symbol_id,
            size=bracket_size,
            order_type=ChildOrderType.STOP,
            limit_price=None,
            stop_price= (1 - stop_loss_percent) if core_size > 0 else (1 + stop_loss_percent), #will be multiplied by core_order filled price when core order is filled
            annotation="stop_loss"
        )
        

    def cancel_order(self):
        """Cancel the parent order."""
        if self.order_status == ParentOrderStatus.COMPLETED:
            raise ValueError(f"Cannot cancel completed parent order with id:{self.order_id}")
        
        # Update parent order status
        self.order_status = ParentOrderStatus.CANCELATION_REQUESTED

        #cancel child order if this parent order is not completed yet
        for child_order in self.child_orders.values():
                #check if child order is not filled
                if child_order.status is not ChildOrderStatus.FILLED and child_order.status is not ChildOrderStatus.CANCELED:
                    #set order status to cancelation requested
                    child_order.status = ChildOrderStatus.CANCELATION_REQUESTED
                    #submit child order for cancellation
                    self.child_order_submission_callback(child_order)
        

    def process_market_data(self, symbol_id: str, timestamp: datetime, bid: float, ask: float):
        """Process market data and submit core order on first call.
        
        On the first call (when order status is NEW), this method will submit
        the core order. The execution plugin will handle the actual execution based on
        the order type and market conditions.
        
        Args:
            symbol_id: Symbol identifier for the market data
            timestamp: Market data timestamp
            bid: Current bid price
            ask: Current ask price
        """
        
        if self.order_status == ParentOrderStatus.NEW:
            # Update parent order status
            self.order_status = ParentOrderStatus.WORKING

            # Store core order in the dictionary
            self.child_orders[self.core_order.order_id] = self.core_order

            # Invoke callback to submit the core order
            self.child_order_submission_callback(self.core_order)


    def process_fill(self, child_order: ChildOrder):
        """Process a fill report from the execution engine.
        
        Updates the child order status and handles bracket order logic:
        1. If core order is filled, create and submit bracket orders
        2. If one bracket order is filled, cancel the other bracket order
        3. Mark parent order as completed only when core order is filled AND
           one bracket order is filled AND the other bracket order is canceled
        
        Args:
            child_order: Child order with updated fill information
        """
        
        # Update the child order in our dictionary
        self.child_orders[child_order.order_id] = child_order
        
        if child_order.order_id == self.core_order.order_id and child_order.status == ChildOrderStatus.FILLED:
            # Core order filled => create and submit bracket orders

            # configure bracket order limit and stop prices based on the fill price of the core order
            self.profit_target_order.limit_price = child_order.filled_price * self.profit_target_order.limit_price
            self.stop_loss_order.stop_price = child_order.filled_price * self.stop_loss_order.stop_price
            
            # Store bracket orders in the dictionary
            self.child_orders[self.profit_target_order.order_id] = self.profit_target_order
            self.child_orders[self.stop_loss_order.order_id] = self.stop_loss_order

            # Invoke callback to submit the bracket orders
            self.child_order_submission_callback(self.profit_target_order)
            self.child_order_submission_callback(self.stop_loss_order)

        elif child_order.status == ChildOrderStatus.FILLED:
            # One of the bracket orders filled => cancel the other bracket order

            if child_order.order_id == self.profit_target_order.order_id:
                # Profit target filled, cancel stop loss
                if self.stop_loss_order.status not in [ChildOrderStatus.FILLED, ChildOrderStatus.CANCELED]:
                    self.stop_loss_order.status = ChildOrderStatus.CANCELATION_REQUESTED
                    self.child_order_submission_callback(self.stop_loss_order)
            elif child_order.order_id == self.stop_loss_order.order_id:
                # Stop loss filled, cancel profit target
                if self.profit_target_order.status not in [ChildOrderStatus.FILLED, ChildOrderStatus.CANCELED]:
                    self.profit_target_order.status = ChildOrderStatus.CANCELATION_REQUESTED
                    self.child_order_submission_callback(self.profit_target_order)
        else:
            #mark parent order completed if all child orders are filled or canceled
            if all((child_order.status is ChildOrderStatus.FILLED or child_order.status is ChildOrderStatus.CANCELED) for child_order in self.child_orders.values()):
                self.order_status = ParentOrderStatus.COMPLETED
            