"""A class for managing a collection of financial instrument symbols."""

import os
import logging
import polars as pl
from mattlibrary.trading.symbol import Symbol


class SymbolDatabase:
    """
    A class for managing a collection of financial instrument symbols.
    
    This class provides methods for adding, removing, and retrieving symbols.
    Symbols are stored in a dictionary for efficient lookup.
    
    Attributes:
        symbol_dictionary (dict): Dictionary containing symbol objects indexed by symbolId
    """

    def __init__(self, directory: str):
        
        self.logger = logging.getLogger(__name__)
        self.path_filename = os.path.join(directory, "symbol_database.parquet")
        
        #attempt to load data from parquet file
        self.symbol_dictionary = self._load_symbols_from_parquet()


    def _load_symbols_from_parquet(self) -> dict[str, Symbol]:
        
        dictionary = dict()
        
        try:
            if os.path.exists(self.path_filename):
                symbol_df = pl.read_parquet(self.path_filename)

                #cast to symbol objects and return as dictionary
                for row in symbol_df.iter_rows(named=True):
                    dictionary[row["symbolId"]] = Symbol(
                        row["symbolId"],
                        row["underlying_symbol"],
                        row["assetClass"],
                        row["baseCurrency"],
                        row["exchange"],
                        row["tradingClass"],
                        row["localSymbol"],
                        row["lastTradeDateOrContractMonth"],
                        row["putCall"],
                        row["strike"],
                        row["multiplier"]
                    )

                self.logger.info(f"Loaded {len(dictionary)} symbols from {self.path_filename}")
            else:
                self.logger.warning(f"Could not load any symbols as symbol database file not found: {self.path_filename}")            
        except Exception as e:
            self.logger.error(f"Could not load symbols from {self.path_filename}: {e}")

        return dictionary


    def _store_symbols_in_parquet(self):
        #create dataframe that contains all symbols (each attribute in a column)
        symbol_df = pl.from_records([symbol.__dict__ for symbol in self.symbol_dictionary.values()])
        symbol_df.write_parquet(self.path_filename)


    def get_symbol_dictionary(self) -> dict[str, Symbol]:
        return self.symbol_dictionary
        

    def add_symbols(self, symbols:list[Symbol]):
        
        #add symbols to dictionary
        for symbol in symbols:
            self.symbol_dictionary[symbol.symbolId] = symbol
        
        #write to parquet file
        self._store_symbols_in_parquet()
        self.logger.info(f"Added {len(symbols)} symbols to symbol database")


    def update_all_symbols(self, symbols:dict[str, Symbol]):
        self.symbol_dictionary = symbols
        self._store_symbols_in_parquet()
        self.logger.info(f"Updated all symbols in symbol database")

    
    def remove_symbols(self, symbols:list[Symbol]):

        #remove symbols from dictionary (check whether symbol exists first)
        for symbol in symbols:
            if symbol.symbolId in self.symbol_dictionary:
                del self.symbol_dictionary[symbol.symbolId]
        
        #write to parquet file
        self._store_symbols_in_parquet()
        self.logger.info(f"Removed {len(symbols)} symbols from symbol database - remaining symbols: {len(self.symbol_dictionary)}")

    
