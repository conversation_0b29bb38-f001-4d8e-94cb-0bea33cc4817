"""Child Order class for representing executable orders with specific execution parameters."""

from datetime import datetime
import uuid

from mattlibrary.trading.child_order_type import ChildOrderType
from mattlibrary.trading.child_order_status import ChildOrderStatus


class ChildOrder:
    """Represents an executable order with specific execution parameters."""

    def __init__(self, 
                 strategy_id: str,
                 parent_order_id: str,
                 symbol_id: str,
                 size: int,
                 order_type: ChildOrderType,
                 limit_price: float = None,
                 stop_price: float = None,
                 annotation: str = None):
        """Initialize a new ChildOrder instance.

        Args:
            strategy_id: Identifier for the strategy submitting the order
            parent_order_id: Identifier for the parent order
            symbol_id: Trading symbol identifier
            size: Order size
            order_type: Type of child order
            limit_price: Price for limit orders (required for LIMIT orders)
            stop_price: Price for stop orders (required for STOP orders)
        """

        self.order_id = str(uuid.uuid4())  # Unique identifier for the child order
        self.strategy_id = strategy_id
        self.parent_order_id = parent_order_id
        self.symbol_id = symbol_id
        self.order_type = order_type
        self.status = ChildOrderStatus.NEW
        self.annotation = annotation
        self.size = size
        self.filled_size = 0
        self.filled_price = None
        self.limit_price = limit_price
        self.stop_price = stop_price
        self.is_stop_limit_stop_triggered = False
        self.timestamp_submission = None # market data timestamp upon submission
        self.timestamp_filled = None # market data timestamp upon fill
        self.timestamp_canceled  = None # market data timestamp upon cancellation
        self.timestamp_modified = None # market data timestamp upon modification


    def __str__(self) -> str:
        """Return a string representation of the order."""
        return (
            f"ChildOrder["
            f"symbol={self.symbol_id}, "
            f"type={self.order_type.name}, "
            f"status={self.status.name}, "
            f"annotation={self.annotation}, "
            f"size={self.size}, "
            f"filled_size={self.filled_size}, "
            f"filled_price={self.filled_price}, "
            f"limit_price={self.limit_price}, "
            f"stop_price={self.stop_price}, "
            f"timestamp_submission={self.timestamp_submission}, "
            f"timestamp_filled={self.timestamp_filled}, "
            f"timestamp_canceled={self.timestamp_canceled}, "
            f"timestamp_modified={self.timestamp_modified}, "
            f"strategy={self.strategy_id}, "
            f"parent_order_id={self.parent_order_id}, "
            f"child_order_id={self.order_id}, "
            f"]"
        )
