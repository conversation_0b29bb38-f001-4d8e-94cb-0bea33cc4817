"""Quote class for representing quotes for financial instruments."""

from datetime import datetime
from enum import StrEnum


class QuoteType(StrEnum):
    """Enumeration of quote types."""
    BID_ASK = "bid_ask"
    MID = "mid"
    TRADE = "trade"
    OHLC = "ohlc"


class Quote:
    """Represents a quote for a financial instrument."""
    symbol_id: str
    quote_type: QuoteType
    timestamp: datetime
    bid: float
    ask: float
    mid: float
    trade: float
    open:  float
    high: float
    low: float
    close: float
    bidSize: int
    askSize: int
    size: int
    

    def __init__(self, symbol_id: str, quote_type: QuoteType, timestamp: datetime, bid: float=0.0, ask: float=0.0, 
                 mid: float=0.0, trade: float=0.0, open: float=0.0, high: float=0.0, low: float=0.0, close: float=0.0, 
                 bidSize: int=0, askSize: int=0, size: int=0):
        self.symbol_id = symbol_id
        self.quote_type = quote_type
        self.timestamp = timestamp
        self.bid = bid
        self.ask = ask
        self.mid = mid
        self.trade = trade
        self.open = open
        self.high = high
        self.low = low
        self.close = close
        self.bidSize = bidSize
        self.askSize = askSize
        self.size = size
        

    def __repr__(self):
        if self.quote_type == QuoteType.BID_ASK:
            return f"Quote(symbol_id={self.symbol_id}, quote_type={self.quote_type}, timestamp={self.timestamp}, bid={self.bid}, ask={self.ask}, bidSize={self.bidSize}, askSize={self.askSize})"
        elif self.quote_type == QuoteType.MID:
            return f"Quote(symbol_id={self.symbol_id}, quote_type={self.quote_type}, timestamp={self.timestamp}, mid={self.mid}, size={self.size})"
        elif self.quote_type == QuoteType.TRADE:
            return f"Quote(symbol_id={self.symbol_id}, quote_type={self.quote_type}, timestamp={self.timestamp}, trade={self.trade}, size={self.size})"
        elif self.quote_type == QuoteType.OHLC:
            return f"Quote(symbol_id={self.symbol_id}, quote_type={self.quote_type}, timestamp={self.timestamp}, open={self.open}, high={self.high}, low={self.low}, close={self.close})"
        else:
            raise ValueError(f"Unsupported quote type: {self.quote_type}")
