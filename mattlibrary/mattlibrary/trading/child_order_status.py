"""Enumeration of child order statuses for trading system."""
from enum import StrEnum

class ChildOrderStatus(StrEnum):
    """Enumeration of child order statuses.
    
    Attributes:
        NEW: Order has been created but not yet processed
        SUBMITTED: Order has been submitted to the execution engine
        PARTIALLY_FILLED: Order has been partially filled
        FILLED: Order has been completely filled
        CANCELED: Order has been canceled
        REJECTED: Order has been rejected
    """
    NEW = 'new'
    SUBMITTED = 'submitted'
    CANCELATION_REQUESTED = 'cancelation_requested'
    FILLED = 'filled'
    CANCELED = 'canceled'
