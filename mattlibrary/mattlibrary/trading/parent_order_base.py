"""Base Parent Order class for representing orders at parent level."""
from abc import ABC, abstractmethod
from datetime import datetime
import uuid
from typing import Callable, Dict, List

from mattlibrary.trading.parent_order_type import ParentOrderType
from mattlibrary.trading.parent_order_status import ParentOrderStatus
from mattlibrary.trading.child_order import ChildOrder


class ParentOrderBase(ABC):
    """Abstract base class for parent orders."""
    
    def __init__(self, 
                 strategy_id: str,
                 symbol_ids: List[str],
                 order_type: ParentOrderType):
        """Initialize a new ParentOrderBase instance.
        
        Args:
            strategy_id: Identifier for the strategy submitting the order
            symbol_ids: List of trading symbol identifiers
            order_type: Type of parent order
        """
        self.strategy_id = strategy_id
        self.order_id = str(uuid.uuid4())
        self.symbol_ids = symbol_ids
        self.order_type = order_type
        self.order_status = ParentOrderStatus.NEW
        self.child_orders: Dict[str, ChildOrder] = dict()  # childorder_id -> ChildOrder
        self.child_order_submission_callback = None


    def register_child_order_submission_callback(self, callback: Callable[[ChildOrder], None]):
        """Register a callback function for child order handling."""
        self.child_order_submission_callback = callback
    

    @abstractmethod
    def cancel_order(self) -> None:
        """Cancel the parent order if it is not completed yet."""
        pass


    @abstractmethod
    def process_fill(self, child_order: ChildOrder) -> None:
        """Process a fill report from the execution engine.
        
        This method must be implemented by subclasses to define order-specific fill handling logic.
        
        Args:
            child_order: The child order that was filled
        """
        pass
    
    
    @abstractmethod
    def process_market_data(self, symbol_id: str, timestamp: datetime, bid: float, ask: float) -> None:
        """Process market data and trigger order execution if conditions are met.
        
        This method must be implemented by subclasses to define order-specific execution logic.
        
        Args:
            symbol_id: Trading symbol identifier
            timestamp: Timestamp of the market data
            bid: Current bid price
            ask: Current ask price
        """
        pass
