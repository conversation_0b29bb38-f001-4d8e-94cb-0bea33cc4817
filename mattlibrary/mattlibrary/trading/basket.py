"""Basket trading utilities for managing groups of related financial instruments."""

import polars as pl
import numpy as np

class Basket:
    """A class for managing baskets of related financial instruments.
    
    This class handles the creation and management of baskets of financial instruments,
    particularly useful for currency pairs and other related assets.
    
    Attributes:
        basket_symbol (str): The identifier for the basket (e.g., 'EUR', 'JPY')
        constituent_symbols (list): List of symbols that make up the basket
    """
    
    def __init__(self, basket_symbol : str, constituent_symbols : list):
        """Initialize a new Basket instance.
        
        Args:
            basket_symbol (str): The identifier for the basket
            constituent_symbols (list): List of symbols that make up the basket
        """
        self.basket_symbol = basket_symbol
        self.constituent_symbols = constituent_symbols
                
                
    def build_basket_series_from_prices(self, df:pl.DataFrame, prefix:str="", suffix:str="") -> pl.Series:
        """Build a basket series from a dataframe containing constituent series.
        
        This method calculates the basket series by aggregating the constituent series
        based on their relationship with the basket symbol.
        
        Args:
            df (pl.DataFrame): The dataframe containing the constituent series
            
        Returns:
            The basket series
        """

        #get column name
        column_name = f"{prefix}{self.constituent_symbols[0]}{suffix}"
        series = self._convert_prices(self.constituent_symbols[0], df[column_name])

        for const_symbol in self.constituent_symbols[1:]:
            column_name = f"{prefix}{const_symbol}{suffix}"
            series += self._convert_prices(const_symbol, df[column_name])
        series /= len(self.constituent_symbols)
        return series.alias(self.basket_symbol)
    

    def build_basket_series_from_log_returns(self, df:pl.DataFrame) -> pl.Series:
        """Build a basket series from a dataframe containing constituent series.
        
        This method calculates the basket series by aggregating the constituent series
        based on their relationship with the basket symbol. If the last 3 characters of any of the 
        constituent symbols match the basket symbol, then multiply the series by -1 before adding the series
        
        Args:
            df (pl.DataFrame): The dataframe containing the constituent series
            
        Returns:
            The basket series
        """

        column_name = [col for col in df.columns if self.constituent_symbols[0] in col][0]
        if self.constituent_symbols[0][-3:] == self.basket_symbol:
            series = -1 * df[column_name]
        else:
            series = df[column_name]        

        for const_symbol in self.constituent_symbols[1:]:
            column_name = [col for col in df.columns if const_symbol in col][0]
            if const_symbol[-3:] == self.basket_symbol:
                series += -1 * df[column_name]
            else:
                series += df[column_name]
            
        series /= len(self.constituent_symbols)
        return series.alias(self.basket_symbol)


    def _convert_prices(self, const_symbol, const_series: pl.Series) -> pl.Series:
        """Convert a price series based on the basket and constituent symbol relationship.
        
        This method handles the conversion of price series for different currency pairs,
        taking into account the relationship between the basket symbol and constituent symbols.
        
        Args:
            const_symbol (str): The constituent symbol to convert
            const_series: The price series to convert
            
        Returns:
            The converted price series
            
        Note:
            - For JPY pairs, special handling is applied (division by 100)
            - For other pairs, the conversion depends on whether the basket symbol matches
              the first three characters of the constituent symbol
        """
        if "JPY" in const_symbol:
            if self.basket_symbol == "JPY":
                return 100 / const_series
            else:
                return const_series / 100
        else:
            if self.basket_symbol == const_symbol[0:3]:
                return const_series
            else:
                return 1 / const_series      
    