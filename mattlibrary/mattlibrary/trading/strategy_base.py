"""Base class for all trading strategies."""
import uuid
from abc import ABC, abstractmethod
from typing import Dict, List, Callable
from mattlibrary.trading.position import Position
from mattlibrary.trading.parent_order_base import ParentOrderBase


class StrategyBase(ABC):
    """Abstract base class for all trading strategies."""


    def __init__(self, description: str, symbols: List[str]):
        """
        Initialize the base strategy.
        
        Args:
            description: Strategy description
            symbols: List of symbols to trade
        """
        self.description = description
        self.symbols = symbols
        self.strategy_id = str(uuid.uuid4())
        self.order_submission_callback = None
        self.positions: Dict[str, Position] = {} # symbol_id -> Position
        self.working_parent_orders: Dict[str, ParentOrderBase] = {} # order_id -> ParentOrderBase


    def configure_strategy(self, order_submission_callback: Callable, positions: Dict[str, Position]):
        """Configure the strategy with callbacks and positions."""
        self.order_submission_callback = order_submission_callback
        self.positions = positions


    def remove_order_submission_callback(self):
        """Remove the order submission callback."""
        self.order_submission_callback = None
        

    def parent_order_completed_notification(self, parent_order: ParentOrderBase):
        """Notify the strategy that a parent order has been completed."""
        #remove parent order from working orders
        del self.working_parent_orders[parent_order.order_id]


    def _submit_parent_order(self, parent_order: ParentOrderBase):
        """Submit a parent order to the trading engine."""
        #store parent order in working orders
        self.working_parent_orders[parent_order.order_id] = parent_order

        #submit parent order to trading engine
        self.order_submission_callback(parent_order)


    @abstractmethod
    def process_data(self, row):
        """
        Process a single data row and generate trading signals.
        
        Args:
            row: Data row containing market data
        """
        pass


    @abstractmethod
    def finalize(self):
        """Finalize the strategy."""
        pass