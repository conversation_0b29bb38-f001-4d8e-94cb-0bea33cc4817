"""Position data class for representing a position's state."""
from datetime import datetime
import uuid

class Position:
    """Represents a position's state."""

    position_id: str
    strategy_id: str
    symbol_id: str
    timestamp_current: datetime
    timestamp_first_fill: datetime
    size: int
    average_price: float
    current_price: float
    unrealized_pnl_local: float
    unrealized_pnl_base: float
    realized_pnl_local: float
    realized_pnl_base: float

    def __init__(self, strategy_id: str, symbol_id: str):
        """Initialize a new Position instance.
        
        Args:
            strategy_id: Identifier for the strategy associated with the position
            symbol_id: Identifier for the financial instrument (symbol)
        """
        self.position_id = str(uuid.uuid4())
        self.strategy_id = strategy_id
        self.symbol_id = symbol_id
        self.timestamp_current = None
        self.timestamp_first_fill = None
        self.size = 0
        self.average_price = 0.0
        self.current_price = 0.0
        self.unrealized_pnl_local = 0.0
        self.unrealized_pnl_base = 0.0
        self.realized_pnl_local = 0.0
        self.realized_pnl_base = 0.0


    def __repr__(self):
        """Return a string representation of the position."""
        position_str = (
            f"Position(position_id={self.position_id}, "
            f"strategy_id={self.strategy_id}, symbol_id={self.symbol_id}, size={self.size}, "
            f"unrealized_pnl_local={self.unrealized_pnl_local}, unrealized_pnl_base={self.unrealized_pnl_base}, " 
            f"realized_pnl_local={self.realized_pnl_local}, realized_pnl_base={self.realized_pnl_base}, " 
            f"timestamp_current={self.timestamp_current}, timestamp_first_fill={self.timestamp_first_fill}, "
            f"average_price={self.average_price}, current_price={self.current_price}"
        )
        return position_str
        

    def to_dict(self):
        """Convert to dictionary for JSON serialization."""
        return {
            "position_id": self.position_id,
            "strategy_id": self.strategy_id,
            "symbol_id": self.symbol_id,
            "timestamp_current": self.timestamp_current.isoformat() if self.timestamp_current else None,
            "timestamp_first_fill": self.timestamp_first_fill.isoformat() if self.timestamp_first_fill else None,
            "size": self.size,
            "average_price": self.average_price,
            "current_price": self.current_price,
            "unrealized_pnl_local": self.unrealized_pnl_local,
            "unrealized_pnl_base": self.unrealized_pnl_base,
            "realized_pnl_local": self.realized_pnl_local,
            "realized_pnl_base": self.realized_pnl_base,
        }
    

    @classmethod
    def from_dict(cls, data: dict):
        """Create Position from dictionary."""
        pos = cls(data["strategy_id"], data["symbol_id"])
        pos.position_id = data["position_id"]
        pos.timestamp_current = datetime.fromisoformat(data["timestamp_current"]) if data["timestamp_current"] else None
        pos.timestamp_first_fill = datetime.fromisoformat(data["timestamp_first_fill"]) if data["timestamp_first_fill"] else None
        pos.size = data["size"]
        pos.average_price = data["average_price"]
        pos.current_price = data["current_price"]
        pos.unrealized_pnl_local = data["unrealized_pnl_local"]
        pos.unrealized_pnl_base = data["unrealized_pnl_base"]
        pos.realized_pnl_local = data["realized_pnl_local"]
        pos.realized_pnl_base = data["realized_pnl_base"]
        return pos
    