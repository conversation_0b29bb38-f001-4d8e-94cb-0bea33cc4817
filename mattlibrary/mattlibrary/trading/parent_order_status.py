"""Enumeration of parent order statuses for trading system."""
from enum import StrEnum

class ParentOrderStatus(StrEnum):
    """Enumeration of parent order statuses.
    
    Attributes:
        NEW: Order has been created but not yet processed
        WORKING: Order is being processed
        COMPLETED: Order has been completely filled or canceled
    """
    NEW = "new"
    WORKING = "working"
    CANCELATION_REQUESTED = "cancelation_requested"
    COMPLETED = "completed"