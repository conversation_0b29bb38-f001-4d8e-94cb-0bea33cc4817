"""Base class for execution plugins."""
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Callable

from mattlibrary.trading.child_order import ChildOrder


class ExecutionPluginBase(ABC):
    """Abstract base class for execution plugins.
    
    This class defines the interface that all execution plugins must implement.
    Execution plugins handle the submission and execution of orders based on
    market conditions.
    """
    def __init__(self):
        """Initialize a new execution plugin."""
        

    @abstractmethod
    def register_fill_callback(self, callback: Callable[[ChildOrder], None]):
        """Register a callback function to notify when an order is filled, canceled, or rejected.
        
        Args:
            callback: Callback function to notify when an order is filled, canceled, or rejected
        """
        pass


    @abstractmethod
    def submit_child_order(self, child_order: ChildOrder):
        """Submit a child order for execution.
        
        Args:
            child_order: The order to submit
        """
        pass
    

    @abstractmethod
    def process_market_data(self, symbol_id: str, timestamp: datetime, bid: float, ask: float):
        """Process market data and trigger order execution if conditions are met.
        
        Args:
            symbol_id: The symbol identifier
            timestamp: The timestamp of the market data
            bid: The current bid price
            ask: The current ask price
        """
        pass