"""Simulated Execution Engine for backtesting."""
import logging
from datetime import datetime
from typing import Callable, Dict, Tuple
from mattlibrary.trading.child_order import ChildOrder
from mattlibrary.trading.child_order_type import ChildOrderType
from mattlibrary.trading.child_order_status import Child<PERSON><PERSON><PERSON><PERSON>tatus
from mattlibrary.trading.execution_plugin_base import ExecutionPluginBase


class SimulatedExecutionEngine(ExecutionPluginBase):
    """Simulated execution engine for backtesting.
    
    This class simulates order execution based on market data. It maintains a collection
    of pending orders and executes them according to their type and market conditions.
    """
    
    def __init__(self):
        """Initialize a new SimulatedExecutionEngine."""
        super().__init__()
        self.logger = logging.getLogger(__name__)

        self.fill_callback : Callable[[ChildOrder], None] = None
        self.pending_orders: Dict[str, Dict[str, ChildOrder]] = {}  # symbol_id -> {order_id -> ChildOrder}
        self.price_cache: Dict[str, Tuple[datetime, float, float]] = {}  # symbol_id -> (timestamp, bid, ask)

        self.logger.info("SimulatedExecutionEngine initialized")


    def register_fill_callback(self, callback: Callable[[ChildOrder], None]):
        """Register a callback function to notify when an order is filled, canceled, or rejected.
        
        Args:
            callback: Callback function to notify when an order is filled, canceled, or rejected
        """
        self.fill_callback = callback


    def submit_child_order(self, child_order: ChildOrder):
        """Submit a child order for execution.
        
        Args:
            child_order: The order to submit
        """
        #check whether cancellation is requested
        if child_order.status == ChildOrderStatus.CANCELATION_REQUESTED:
            self._cancel_order(child_order)
            return

        # try to execute the order immediately
        is_filled = self._try_execute_order(child_order)
        if is_filled:
            # pass filled child order back to the trading engine
            self.logger.info(f"Order was executed immediately: {child_order}")
            self.fill_callback(child_order)

        else:
            # store the order for later execution
            if child_order.symbol_id not in self.pending_orders:
                self.pending_orders[child_order.symbol_id] = {}
            self.pending_orders[child_order.symbol_id][child_order.order_id] = child_order
            self.logger.info(f"Order could not be executed immediately and was added to pending queue: {child_order}")    


    def process_market_data(self, symbol_id: str, timestamp: datetime, bid: float, ask: float):
        """Process market data and trigger order execution if conditions are met.
        
        Args:
            symbol_id: The symbol identifier
            timestamp: The timestamp of the market data
            bid: The current bid price
            ask: The current ask price
        """
        # Update price cache
        self.price_cache[symbol_id] = (timestamp, bid, ask)
        
        # Skip if no orders for this symbol
        if symbol_id not in self.pending_orders:
            return
        
        # Get orders for this symbol
        order_ids = list(self.pending_orders[symbol_id].keys())
        
        # Process each order 
        for order_id in order_ids:
            
            #check if order was already filled or cancelled (it can happen that orders get removed from pending_orders during this iteration)
            if order_id not in self.pending_orders[symbol_id]:
                continue

            #get the order
            order = self.pending_orders[symbol_id][order_id]

            #try to execute the order
            was_filled = self._try_execute_order(order)

            if was_filled:
                self.logger.info(f"Order was filled: {order}")
                # Remove from pending orders
                del self.pending_orders[symbol_id][order_id]
                # Pass filled child order back to the trading engine
                self.fill_callback(order)


    def _try_execute_order(self, order: ChildOrder) -> bool:
        """Try to execute an order immediately based on market conditions.
        
        Args:
            order: The order to execute
            
        Returns:
            bool: True if order was filled, False otherwise
        """
        # Get cached price
        timestamp, bid, ask = self.price_cache[order.symbol_id]
        
        #attach submission timestamp and order_status to childorder
        order.timestamp_submission = timestamp
        order.status = ChildOrderStatus.SUBMITTED

        def fill_order(price: float) -> bool:
            """Helper function to fill an order with common logic."""
            order.filled_price = price
            order.filled_size = order.size  # assumption: order is always filled completely
            order.timestamp_filled = timestamp  # filled at the time of last market data update
            order.status = ChildOrderStatus.FILLED
            return True

        # Handle market orders
        if order.order_type == ChildOrderType.MARKET:
            return fill_order(ask if order.size > 0 else bid)

        # Handle stop limit orders - check and set trigger
        if order.order_type == ChildOrderType.STOPLIMIT:
            if (order.size > 0 and ask >= order.stop_price) or \
               (order.size < 0 and bid <= order.stop_price):
                order.is_stop_limit_stop_triggered = True

        # Handle limit and triggered stop-limit orders
        if order.order_type == ChildOrderType.LIMIT or \
           (order.order_type == ChildOrderType.STOPLIMIT and order.is_stop_limit_stop_triggered):
            if (order.size > 0 and ask <= order.limit_price):
                return fill_order(ask)
            if (order.size < 0 and bid >= order.limit_price):
                return fill_order(bid) #assume order is filled right at the limit price (order.limit_price)
                
        # Handle stop orders
        if order.order_type == ChildOrderType.STOP:
            if (order.size > 0 and ask >= order.stop_price):
                return fill_order(ask)
            if (order.size < 0 and bid <= order.stop_price):
                return fill_order(bid) #assume order is filled right at the stop price (order.stop_price)

        return False


    def _cancel_order(self, order: ChildOrder):
        """Cancel a pending order.
        
        Args:
            order: The order to cancel
        """
        # Get cached price
        if order.symbol_id in self.price_cache:
            timestamp, bid, ask = self.price_cache[order.symbol_id]

        if order.symbol_id not in self.pending_orders:
            self.logger.warning(f"Order Cancellation failed. Symbol not found: {order.symbol_id}")
            return
        
        if order.order_id not in self.pending_orders[order.symbol_id]:
            self.logger.warning(f"Order Cancellation failed. Order not found: {order.order_id}")
            return

        #set order status to canceled and set timestamps
        order.status = ChildOrderStatus.CANCELED
        order.timestamp_canceled = timestamp

        # Remove from pending orders
        del self.pending_orders[order.symbol_id][order.order_id]
        
        self.logger.info(f"Order canceled: {order}")
        self.fill_callback(order)
