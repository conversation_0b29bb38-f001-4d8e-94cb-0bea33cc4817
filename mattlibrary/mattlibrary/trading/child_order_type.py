"""Enumeration of child order types for trading system."""
from enum import StrEnum

class ChildOrderType(StrEnum):
    """Enumeration of child order types.
    
    Attributes:
        MARKET: Market order, executed immediately at current market price
        LIMIT: Limit order, executed only at specified price or better
        STOP: Stop order, triggers a market order when price reaches specified level
    """
    MARKET = 'market'
    LIMIT = 'limit'
    STOP = 'stop'
    STOPLIMIT = 'stoplimit'