import os
import polars as pl
import polars.selectors as cs
import logging
from datetime import datetime, date, time, timedelta
from zoneinfo import ZoneInfo
from mattlibrary.datamanagement.interactive_brokers import InteractiveBrokersAPI
from mattlibrary.trading.symbol import Forex

class Fx_Converter:
    """
    Currency conversion module for handling foreign exchange rate calculations.

    This class provides methods for converting between different currencies based on
    historical exchange rates. It uses pre-downloaded and pre-processed exchange rate
    data stored in a Parquet file.
    
    Attributes:
        base_currency (str): The base currency for conversion
        pricing_data (dict): Dictionary containing exchange rates indexed by date

        structure of pricing_data dictionary: 
            key (str): currency pair (e.g., 'EURUSD')
            value (dict): dictionary of dates and closing prices
                key (datetime.date): date
                value (float): closing price
    """
    
    def __init__(self, base_currency:str, base_directory:str = "."):
        
        self.logger = logging.getLogger(__name__)
        
        self.base_currency = base_currency
        self.path_filename = os.path.join(base_directory, "fx_converter_data.parquet")
        
        self.all_fx_symbolIds = ['FOREX_AUDUSD', 'FOREX_AUDNZD', 'FOREX_AUDCAD', 'FOREX_AUDCHF', 'FOREX_AUDJPY', 'FOREX_EURAUD', 'FOREX_GBPAUD',
              'FOREX_NZDUSD', 'FOREX_NZDCAD', 'FOREX_NZDCHF', 'FOREX_NZDJPY', 'FOREX_EURNZD', 'FOREX_GBPNZD', 
              'FOREX_CADCHF', 'FOREX_CADJPY', 'FOREX_EURCAD', 'FOREX_GBPCAD', 'FOREX_USDCAD',
              'FOREX_CHFJPY', 'FOREX_EURCHF', 'FOREX_GBPCHF', 'FOREX_USDCHF',
              'FOREX_EURJPY', 'FOREX_EURUSD', 'FOREX_EURGBP', 
              'FOREX_GBPJPY', 'FOREX_GBPUSD',
              'FOREX_USDJPY']
        
        self.fx_symbolIds = [x for x in self.all_fx_symbolIds if self.base_currency in x]

        #load pricing data
        self.pricing_data = self.load_fx_data()
        
    
    def load_fx_data(self) -> dict:
        """
        Loads the fx conversion data from the Parquet file (format: 'Date' column, and column for each fx pair)
        Converts the dataframe to a dictionary of type : 
            key (str): currency pair (e.g., 'EURUSD')
            value (dict): dictionary of dates and closing prices
                key (datetime.date): date
                value (float): closing price
        """
        fx_dict = dict()

        try:
            df = pl.read_parquet(self.path_filename)
        
            for col in df.columns:
                if col == "Date":
                    continue
                fx_dict[col] = dict(zip(df["Date"], df[col]))
            
            self.logger.info(f"Loaded fx conversion data ({len(df)} rows) from {self.path_filename}")
            return fx_dict
            
        except Exception as e:
            self.logger.error(f"Could not load fx data from {self.path_filename}: {e}")
            return fx_dict


    def reset_data_source(self, ib_ip:str, ib_port:int, ib_client_id:int, is_jupyter_env:bool):
        """
        Resets the fx conversion data source by fetching historical data from Interactive Brokers.
        Args:
            ib_ip (str): Interactive Brokers IP address
            ib_port (int): Interactive Brokers port
            ib_client_id (int): Interactive Brokers client id
            is_jupyter_env (bool): Whether the code is running in a Jupyter notebook
        """
        try:
            #parameters
            start_dt = datetime(2000, 1, 1).date()
            end_dt = datetime.now().date()
            symbolIds = self.fx_symbolIds

            #remove file if it exists
            if os.path.exists(self.path_filename) == True:
                #delete the file
                os.remove(self.path_filename)

            #connect to IB
            ib_api = InteractiveBrokersAPI(ib_ip, ib_port, ib_client_id, is_jupyter_env)
            ib_api.connect()

            #iterate over all symbols and fetch data
            source_df = None
            for symbolId in symbolIds:
                symbol = Forex(symbolId[-6:]) #last 6 characters of the symbolId
                df = ib_api.get_historical_bar_data(symbol, start_dt, end_dt, "1 day")
                
                df = df.select(                
                    pl.col("date").alias("Date"), 
                    pl.col("close").alias(symbol.symbolId))
                
                #update source_df
                if source_df is None:
                    source_df = df
                else:
                    #join (add column)
                    source_df = source_df.join(df, on="Date", how="full", coalesce=True)
                
                #sleep a bit
                ib_api.sleep(2)
                
            #disconnect from IB
            ib_api.disconnect()

            #cleanse dataframe
            source_df = source_df.drop_nulls().sort("Date")

            #write to parquet
            source_df.write_parquet(self.path_filename)
            self.logger.info(f"Successfully Updated Source FX Conversion Data")

            #update pricing data
            self.pricing_data = self.load_fx_data()

        except Exception as e:
            self.logger.error(f"Could not reset fx data: {e}")


    def refresh_data_source(self, ib_ip:str, ib_port:int, ib_client_id:int, is_jupyter_env:bool):
        
        try:
            if os.path.exists(self.path_filename) == False:
                self.logger.info(f"Source FX Conversion Data not found. Aborting refresh.")
                return

            #load dataset
            source_df = pl.read_parquet(self.path_filename)

            #parameters
            start_dt = (source_df.select(pl.col("Date").last()).item() + timedelta(days=1))
            end_dt = (datetime.now().date() - timedelta(days=1))
            symbolIds = [x for x in source_df.columns if x != "Date"]

            if start_dt >= end_dt:
                self.logger.info(f"Source FX Conversion Data is up to date. No new data to fetch.")
                return

            #connect to IB
            ib_api = InteractiveBrokersAPI(ib_ip, ib_port, ib_client_id, is_jupyter_env)
            ib_api.connect()

            #iterate over each fx symbol and fetch historical data
            new_df = None
            for symbolId in symbolIds:
                symbol = Forex(symbolId[-6:]) #last 6 characters of the symbolId
                df = ib_api.get_historical_bar_data(symbol, start_dt, end_dt, "1 day")
                
                df = df.select(                
                pl.col("date").alias("Date"), 
                pl.col("close").alias(symbol.symbolId))
            
                #update new_df
                if new_df is None:
                    new_df = df
                else:
                    #join (add column)
                    new_df = new_df.join(df, on="Date", how="full", coalesce=True)

                #sleep a bit
                ib_api.sleep(2)

            #disconnect from IB
            ib_api.disconnect()

            #merge dataframes and cleanse
            source_df = pl.concat([source_df, new_df], how="vertical")
            source_df = source_df.drop_nulls().unique(subset="Date").sort("Date")

            #write to parquet
            source_df.write_parquet(self.path_filename)
            self.logger.info(f"Successfully Updated Source FX Conversion Data")

            #update pricing data
            self.pricing_data = self.load_fx_data()

        except Exception as e:
            self.logger.error(f"Could not refresh fx data: {e}")


    def get_fx_conversion_rate(self, dt:date, from_currency:str):        
        
        if from_currency == self.base_currency:
            return 1.0

        from_to_ccy = f"FOREX_{from_currency}{self.base_currency}"
        to_from_ccy = f"FOREX_{self.base_currency}{from_currency}"
                
        if from_to_ccy in self.pricing_data:
            conversion_rate = self._find_fx_rate(from_to_ccy, dt.date())
        elif to_from_ccy in self.pricing_data:
            conversion_rate = 1 / self._find_fx_rate(to_from_ccy, dt.date())
        else:
            self.logger.error(f"Cannot find currency {from_currency} in table to convert fx rates")
            raise Exception(f"Cannot find currency {from_currency} in table to convert fx rates") 
        
        return conversion_rate


    def _find_fx_rate(self, ccy:str, dt:date):
    
        if dt in self.pricing_data[ccy]:
            return self.pricing_data[ccy][dt]
        
        #go back dates and find the closest date before requested date
        current_dt = dt
            
        for i in range(10):
            current_dt = current_dt - timedelta(days=1)
            if current_dt in self.pricing_data[ccy]:
                return self.pricing_data[ccy][current_dt]
            
        self.logger.error(f"Could not find fx rate for Currency: {ccy} and date: {str(dt)}")
        raise Exception(f"Could not find fx rate for Currency: {ccy} and date: {str(dt)}")