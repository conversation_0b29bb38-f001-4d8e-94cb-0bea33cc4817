"""Module for tracking and analyzing trading performance metrics and generating statistics."""
import logging
import os
import math
import polars as pl
from mattlibrary.helpers.datetime_helper import format_duration
from mattlibrary.trading.parent_order_base import ParentOrderBase
from mattlibrary.trading.trade import Trade
from mattlibrary.trading.position import Position
from mattlibrary.visualizations.line_scatter_chart import plot_scatter_chart
from mattlibrary.visualizations.bar_chart import plot_bar_chart
from mattlibrary.backtesting.backtest_configuration import BacktestConfiguration

class PerformanceMetrics():
    """Tracks and analyzes trading performance metrics including PnL, drawdowns, and trade statistics."""

    def __init__(self, backtest_configuration: BacktestConfiguration):
        self.logger = logging.getLogger(__name__)
        self.backtest_configuration = backtest_configuration

        #statistics dataframes
        self.child_orders = None
        self.trades = None
        self.positions = None
        self.data_series = None
        self.weekly_pnl_series = None
        self.monthly_pnl_series = None
        self.annual_pnl_series = None
        self.statistics_string_format = None
        self.statistics_numeric_format = None

    
    def generate_statistics(self, quote_count:int, parent_orders:list[ParentOrderBase], trades:list[Trade], positions:list[Position]):
        """Generates trading performance statistics including PnL, drawdowns, and trade statistics."""
        
        #abort if any of the passed lists dont contain any data
        if not parent_orders:
            self.logger.error("No parent orders have been recorded.")
            return
        if not trades:
            self.logger.error("No trades have been recorded.")
            return
        if not positions:
            self.logger.error("No positions have been recorded.")
            return
        if not (parent_orders and trades and positions):
            self.logger.error("Either no parent orders, trades, or positions have been recorded.")
            return

        # Convert dataclasses to DataFrames and add to statistics
        self.child_orders = pl.DataFrame([vars(child_order) for parent_order in parent_orders for child_order in parent_order.child_orders.values()])
        self.trades = pl.DataFrame([vars(trade) for trade in trades])
        self.positions = pl.DataFrame([vars(pos) for pos in positions])

        # Generate current_positions by getting the last position for each unique strategy_id and symbol_id combination
        current_positions = (self.positions
            .sort("timestamp_current")
            .group_by(["strategy_id", "symbol_id"])
            .last()
        )

        starting_balance = self.backtest_configuration.starting_balance

        #data series statistics
        daily_data_series = self.positions.sort("timestamp_current").group_by_dynamic("timestamp_current", every='1d').agg(
            pl.col("unrealized_pnl_base").add(pl.col("realized_pnl_base")).sum().alias("daily_pnl")).select(
            "timestamp_current", pl.col("daily_pnl").diff()).drop_nulls().sort("timestamp_current")
        
        daily_data_series = daily_data_series.with_columns(equity_curve=pl.col("daily_pnl").cum_sum() + starting_balance)
        daily_data_series = daily_data_series.with_columns(daily_return=pl.col("daily_pnl") / starting_balance)
        daily_data_series = daily_data_series.with_columns(cumulative_return=pl.col("daily_return").cum_sum())
        drawdown_data = self._calculate_drawdown(daily_data_series["equity_curve"].drop_nulls())
        daily_data_series = daily_data_series.with_columns(drawdown = pl.Series(drawdown_data[0]))

        self.data_series = daily_data_series
        self.weekly_pnl_series = daily_data_series.group_by_dynamic("timestamp_current", every='1w', start_by='monday').agg(pl.col("daily_return").sum().alias("weekly_pnl"))
        self.monthly_pnl_series = daily_data_series.group_by_dynamic("timestamp_current", every='1mo').agg(pl.col("daily_return").sum().alias("monthly_pnl"))
        self.annual_pnl_series = daily_data_series.group_by_dynamic("timestamp_current", every='1y').agg(pl.col("daily_return").sum().alias("annual_pnl"))
        
        # Calculate PnL values
        total_pnl = current_positions.select(pl.col("realized_pnl_base").add(pl.col("unrealized_pnl_base")).sum()).item()
        ending_balance = starting_balance + total_pnl
        total_return_pct = total_pnl / starting_balance
        
        # Calculate Sharpe ratio
        daily_return_mean = daily_data_series.select(pl.col("daily_return").mean()).item()
        daily_return_std = daily_data_series.select(pl.col("daily_return").std()).item()
        sharpe_ratio = daily_return_mean / daily_return_std * math.sqrt(252)

        # Compound Annual Growth Rate
        start_date = daily_data_series.select(pl.col("timestamp_current").first()).item()
        end_date = daily_data_series.select(pl.col("timestamp_current").last()).item()
        duration = end_date - start_date
        cagr_years = duration.days / 365.2425
        compound_annual_growth_rate = (ending_balance / starting_balance) ** (1 / cagr_years) - 1
        if self.can_be_float(compound_annual_growth_rate):
            compound_annual_growth_rate = float(compound_annual_growth_rate)
        else:
            compound_annual_growth_rate = 0.0

        # Average Annual Return
        average_annual_return = (ending_balance / starting_balance - 1) / cagr_years

        # Counts
        total_quotes = quote_count
        positions_count = len(current_positions)
        child_orders_count = len(self.child_orders)
        total_trades_count = len(self.trades)
        winning_trades_count = len(self.trades.filter(pl.col("pnl_base") >= 0))
        losing_trades_count = len(self.trades.filter(pl.col("pnl_base") < 0))

        # Trade Performance Statistics
        win_rate = winning_trades_count / total_trades_count
        avg_trade_pnl = self.trades.select(pl.col("pnl_base")).mean().item()
        avg_winning_trade_pnl = self.trades.filter(pl.col("pnl_base") >= 0).select(pl.col("pnl_base")).mean().item()
        avg_losing_trade_pnl = self.trades.filter(pl.col("pnl_base") < 0).select(pl.col("pnl_base")).mean().item()
        largest_winning_trade = self.trades.select(pl.col("pnl_base")).max().item()
        largest_losing_trade = self.trades.select(pl.col("pnl_base")).min().item()
        avg_trade_pnl_pct = self.trades.select(pl.col("pnl_local") / (pl.col("price_entry") * abs(pl.col("size")))).mean().item()
        avg_winning_trade_pnl_pct = self.trades.filter(pl.col("pnl_local") >= 0).select(pl.col("pnl_local") / (pl.col("price_entry") * abs(pl.col("size")))).mean().item()
        avg_losing_trade_pnl_pct = self.trades.filter(pl.col("pnl_local") < 0).select(pl.col("pnl_local") / (pl.col("price_entry") * abs(pl.col("size")))).mean().item()
        expectancy_per_trade = win_rate * avg_winning_trade_pnl_pct - (1 - win_rate) * abs(avg_losing_trade_pnl_pct)

        #TODO: r-multiple 

        # Durations
        avg_trade_duration = self.trades.select(pl.col("timestamp_exit").sub(pl.col("timestamp_entry")).mean()).item()
        min_trade_duration = self.trades.select(pl.col("timestamp_exit").sub(pl.col("timestamp_entry")).min()).item()
        max_trade_duration = self.trades.select(pl.col("timestamp_exit").sub(pl.col("timestamp_entry")).max()).item()
        max_drawdown_recovery = drawdown_data[1]
        
        #create metrics dataframes

        # Helper functions for formatting
        def currency_format(value, precision):
            return "$" + format(value, f",.{precision}f")

        def percent_format(value, precision):
            return format(value*100, f",.{precision}f") + " %"
        
        #list of metric tuples
        metrics = []

        # Create metrics tuples
        metrics.extend([
            ("Overall Profitability", None, "Overall Profitability", None),
            ("-------------", None, "-------------", None),
            ("Starting Balance", "starting_balance", currency_format(starting_balance, 0), starting_balance),
            ("Ending Balance", "ending_balance", currency_format(ending_balance, 0), ending_balance),
            ("Total Return ($)", "total_return", currency_format(total_pnl, 0), total_pnl),
            ("Total Return (%)", "total_return_pct", percent_format(total_return_pct, 2), total_return_pct),
            ("Sharpe Ratio", "sharpe_ratio", format(sharpe_ratio, ",.2f"), sharpe_ratio),
            ("Compound Annual Growth Rate", "compound_annual_growth_rate", percent_format(compound_annual_growth_rate, 2), compound_annual_growth_rate),
            ("Average Annual Return", "avg_annual_return", percent_format(average_annual_return, 2), average_annual_return),
            ("-------------", None, "-------------", None),
            ("Trade Performance", None, "Trade Performance", None),
            ("-------------", None, "-------------", None),
            ("Average Trade PnL ($)", "avg_trade_pnl_usd", currency_format(avg_trade_pnl, 2), avg_trade_pnl),
            ("Average Winning Trade PnL ($)", "avg_winning_trade_pnl_usd", currency_format(avg_winning_trade_pnl, 2), avg_winning_trade_pnl),
            ("Average Losing Trade PnL ($)", "avg_losing_trade_pnl_usd", currency_format(avg_losing_trade_pnl, 2), avg_losing_trade_pnl),
            ("Largest Winning Trade", "largest_winning_trade", currency_format(largest_winning_trade, 2), largest_winning_trade),
            ("Largest Losing Trade", "largest_losing_trade", currency_format(largest_losing_trade, 2), largest_losing_trade),
            ("Average Trade PnL (%)", "avg_trade_pnl_pct", percent_format(avg_trade_pnl_pct, 5), avg_trade_pnl_pct),
            ("Average Winning Trade PnL (%)", "avg_winning_trade_pnl_pct", percent_format(avg_winning_trade_pnl_pct, 5), avg_winning_trade_pnl_pct),
            ("Average Losing Trade PnL (%)", "avg_losing_trade_pnl_pct", percent_format(avg_losing_trade_pnl_pct, 5), avg_losing_trade_pnl_pct),
            ("Win Rate (%)", "win_rate", percent_format(win_rate, 2), win_rate),
            ("Loss Rate (%)", "loss_rate", percent_format(1 - win_rate, 2), 1 - win_rate),
            ("Expectancy Per Trade (%)", "expectancy_per_trade", percent_format(expectancy_per_trade, 5), expectancy_per_trade),
            ("-------------", None, "-------------", None),
            ("Counts", None, "Counts", None),
            ("-------------", None, "-------------", None),
            ("Total Number Quotes", "total_quotes", format(total_quotes, ","), total_quotes),
            ("Number Positions", "positions_count", format(positions_count, ","), positions_count),
            ("Number Child Orders", "child_orders_count", format(child_orders_count, ","), child_orders_count),
            ("Number Trades", "total_trades_count", format(total_trades_count, ","), total_trades_count),
            ("Winning Trades", "winning_trades_count", format(winning_trades_count, ","), winning_trades_count),
            ("Losing Trades", "losing_trades_count", format(losing_trades_count, ","), losing_trades_count),
            ("-------------", None, "-------------", None),
            ("Durations", None, "Durations", None),
            ("-------------", None, "-------------", None),
            ("Starting Date", "start_date", str(start_date), start_date),
            ("Ending Date", "end_date", str(end_date), end_date),
            ("Duration", "duration", format_duration(duration), duration),
            ("Average Trade Duration", "avg_trade_duration", format_duration(avg_trade_duration), avg_trade_duration),
            ("Min Trade Duration", "min_trade_duration", format_duration(min_trade_duration), min_trade_duration),
            ("Max Trade Duration", "max_trade_duration", format_duration(max_trade_duration), max_trade_duration),
            ("Max Drawdown Recovery Period", "max_drawdown_recovery", str(max_drawdown_recovery), max_drawdown_recovery)
        ])
        

        #create dataframe with string formatted values (col1=Statistic, col2=Value)
        stats_name_str = [metric[0] for metric in metrics]
        stats_value_str = [metric[2] for metric in metrics]
        self.statistics_string_format = pl.DataFrame({"Statistic": stats_name_str, "Value": stats_value_str})

        #create dataframe with numeric values (column names=stats_name, row values=stats_value)
        stats_name = [metric[1] for metric in metrics if metric[1] is not None]
        stats_value = [metric[3] for metric in metrics if metric[1] is not None]
        self.statistics_numeric_format = pl.DataFrame({name: [value] for name, value in zip(stats_name, stats_value)})

        self.logger.info("Performance Statistics successfully generated.")
        
       
    def export_statistics_to_excel(self, output_dir: str):
        """Exports statistics to separate Excel files.
        
        Args:
            output_dir (str): Directory to save the Excel files.
        """
        # Ensure output directory exists
        os.makedirs(output_dir, exist_ok=True)
        
        # Export data series with percentage formatting for return columns
        self.data_series.write_excel(
                os.path.join(output_dir, "data_series.xlsx"),
                column_formats={
                    "daily_return": "0.00%",
                    "cumulative_return": "0.00%",
                    "drawdown": "0.00%"
                }
            )
        
        # Export PnL series to separate files with percentage formatting
        self.weekly_pnl_series.write_excel(
            os.path.join(output_dir, "weekly_pnl_series.xlsx"),
            column_formats={"weekly_pnl": "0.00%"}
        )
            
        self.monthly_pnl_series.write_excel(
            os.path.join(output_dir, "monthly_pnl_series.xlsx"),
            column_formats={"monthly_pnl": "0.00%"}
        )
            
        self.annual_pnl_series.write_excel(
            os.path.join(output_dir, "annual_pnl_series.xlsx"),
            column_formats={"annual_pnl": "0.00%"}
        )
        
        # Export trades
        self.trades.write_excel(os.path.join(output_dir, "trades.xlsx"))
        
        # Export child orders
        self.child_orders.write_excel(os.path.join(output_dir, "child_orders.xlsx"))
        
        # Export positions
        self.positions.write_excel(os.path.join(output_dir, "positions.xlsx"))
        
        # Export metrics
        self.statistics_string_format.write_excel(os.path.join(output_dir, "metrics.xlsx"))

        self.logger.info(f"Performance Statistics successfully exported to Excel files in directory: {output_dir}")

    
    def generate_visuals(self):
        """Generates visualizations for trading performance statistics."""
        
        #equity curve
        plot_scatter_chart(
            pl.DataFrame({"timestamp": self.data_series["timestamp_current"], "equity": self.data_series["equity_curve"]}),
            ["equity"], 
            ["blue"], 
            "Equity Curve", 
            "Date", 
            "Value", 
            "0,0", 
            1000, 
            600
        )

        #max drawdowns
        plot_scatter_chart(
            pl.DataFrame({"timestamp": self.data_series["timestamp_current"], "drawdown": self.data_series["drawdown"]}),
            ["drawdown"], 
            ["red"], 
            "Drawdown Curve", 
            "Date", 
            "Value", 
            "00.0%", 
            1000, 
            400
        )

        #derive percent return per symbol
        current_positions = (self.positions
                .sort("timestamp_current")
                .group_by(["strategy_id", "symbol_id"])
                .last())
        
        current_positions = current_positions.group_by("symbol_id").agg((pl.col("unrealized_pnl_base").add(pl.col("realized_pnl_base")).sum() / self.backtest_configuration.starting_balance).alias("pnl"))
        x_data = current_positions["symbol_id"].to_list()
        y_data = current_positions["pnl"].to_list()

        plot_bar_chart(pl.DataFrame({"symbol": x_data, "color": ["lightgreen"] * len(x_data), "pnl": y_data}),
                       "Percent Return by Symbol", 
                       "Percent Return", 
                       "0.00%",
                       1000, 
                       600)


        #Weekly Pnl Barchart
        x_data = self.weekly_pnl_series["timestamp_current"].dt.strftime("%W-%Y").cast(pl.Utf8).to_list()
        y_data = self.weekly_pnl_series["weekly_pnl"]
        
        plot_bar_chart(
            pl.DataFrame({"week": x_data, "color": ["lightgreen"] * len(x_data), "pnl": y_data}),
            "Weekly PnL", 
            "", 
            "0.00 %",
            1000, 
            600
        )

        #Monthly Pnl Barchart
        x_data = self.monthly_pnl_series["timestamp_current"].dt.strftime("%B-%Y").cast(pl.Utf8).to_list()
        y_data = self.monthly_pnl_series["monthly_pnl"]
        plot_bar_chart(
            pl.DataFrame({"month": x_data, "color": ["lightgreen"] * len(x_data), "pnl": y_data}),
            "Monthly PnL", 
            "", 
            "0.00 %",
            1000, 
            600
        )

        #Annual Pnl Barchart
        x_data = self.annual_pnl_series["timestamp_current"].dt.strftime("%Y").cast(pl.Utf8).to_list()
        y_data = self.annual_pnl_series["annual_pnl"]
        plot_bar_chart(
            pl.DataFrame({"year": x_data, "color": ["lightgreen"] * len(x_data), "pnl": y_data}),
            "Annual PnL", 
            "", 
            "0.00 %",
            1000, 
            600
        )

        self.logger.info("Visualizations successfully generated.")


    def _calculate_drawdown(self, data_series:pl.Series) -> tuple[list, int]:
            """Calculates the drawdown of the equity curve."""
            drawdowns = []
            peak_price = -math.inf
            drawdown_recovery_period = 0

            for pnl in data_series:
                if pnl > peak_price:
                    peak_price = pnl
                    drawdown_recovery_period = 0
                else:
                    drawdown_recovery_period += 1

                drawdown = min(0.0, (pnl - peak_price) / peak_price)
                drawdowns.append(drawdown)

            return (drawdowns, drawdown_recovery_period)


    def can_be_float(self, value):
        try:
            float(value)
            return True
        except:
            return False