"""Excel logging module for writing data to Excel files with custom formatting."""
import os
from datetime import datetime
from typing import List, Tuple, Dict, Any

class ExcelLoggerError(Exception):
    """Base exception class for ExcelLogger errors."""
    pass

class ConfigurationError(ExcelLoggerError):
    """Raised when there's an error in the configuration."""
    pass

class DataValidationError(ExcelLoggerError):
    """Raised when data validation fails."""
    pass

class ExcelLogger:
    """Excel logger for writing formatted data to Excel files."""

    def __init__(self):
        """Initialize the Excel logger with empty cache."""
        self._workbook_cache: Dict[str, Dict[str, Dict[str, Any]]] = {}
        self._workbook_names: Dict[str, str] = {}  # Maps workbook names to full paths
        self._remove_timezone: Dict[str, Dict[str, bool]] = {}  # Maps workbook/worksheet to timezone removal setting
        self._base_directory = None

    def configure_base_directory(self, base_directory: str) -> None:
        """Configure the base directory for all workbooks."""
        if not base_directory:
            raise ConfigurationError("Base directory cannot be empty")
        self._base_directory = base_directory

    def configure_worksheet(self, workbook_name: str, worksheet: str, columns: List[Tuple[str, str]], remove_timezone: bool = True) -> None:
        """Configure worksheet columns and their formats."""
        if not workbook_name:
            raise ConfigurationError("Workbook path cannot be empty")
        if not worksheet:
            raise ConfigurationError("Worksheet name cannot be empty")
        if not columns:
            raise ConfigurationError("Column configuration cannot be empty")

        self._workbook_names[workbook_name] = workbook_name

        if workbook_name not in self._remove_timezone:
            self._remove_timezone[workbook_name] = {}
        self._remove_timezone[workbook_name][worksheet] = remove_timezone

        for i, col in enumerate(columns):
            if not isinstance(col, tuple) or len(col) != 2:
                raise ConfigurationError(f"Column {i} configuration must be a tuple of (name, format)")
            if not isinstance(col[0], str) or not isinstance(col[1], str):
                raise ConfigurationError(f"Column {i} name and format must be strings")
            if not col[0]:
                raise ConfigurationError(f"Column {i} name cannot be empty")

        column_names = [col[0] for col in columns]
        if len(column_names) != len(set(column_names)):
            raise ConfigurationError("Duplicate column names are not allowed")

        if workbook_name not in self._workbook_cache:
            self._workbook_cache[workbook_name] = {}

        if worksheet in self._workbook_cache[workbook_name] and self._workbook_cache[workbook_name][worksheet]["data"]:
            raise ConfigurationError(f"Worksheet '{worksheet}' already contains data and cannot be reconfigured")

        self._workbook_cache[workbook_name][worksheet] = {
            "columns": columns,
            "data": []
        }

    def log(self, workbook_name: str, worksheet: str, data: List[Any]) -> None:
        """Log a single row of data."""
        if workbook_name not in self._workbook_cache:
            raise ConfigurationError(f"Workbook '{workbook_name}' not configured")

        if worksheet not in self._workbook_cache[workbook_name]:
            raise ConfigurationError(f"Worksheet '{worksheet}' not configured in workbook '{workbook_name}'")

        worksheet_config = self._workbook_cache[workbook_name][worksheet]
        if len(data) != len(worksheet_config["columns"]):
            raise DataValidationError(
                f"Data length {len(data)} does not match configured columns {len(worksheet_config['columns'])}. "
                f"Expected columns: {[col[0] for col in worksheet_config['columns']]}"
            )

        worksheet_config["data"].append(data)

    def finalize_workbook(self, workbook_name: str) -> None:
        """Write workbook to file and clear its cache."""
        if workbook_name not in self._workbook_names:
            return  # Skip if workbook was never configured

        if not self._base_directory:
            raise ConfigurationError("Base directory not configured")

        workbook_path = os.path.join(self._base_directory, workbook_name + ".xlsx")
        
        try:
            import xlsxwriter
        except ImportError:
            raise ImportError("xlsxwriter is required for Excel output. Install with 'pip install xlsxwriter'")

        try:
            os.makedirs(os.path.dirname(workbook_path) or '.', exist_ok=True)
            workbook = xlsxwriter.Workbook(workbook_path)

            if workbook_name in self._workbook_cache:
                for worksheet_name, config in self._workbook_cache[workbook_name].items():
                    if not config["data"]:
                        continue

                    worksheet = workbook.add_worksheet(worksheet_name)
                    remove_tz = self._remove_timezone.get(workbook_name, {}).get(worksheet_name, False)

                    column_names = [col[0] for col in config["columns"]]
                    column_formats = {col[0]: col[1] for col in config["columns"]}

                    excel_formats = {}
                    for col_name, format_str in column_formats.items():
                        excel_formats[col_name] = workbook.add_format({'num_format': format_str})

                    for col_idx, col_name in enumerate(column_names):
                        worksheet.write(0, col_idx, col_name)

                    for row_idx, row_data in enumerate(config["data"]):
                        for col_idx, (col_name, value) in enumerate(zip(column_names, row_data)):
                            if remove_tz and isinstance(value, datetime):
                                value = value.replace(tzinfo=None)
                            
                            if col_name in excel_formats:
                                worksheet.write(row_idx + 1, col_idx, value, excel_formats[col_name])
                            else:
                                worksheet.write(row_idx + 1, col_idx, value)

            workbook.close()

            if workbook_name in self._workbook_cache:
                del self._workbook_cache[workbook_name]
            if workbook_name in self._workbook_names:
                del self._workbook_names[workbook_name]
            if workbook_name in self._remove_timezone:
                del self._remove_timezone[workbook_name]

        except PermissionError:
            raise OSError(
                f"Permission denied when writing to '{workbook_path}'. "
                "Ensure the file is not open in another application."
            )
        except OSError as e:
            raise OSError(f"Failed to create directory or write file '{workbook_path}': {str(e)}")
        except Exception as e:
            raise OSError(f"Failed to write workbook '{workbook_path}': {str(e)}")

    def clear_all(self) -> None:
        """Clear all cached data and configurations."""
        self._workbook_cache.clear()
        self._workbook_names.clear()
        self._remove_timezone.clear()

# Global instance
excel_logger = ExcelLogger()
