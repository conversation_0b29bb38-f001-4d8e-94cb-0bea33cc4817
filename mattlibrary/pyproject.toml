[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[project]
name = "mattlibrary"        # Your package name
version = "0.1.0"
description = "My personal Python library"
authors = [
    { name = "<PERSON>", email = "<EMAIL>" }
]
readme = "README.md"
requires-python = ">=3.8"

# Optional dependencies your package needs
dependencies = [
    # "pandas>=1.5",
    # "requests>=2.28"
]