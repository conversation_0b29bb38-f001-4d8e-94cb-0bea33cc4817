Executive Summary


  matt_library is a comprehensive, modular framework for quantitative financial analysis, with a primary focus on backtesting trading strategies. The architecture is designed
   around a central TradingEngine that orchestrates interactions between data, strategies, and order execution. It employs several key design patterns, including the
  Strategy, Observer (via callbacks), and a plugin-style architecture for execution engines. Data management is robust, supporting multiple database backends and a custom
  Polars-based partitioned storage system. While the architecture is powerful and well-structured, it has some potential issues, notably the use of exec for loading
  configurations, which introduces tight coupling and security risks.

  ---

  1. Main Modules and Their Responsibilities


  The project is logically divided into several modules, each with a distinct responsibility:


   * `trading`: This is the core module of the library.
       * `trading_engine.py`: The central orchestrator. It manages the lifecycle of strategies, processes the market data stream, and routes orders and fills between
         strategies and the execution engine.
       * `strategy_base.py`: An abstract base class (ABC) defining the interface for all trading strategies. This allows for a plug-and-play approach to developing new
         strategies.
       * `parent_order_base.py` / `child_order.py`: These define a two-tiered order hierarchy. Parent orders represent the strategic intent (e.g., a bracket order), while
         child orders are the concrete, executable orders (e.g., a limit or market order).
       * `position.py` / `trade.py`: Data classes that represent the state of a trading position and the details of a completed trade, respectively.
       * `execution_engines/`: A plugin-style module for order execution. The provided simulated_execution_engine.py handles order fills in a backtesting environment. This
         could be extended with plugins for live trading brokers.
       * `symbol_database.py` / `fx_converter.py`: Utilities for managing instrument metadata and handling currency conversions.


   * `backtesting`: This module is responsible for running strategies against historical data.
       * `backtest_engine.py`: Manages the entire backtesting process. It initializes the TradingEngine, loads historical data and strategies (via external script files),
         drives the market data simulation, and processes the final results.
       * `backtest_configuration.py`: A data class that holds all the parameters for a backtest run, making runs configurable and reproducible.


   * `datamanagement`: This module handles data persistence and retrieval. It provides a clear separation between data sources and the rest of an application.
       * `polars_db.py`: Implements a sophisticated, Hive-partitioned storage system using Polars and Parquet files. This is efficient for storing and querying large
         time-series datasets.
       * `clickhouse.py`, `dukascopy.py`, `polygon.py`: Clients for interacting with specific data sources, including a ClickHouse database and APIs from Dukascopy and
         Polygon.io.

   * `visualizations`: A utility module for creating plots and charts using Bokeh and Plotly. It's used for visualizing performance metrics and data analysis.


   * `optimization`: Provides tools for running parameter optimization sweeps, likely for finding the best parameters for a given trading strategy.

   * `logging` / `helpers`: These are cross-cutting concerns, providing standardized logging (including to Excel) and various utility functions used throughout the library.

  ---


  2. Data Flow and Dependencies

  The data flow is centered around the backtesting process:


   1. Initialization: The BacktestEngine is created with a BacktestConfiguration object and paths to data/strategy generator scripts.
   2. Data & Strategy Loading: The BacktestEngine executes the generator scripts using exec().
       * The data generator script produces a Polars DataFrame of historical market data.
       * The strategy generator script produces one or more instances of StrategyBase subclasses.
   3. Engine Setup: The BacktestEngine initializes the TradingEngine and adds the generated strategies to it.
   4. Market Data Simulation: The BacktestEngine iterates through the historical data DataFrame row by row. Each row (representing a market data tick/bar) is passed to the
      TradingEngine's process_market_data method.
   5. Strategy Processing: The TradingEngine passes the market data to every relevant strategy.
   6. Order Generation: A strategy, based on its internal logic, may decide to issue an order. It does so by creating a ParentOrder (e.g., ParentOrderBracket) and submitting it
      to the TradingEngine via a callback.
   7. Order Execution Flow:
       * The TradingEngine receives the ParentOrder and calls its process_market_data method on subsequent ticks.
       * The ParentOrder's logic determines when to create one or more ChildOrders.
       * The ChildOrder is sent to the TradingEngine, which forwards it to the configured ExecutionPlugin (e.g., SimulatedExecutionEngine).
       * The ExecutionPlugin simulates the fill based on the current market data. Upon a fill or cancellation, it notifies the TradingEngine via a callback.
       * The TradingEngine updates the relevant Position object and notifies the ParentOrder of the fill.
       * The ParentOrder updates its state. Once the parent order is complete, the TradingEngine notifies the original Strategy.
   8. Performance Tracking: Throughout the process, the TradingEngine records all trades, orders, and snapshots of position state.
   9. Finalization: After the data stream ends, the BacktestEngine uses PerformanceMetrics to analyze the recorded data and generate statistics and visualizations.


  Dependencies:
   * backtesting depends heavily on trading and datamanagement.
   * trading is the core and has few dependencies on other major modules, but uses helpers and logging.
   * Strategies (like SmaCrossoverStrategy) depend on the trading module's base classes.
   * datamanagement and visualizations are largely standalone utility modules.

  ---

  3. Use of Design Patterns

  The library effectively uses several design patterns:


   * Strategy Pattern: StrategyBase defines an interface that allows different algorithms to be swapped in and out of the TradingEngine.
   * Observer Pattern (Callbacks): The architecture is heavily reliant on callbacks for decoupling. The TradingEngine observes strategies for parent orders, parent orders for
     child orders, and the execution engine for fills. This avoids tight coupling between components.
   * Plugin Architecture: The ExecutionPluginBase defines a contract for execution engines, allowing the system to be extended from simulation to live trading without changing
     the core TradingEngine.
   * Facade Pattern: The PolarsDataFrameManager and ClickHouseClient provide simple, high-level APIs that hide the complexity of the underlying data storage and retrieval
     mechanisms.
   * Data Transfer Object (DTO): Classes like BacktestConfiguration, Position, Trade, and ChildOrder serve primarily to bundle and transfer data between different layers of
     the application.