"""<PERSON><PERSON><PERSON> to run the FastAPI trading backend server. This is the trading backend, which provides the trading engine and market data."""
import uvicorn
from app.configuration.settings import settings

if __name__ == "__main__":
    uvicorn.run(
        "app.main:app", #app.main:app is the path to the FastAPI application
        host=settings.backend_address,
        port=settings.backend_port,
        reload=True,
        reload_dirs=["trading_backend"],
        reload_excludes=["*.log", "*.parquet", "*.json", "logs"],
        log_level="info"
    )