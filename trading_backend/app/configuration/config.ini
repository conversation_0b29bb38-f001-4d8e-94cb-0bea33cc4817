[BACKEND]
BACKEND_ID = backend
BACKEND_ADDRESS = localhost
BACKEND_PORT = 8000

[LOGGING]
LOGGING_ENABLED = True
LOG_LEVEL = INFO
CONSOLE_LOGGING_ENABLED = True
FILE_LOGGING_ENABLED = True
LOG_FILE = trading_backend/app/logs/trading_backend.log
CLEAN_LOG_FILE = True

[TRADINGENGINE]
STARTING_BALANCE = 100000
BASE_CURRENCY = USD
TRACK_PERFORMANCE = True
EXECUTION_PLUGIN_TYPE = SimulatedExecutionEngine
STATIC_DATA_DIRECTORY = trading_backend/app/data/
READ_POSITIONS_FROM_DISK = True
WRITE_POSITIONS_TO_DISK = True

[INTERACTIVEBROKERS]
ADDRESS = 127.0.0.1
PORT = 7497