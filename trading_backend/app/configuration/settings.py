"""Settings module for trading backend. This is the trading backend, which provides the trading engine and market data."""
import configparser
import os
from pathlib import Path

class Settings:
    
    def __init__(self):
        self.config = configparser.ConfigParser()
        config_path = Path(__file__).parent / "config.ini"
        self.config.read(config_path)
    
    @property
    def backend_id(self) -> str:
        return self.config.get('BACKEND', 'BACKEND_ID')

    @property
    def backend_address(self) -> str:
        return self.config.get('BACKEND', 'BACKEND_ADDRESS')
    
    @property
    def backend_port(self) -> int:
        return self.config.getint('BACKEND', 'BACKEND_PORT')


    @property
    def logging_enabled(self) -> bool:
        return self.config.getboolean('LOGGING', 'LOGGING_ENABLED')
    
    @property
    def log_level(self) -> str:
        return self.config.get('LOGGING', 'LOG_LEVEL')
    
    @property
    def console_logging_enabled(self) -> bool:
        return self.config.getboolean('LOGGING', 'CONSOLE_LOGGING_ENABLED')
    
    @property
    def file_logging_enabled(self) -> bool:
        return self.config.getboolean('LOGGING', 'FILE_LOGGING_ENABLED')
    
    @property
    def log_file(self) -> str:
        return self.config.get('LOGGING', 'LOG_FILE')
    
    @property
    def clean_log_file(self) -> bool:
        return self.config.getboolean('LOGGING', 'CLEAN_LOG_FILE')
    
    @property
    def starting_balance(self) -> int:
        return self.config.getint('TRADINGENGINE', 'STARTING_BALANCE')
    
    @property
    def base_currency(self) -> str:
        return self.config.get('TRADINGENGINE', 'BASE_CURRENCY')
    
    @property
    def track_performance(self) -> bool:
        return self.config.getboolean('TRADINGENGINE', 'TRACK_PERFORMANCE')
    
    @property
    def execution_plugin_type(self) -> str:
        return self.config.get('TRADINGENGINE', 'EXECUTION_PLUGIN_TYPE')
    
    @property
    def static_data_directory(self) -> str:
        return self.config.get('TRADINGENGINE', 'STATIC_DATA_DIRECTORY')
    
    @property
    def read_positions_from_disk(self) -> bool:
        return self.config.getboolean('TRADINGENGINE', 'READ_POSITIONS_FROM_DISK')
    
    @property
    def write_positions_to_disk(self) -> bool:
        return self.config.getboolean('TRADINGENGINE', 'WRITE_POSITIONS_TO_DISK')
    
    @property
    def ib_address(self) -> str:
        return self.config.get('INTERACTIVEBROKERS', 'ADDRESS')
    
    @property
    def ib_port(self) -> int:
        return self.config.getint('INTERACTIVEBROKERS', 'PORT')


settings = Settings()