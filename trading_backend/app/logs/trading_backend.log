INFO - 2025-08-21 00:17:16,222 - app.manager - __init__:29 - Backend manager initialized successfully
INFO - 2025-08-21 00:17:16,235 - app.communicator - __init__:78 - Backend communicator initialized successfully
INFO - 2025-08-21 00:17:16,237 - app.communicator - lifespan:113 - Starting trading backend...listening to clients...
INFO - 2025-08-21 00:17:57,986 - app.client_instance - __init__:31 - Initializing client instance: client100
INFO - 2025-08-21 00:17:57,987 - mattlibrary.trading.execution_data_engines.simulated_execution_engine - __init__:27 - SimulatedExecutionEngine initialized
INFO - 2025-08-21 00:17:58,067 - mattlibrary.trading.fx_converter - load_fx_data:69 - Loaded fx conversion data (4553 rows) from trading_backend/app/data/fx_converter_data.parquet
INFO - 2025-08-21 00:17:58,073 - mattlibrary.trading.symbol_database - _load_symbols_from_parquet:53 - Loaded 28 symbols from trading_backend/app/data/symbol_database.parquet
INFO - 2025-08-21 00:17:58,073 - mattlibrary.trading.trading_engine - load_positions_from_disk:171 - 1 Positions loaded from disk: trading_backend/app/data/positions_client100.json
INFO - 2025-08-21 00:17:58,074 - mattlibrary.trading.trading_engine - __init__:100 - Initialized Trading Engine [base_currency=USD, starting_balance=100000 USD, track_performance=True, execution_plugin_type=SimulatedExecutionEngine, static_data_directory=trading_backend/app/data/]
INFO - 2025-08-21 00:17:58,074 - mattlibrary.datamanagement.interactive_brokers - __init__:19 - Initializing InteractiveBrokersAPI
INFO - 2025-08-21 00:17:58,075 - app.client_instance - __init__:55 - Client instance initialized successfully: client100 with IB client ID 963
INFO - 2025-08-21 00:17:58,075 - app.manager - create_client_instance:43 - Successfully created new client instance: client100
INFO - 2025-08-21 00:17:58,080 - app.client_instance - get_positions:76 - Retrieved 1 positions for client client100
INFO - 2025-08-21 00:17:58,080 - app.communicator - get_client_positions:331 - Positions requested for client client100. Total positions: 1
INFO - 2025-08-21 00:17:58,487 - app.communicator - open_websocket:432 - WebSocket connected for client: client100
ERROR - 2025-08-21 00:18:51,060 - app.communicator - save_symbols:299 - Error retrieving symbols for client client100: cannot access local variable 'symbol_dictionary' where it is not associated with a value
INFO - 2025-08-21 00:19:03,007 - app.communicator - open_websocket:473 - WebSocket client disconnected: client100
INFO - 2025-08-21 00:19:03,108 - app.communicator - lifespan:116 - Shutting down trading backend, cleaning up all clients...
INFO - 2025-08-21 00:19:03,108 - app.manager - cleanup_all_clients:74 - Cleaning up all client instances...
INFO - 2025-08-21 00:19:03,108 - app.client_instance - cleanup:170 - Cleaning up client instance: client100
INFO - 2025-08-21 00:19:03,108 - mattlibrary.datamanagement.interactive_brokers - disconnect:51 - Disconnected from Interactive Brokers
INFO - 2025-08-21 00:19:03,108 - mattlibrary.trading.trading_engine - finalize:332 - Trading Engine finalized [strategies=0]
INFO - 2025-08-21 00:19:03,109 - app.client_instance - cleanup:180 - Client instance cleanup completed: client100
INFO - 2025-08-21 00:19:03,109 - app.manager - remove_client_instance:55 - Gracefully closed existing client and removed client instance: client100
INFO - 2025-08-21 00:19:03,109 - app.manager - cleanup_all_clients:80 - All client instances cleaned up
INFO - 2025-08-21 00:19:03,109 - app.communicator - lifespan:118 - Trading backend shutdown complete
