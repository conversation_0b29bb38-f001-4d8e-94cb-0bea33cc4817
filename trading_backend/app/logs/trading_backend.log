INFO - 2025-08-21 00:38:25,534 - app.manager - __init__:29 - Backend manager initialized successfully
INFO - 2025-08-21 00:38:25,548 - app.communicator - __init__:79 - Backend communicator initialized successfully
INFO - 2025-08-21 00:38:25,550 - app.communicator - lifespan:114 - Starting trading backend...listening to clients...
INFO - 2025-08-21 00:44:43,528 - app.client_instance - __init__:31 - Initializing client instance: client100
INFO - 2025-08-21 00:44:43,530 - mattlibrary.trading.execution_data_engines.simulated_execution_engine - __init__:27 - SimulatedExecutionEngine initialized
INFO - 2025-08-21 00:44:43,617 - mattlibrary.trading.fx_converter - load_fx_data:69 - Loaded fx conversion data (4553 rows) from trading_backend/app/data/fx_converter_data.parquet
INFO - 2025-08-21 00:44:43,628 - mattlibrary.trading.symbol_database - _load_symbols_from_parquet:53 - Loaded 28 symbols from trading_backend/app/data/symbol_database.parquet
INFO - 2025-08-21 00:44:43,629 - mattlibrary.trading.trading_engine - load_positions_from_disk:171 - 1 Positions loaded from disk: trading_backend/app/data/positions_client100.json
INFO - 2025-08-21 00:44:43,630 - mattlibrary.trading.trading_engine - __init__:100 - Initialized Trading Engine [base_currency=USD, starting_balance=100000 USD, track_performance=True, execution_plugin_type=SimulatedExecutionEngine, static_data_directory=trading_backend/app/data/]
INFO - 2025-08-21 00:44:43,630 - mattlibrary.datamanagement.interactive_brokers - __init__:19 - Initializing InteractiveBrokersAPI
INFO - 2025-08-21 00:44:43,631 - app.client_instance - __init__:55 - Client instance initialized successfully: client100 with IB client ID 963
INFO - 2025-08-21 00:44:43,631 - app.manager - create_client_instance:43 - Successfully created new client instance: client100
INFO - 2025-08-21 00:44:43,635 - app.client_instance - get_positions:76 - Retrieved 1 positions for client client100
INFO - 2025-08-21 00:44:43,636 - app.communicator - get_client_positions:350 - Positions requested for client client100. Total positions: 1
INFO - 2025-08-21 00:44:44,033 - app.communicator - open_websocket:451 - WebSocket connected for client: client100
INFO - 2025-08-21 00:54:49,298 - app.communicator - open_websocket:492 - WebSocket client disconnected: client100
INFO - 2025-08-21 00:54:49,398 - app.communicator - lifespan:117 - Shutting down trading backend, cleaning up all clients...
INFO - 2025-08-21 00:54:49,398 - app.manager - cleanup_all_clients:74 - Cleaning up all client instances...
INFO - 2025-08-21 00:54:49,398 - app.client_instance - cleanup:170 - Cleaning up client instance: client100
INFO - 2025-08-21 00:54:49,398 - mattlibrary.datamanagement.interactive_brokers - disconnect:51 - Disconnected from Interactive Brokers
INFO - 2025-08-21 00:54:49,399 - mattlibrary.trading.trading_engine - finalize:332 - Trading Engine finalized [strategies=0]
INFO - 2025-08-21 00:54:49,399 - app.client_instance - cleanup:180 - Client instance cleanup completed: client100
INFO - 2025-08-21 00:54:49,399 - app.manager - remove_client_instance:55 - Gracefully closed existing client and removed client instance: client100
INFO - 2025-08-21 00:54:49,399 - app.manager - cleanup_all_clients:80 - All client instances cleaned up
INFO - 2025-08-21 00:54:49,399 - app.communicator - lifespan:119 - Trading backend shutdown complete
