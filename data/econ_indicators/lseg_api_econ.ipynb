{"cells": [{"cell_type": "code", "execution_count": null, "id": "cf0a9370", "metadata": {}, "outputs": [], "source": ["import os\n", "import polars as pl\n", "import logging.config\n", "from typing import List\n", "import lseg.data as ld"]}, {"cell_type": "code", "execution_count": null, "id": "a86439b5", "metadata": {}, "outputs": [], "source": ["session = ld.open_session()\n", "session.set_log_level(logging.DEBUG)"]}, {"cell_type": "code", "execution_count": null, "id": "9fc15f6c", "metadata": {}, "outputs": [], "source": ["def get_history(ticker: str, data_fields: List[str], start_date: str, end_date: str) -> pl.DataFrame:\n", "    data = ld.get_history(\n", "        universe=[ticker],\n", "        fields=data_fields,\n", "        start=start_date,\n", "        end=end_date\n", "    )\n", "    \n", "    polars_df = pl.from_pandas(data, include_index=True)\n", "    return polars_df"]}, {"cell_type": "code", "execution_count": null, "id": "f2ddbd25", "metadata": {}, "outputs": [], "source": ["usd_symbols = [\n", "    (\"USFOMC=ECIX\", \"VALUE\", \"usd_fed_rate\"),\n", "    (\"USGDEF=ECIX\", \"VALUE\", \"usd_fed_budget\"),\n", "    (\"USCPF=ECIX\", \"VALUE\", \"usd_core_cpi_mm\"),\n", "    (\"USCPFY=ECIX\", \"VALUE\", \"usd_core_cpi_yy\"),\n", "    (\"USCPI=ECIX\", \"VALUE\", \"usd_cpi_mm\"),\n", "    (\"USCPNY=ECIX\", \"VALUE\", \"usd_cpi_yy\"),\n", "    (\"USCURA=ECIX\", \"VALUE\", \"usd_current_account\"),\n", "    (\"USGDPP=ECIX\", \"VALUE\", \"usd_gdp_preliminary\"),\n", "    (\"USGDPF=ECIX\", \"VALUE\", \"usd_gdp_final\"),\n", "    (\"USIP=ECIX\", \"VALUE\", \"usd_industrial_production\"),\n", "    (\"USISME=ECIX\", \"VALUE\", \"usd_ism_manufacturing_employment\"),\n", "    (\"USISMN=ECIX\", \"VALUE\", \"usd_ism_manufacturing_new_orders\"),\n", "    (\"USPMI=ECIX\", \"VALUE\", \"usd_ism_manufacturing_prices\"),\n", "    (\"USISMP=ECIX\", \"VALUE\", \"usd_ism_manufacturing_prices_paid\"),\n", "    (\"USOPMI=ECIX\", \"VALUE\", \"usd_ism_non_manufacturing\"),\n", "    (\"USDPMI=ECIX\", \"VALUE\", \"usd_ism_non_manufacturing_nmi_pmi\"),\n", "    (\"USNPMI=ECIX\", \"VALUE\", \"usd_ism_non_manufacturing_new_orders\"),\n", "    (\"USPPMI=ECIX\", \"VALUE\", \"usd_ism_services_prices\"),\n", "    (\"USPPFD=ECIX\", \"VALUE\", \"usd_producer_prices_final_demand\"),\n", "    (\"USPPFY=ECIX\", \"VALUE\", \"usd_producer_prices_final_demand_yy\"),\n", "    (\"USRSLA=ECIX\", \"VALUE\", \"usd_retail_sales_total\"),\n", "    (\"USRSL=ECIX\", \"VALUE\", \"usd_retail_sales_mm\"),\n", "    (\"USTBAL=ECIX\", \"VALUE\", \"usd_trade_balance\"),\n", "    (\"USUNR=ECIX\", \"VALUE\", \"usd_unemployment_rate\"),\n", "    (\"USNFAR=ECIX\", \"VALUE\", \"usd_nonfarm_payrolls\"),\n", "    (\"USMFP=ECIX\", \"VALUE\", \"usd_manufacturing_payrolls\"),\n", "    (\"USPRP=ECIX\", \"VALUE\", \"usd_private_payrolls\"),\n", "    (\"USAVGE=ECIX\", \"VALUE\", \"usd_average_earnings\"),\n", "    (\"USWRKW=ECIX\", \"VALUE\", \"usd_average_work_week\")]\n", "\n", "jpy_symbols = [\n", "    (\"JPINTN=ECIX\", \"VALUE\", \"jpy_boj_cash_rate\"),\n", "    (\"JPCGP=ECIX\", \"VALUE\", \"jpy_corp_goods_price\"), \n", "    (\"JPCGPY=ECIX\", \"VALUE\", \"jpy_corp_goods_price_yy\"),\n", "    (\"JPCPI=ECIX\", \"VALUE\", \"jpy_cpi_core\"),\n", "    (\"JPCPIT=ECIX\", \"VALUE\", \"jpy_cpi_core_tokyo\"),\n", "    (\"JPCPO=ECIX\", \"VALUE\", \"jpy_cpi_overall\"),\n", "    (\"JPCPTO=ECIX\", \"VALUE\", \"jpy_cpi_overall_tokyo\"),\n", "    (\"JPCURT=ECIX\", \"VALUE\", \"jpy_current_account\"),\n", "    (\"JPEXPY=ECIX\", \"VALUE\", \"jpy_exports_yy\"),\n", "    (\"JPRES=ECIX\", \"VALUE\", \"jpy_foreign_reserves\"),\n", "    (\"JPGD1=ECIX\", \"VALUE\", \"jpy_gdp_qq\"),\n", "    (\"JPGDA=ECIX\", \"VALUE\", \"jpy_gdp_qq_annualized\"),\n", "    (\"JPGDR1=ECIX\", \"VALUE\", \"jpy_gdp_revised_qq\"),\n", "    (\"JPGDAR=ECIX\", \"VALUE\", \"jpy_gdp_revised_qq_annualized\"),\n", "    (\"JPIMPY=ECIX\", \"VALUE\", \"jpy_imports_yy\"),\n", "    (\"JPIP1=ECIX\", \"VALUE\", \"jpy_industrial_production\"),\n", "    (\"JPIP4=ECIX\", \"VALUE\", \"jpy_industrial_production_revised\"),\n", "    (\"JPRSLS=ECIX\", \"VALUE\", \"jpy_retail_sales\"),\n", "    (\"JPBCLG=ECIX\", \"VALUE\", \"jpy_tankan_big_manufacturing\"),\n", "    (\"JPBCLN=ECIX\", \"VALUE\", \"jpy_tankan_big_non_manufacturing\"),\n", "    (\"JPTBL2=ECIX\", \"VALUE\", \"jpy_trade_balance\"),\n", "    (\"JPUNR=ECIX\", \"VALUE\", \"jpy_unemployment_rate\")]\n", "\n", "eur_symbols = [\n", "    (\"EUECBR=ECIX\", \"VALUE\", \"eur_ecb_refinancing_rate\"),\n", "    (\"EUCPIX=ECIX\", \"VALUE\", \"eur_core_cpi\"),\n", "    (\"EUCPXY=ECIX\", \"VALUE\", \"eur_core_cpi_yy\"),\n", "    (\"EUCONS=ECIX\", \"VALUE\", \"eur_consumer_confidence\"),\n", "    (\"EUCURA=ECIX\", \"VALUE\", \"eur_current_account\"),\n", "    (\"EUCURU=ECIX\", \"VALUE\", \"eur_current_account_revised\"),\n", "    (\"EUECOS=ECIX\", \"VALUE\", \"eur_economic_sentiment\"),\n", "    (\"EUGDF=ECIX\", \"VALUE\", \"eur_gdp_flash_estimate\"),\n", "    (\"EUGDFY=ECIX\", \"VALUE\", \"eur_gdp_flash_estimate_yy\"),\n", "    (\"EUGDP=ECIX\", \"VALUE\", \"eur_gdp_revised\"),\n", "    (\"EUGDPY=ECIX\", \"VALUE\", \"eur_gdp_revised_yy\"),\n", "    (\"EUIP=ECIX\", \"VALUE\", \"eur_industrial_production\"),\n", "    (\"EUIPY=ECIX\", \"VALUE\", \"eur_industrial_production_yy\"),\n", "    (\"EUPPI=ECIX\", \"VALUE\", \"eur_producer_prices\"),\n", "    (\"EUPPIY=ECIX\", \"VALUE\", \"eur_producer_prices_yy\"),\n", "    (\"EURSL=ECIX\", \"VALUE\", \"eur_retail_sales\"),\n", "    (\"EURSLY=ECIX\", \"VALUE\", \"eur_retail_sales_yy\"),\n", "    (\"EUTBAL=ECIX\", \"VALUE\", \"eur_trade_balance\"),\n", "    (\"EUUNR=ECIX\", \"VALUE\", \"eur_unemployment_rate\"),\n", "    (\"EUEMPQ=ECIX\", \"VALUE\", \"eur_employment_rate_qq\"),\n", "    (\"EUEMPY=ECIX\", \"VALUE\", \"eur_employment_rate_yy\")]\n", "\n", "gbp_symbols = [\n", "    (\"GBBOEI=ECIX\", \"VALUE\", \"gbp_boe_overnight_rate\"),\n", "    (\"GBHICM=ECIX\", \"VALUE\", \"gbp_cpi_mm\"),\n", "    (\"GBHICY=ECIX\", \"VALUE\", \"gbp_cpi_yy\"),\n", "    (\"GBCURA=ECIX\", \"VALUE\", \"gbp_current_account\"),\n", "    (\"GBGDP=ECIX\", \"VALUE\", \"gbp_gdp_preliminary_qq\"),\n", "    (\"GBGDPY=ECIX\", \"VALUE\", \"gbp_gdp_preliminary_yy\"),\n", "    (\"GBGDF=ECIX\", \"VALUE\", \"gbp_gdp_final_qq\"),\n", "    (\"GBGDFY=ECIX\", \"VALUE\", \"gbp_gdp_final_yy\"),\n", "    (\"GBIP=ECIX\", \"VALUE\", \"gbp_industrial_production\"),\n", "    (\"GBIPY=ECIX\", \"VALUE\", \"gbp_industrial_production_yy\"),\n", "    (\"GBMFG=ECIX\", \"VALUE\", \"gbp_manufacturing_production\"),\n", "    (\"GBMFGY=ECIX\", \"VALUE\", \"gbp_manufacturing_production_yy\"),\n", "    (\"GBPSNB=ECIX\", \"VALUE\", \"gbp_public_sector_net_borrowing\"),\n", "    (\"GBPSCR=ECIX\", \"VALUE\", \"gbp_public_sector_net_cash_requirement\"),\n", "    (\"GBPCOM=ECIX\", \"VALUE\", \"gbp_ppi_core_output\"),\n", "    (\"GBPCOY=ECIX\", \"VALUE\", \"gbp_ppi_core_output_yy\"),\n", "    (\"GBPPI=ECIX\", \"VALUE\", \"gbp_ppi_output\"),\n", "    (\"GBPPIY=ECIX\", \"VALUE\", \"gbp_ppi_output_yy\"),\n", "    (\"GBRSX=ECIX\", \"VALUE\", \"gbp_retail_sales_1\"),\n", "    (\"GBRSXY=ECIX\", \"VALUE\", \"gbp_retail_sales_1_yy\"),\n", "    (\"GBRSL=ECIX\", \"VALUE\", \"gbp_retail_sales_2\"),\n", "    (\"GBRSLY=ECIX\", \"VALUE\", \"gbp_retail_sales_2_yy\"),\n", "    (\"GBTBAL=ECIX\", \"VALUE\", \"gbp_trade_balance\"),\n", "    (\"GBNEUT=ECIX\", \"VALUE\", \"gbp_trade_balance_non_eu\"),\n", "    (\"GBILOU=ECIX\", \"VALUE\", \"gbp_unemployment_rate\"),\n", "    (\"GBCCU=ECIX\", \"VALUE\", \"gbp_claimant_count\")]\n", "\n", "aud_symbols = [\n", "    (\"AUCBIR=ECIX\", \"VALUE\", \"aud_cash_rate\"), \n", "    (\"AUCPIQ=ECIX\", \"VALUE\", \"aud_cpi_qq\"),\n", "    (\"AUCPIY=ECIX\", \"VALUE\", \"aud_cpi_yy\"),\n", "    (\"AUCPI=ECIX\", \"VALUE\", \"aud_cpi_index_number\"),\n", "    (\"AUCADQ=ECIX\", \"VALUE\", \"aud_current_account\"),\n", "    (\"AUEXPQ=ECIX\", \"VALUE\", \"aud_export_prices\"),\n", "    (\"AUGDPQ=ECIX\", \"VALUE\", \"aud_gdp_qq\"),\n", "    (\"AUGDPY=ECIX\", \"VALUE\", \"aud_gdp_yy\"),\n", "    (\"AUIMPQ=ECIX\", \"VALUE\", \"aud_import_prices\"),\n", "    (\"AUPPIQ=ECIX\", \"VALUE\", \"aud_producer_prices\"),\n", "    (\"AUPPIY=ECIX\", \"VALUE\", \"aud_producer_prices_yy\"),\n", "    (\"AURSL=ECIX\", \"VALUE\", \"aud_retail_sales\"),\n", "    (\"AURSLQ=ECIX\", \"VALUE\", \"aud_retail_trade\"),\n", "    (\"AUJOBL=ECIX\", \"VALUE\", \"aud_unemployment_rate\")\n", "]\n", "\n", "nzd_symbols = [\n", "    (\"NZINTR=ECIX\", \"VALUE\", \"nzd_cash_rate\"), \n", "    (\"NZRAST=ECIX\", \"VALUE\", \"nzd_reserve_assets_total\"), \n", "    (\"NZMLKA=ECIX\", \"VALUE\", \"nzd_milk_auctions\"),\n", "    (\"NZWGSY=ECIX\", \"VALUE\", \"nzd_labor_cost_index_yy\"),\n", "    (\"NZWGSQ=ECIX\", \"VALUE\", \"nzd_labor_cost_index_qq\"),\n", "    (\"NZEMPQ=ECIX\", \"VALUE\", \"nzd_hlfs_unemployment_rate\"),\n", "    (\"NZCPIN=ECIX\", \"VALUE\", \"nzd_cpi_index_number\"),\n", "    (\"NZCPIQ=ECIX\", \"VALUE\", \"nzd_cpi_qq\"),\n", "    (\"NZGDPX=ECIX\", \"VALUE\", \"nzd_gdp_ependiture_qq\"),\n", "    (\"NZGDPQ=ECIX\", \"VALUE\", \"nzd_gdp_production_qq\")]\n", "\n", "cad_symbols = [\n", "    (\"CABOCR=ECIX\", \"VALUE\", \"cad_boc_rate\"),\n", "    (\"CABUD=ECIX\", \"VALUE\", \"cad_budget\"),\n", "    (\"CABUDY=ECIX\", \"VALUE\", \"cad_budget_ytd\"),\n", "    (\"CACPMM=ECIX\", \"VALUE\", \"cad_cpi_core_mm\"),\n", "    (\"CACPXX=ECIX\", \"VALUE\", \"cad_cpi_core_yy\"),\n", "    (\"CACPI=ECIX\", \"VALUE\", \"cad_cpi_mm\"),\n", "    (\"CACPIY=ECIX\", \"VALUE\", \"cad_cpi_yy\"),\n", "    (\"CACURA=ECIX\", \"VALUE\", \"cad_current_account\"),\n", "    (\"CAGDPM=ECIX\", \"VALUE\", \"cad_gdp_mm\"),\n", "    (\"CAGDP=ECIX\", \"VALUE\", \"cad_gdp_quarterly\"),\n", "    (\"CAGDPA=ECIX\", \"VALUE\", \"cad_gdp_annualized\"),\n", "    (\"CAMFGS=ECIX\", \"VALUE\", \"cad_manufacturing_sales\"),\n", "    (\"CAIPMI=ECIX\", \"VALUE\", \"cad_pmi_composite\"),\n", "    (\"CAIVSA=ECIX\", \"VALUE\", \"cad_pmi\"),\n", "    (\"CAPPI=ECIX\", \"VALUE\", \"cad_producer_prices_mm\"),\n", "    (\"CAPPIY=ECIX\", \"VALUE\", \"cad_producer_prices_yy\"),\n", "    (\"CARSLS=ECIX\", \"VALUE\", \"cad_retail_sales_mm\"),\n", "    (\"CAXAUT=ECIX\", \"VALUE\", \"cad_retail_sales_ex_auto\"),\n", "    (\"CATBAL=ECIX\", \"VALUE\", \"cad_trade_balance\"),\n", "    (\"CAUNR=ECIX\", \"VALUE\", \"cad_unemployment_rate\"),\n", "    (\"CAEMPC=ECIX\", \"VALUE\", \"cad_jobs_change\"),\n", "    (\"CAEMPF=ECIX\", \"VALUE\", \"cad_full_time_employment\"),\n", "    (\"CAEMPT=ECIX\", \"VALUE\", \"cad_part_time_employment\")]\n", " \n", "chf_symbols = [\n", "    (\"CHLBOR=ECIX\", \"VALUE\", \"chf_snb_policy_rate\"),\n", "    (\"CHCPI=ECIX\", \"VALUE\", \"chf_cpi_mm\"),\n", "    (\"CHCPIY=ECIX\", \"VALUE\", \"chf_cpi_yy\"),\n", "    (\"CHGDP=ECIX\", \"VALUE\", \"chf_gdp_qq\"),\n", "    (\"CHGDPY=ECIX\", \"VALUE\", \"chf_gdp_yy\"),\n", "    (\"CHKOFL=ECIX\", \"VALUE\", \"chf_kof_indicator\"),\n", "    (\"CHPPI=ECIX\", \"VALUE\", \"chf_producer_prices\"),\n", "    (\"CHPPIY=ECIX\", \"VALUE\", \"chf_producer_prices_yy\"),\n", "    (\"CHRS=ECIX\", \"VALUE\", \"chf_retail_sales\"),\n", "    (\"CHTBAL=ECIX\", \"VALUE\", \"chf_trade_balance\"),\n", "    (\"CHJOB=ECIX\", \"VALUE\", \"chf_unemployment_rate\"),\n", "    (\"CHJOBL=ECIX\", \"VALUE\", \"chf_unemployment_rate_labor\"),\n", "    (\"CHNFP=ECIX\", \"VALUE\", \"chf_nonfarm_payrolls\")]\n", "\n", "currency_tuples = [(\"usd\", usd_symbols), (\"eur\", eur_symbols), (\"gbp\", gbp_symbols), (\"aud\", aud_symbols), (\"nzd\", nzd_symbols), (\"cad\", cad_symbols), (\"chf\", chf_symbols), (\"jpy\", jpy_symbols)]"]}, {"cell_type": "code", "execution_count": null, "id": "c338214f", "metadata": {}, "outputs": [], "source": ["#read all symbols and store as parquet files\n", "date_from = \"2000-01-01\"\n", "date_to = \"2025-12-31\"\n", "data_collections = dict()\n", "for currency, tuples in currency_tuples:\n", "    print(f\"Getting data for {currency}\")\n", "    dataframes = []\n", "    for ric, data_field, column_name in tuples:\n", "        data = get_history(ric, [data_field], date_from, date_to)\n", "        data = data.select(pl.nth(0).alias(\"datetime\"), pl.nth(1).alias(column_name))\n", "        dataframes.append(data)\n", "\n", "    data_collections[currency] = dataframes"]}, {"cell_type": "code", "execution_count": 22, "id": "26b26a32", "metadata": {}, "outputs": [], "source": ["#iterate over each currency and join the dataframes\n", "currency_joined = []\n", "directory = \"/home/<USER>/development/python_development/financial_data/econ_indicators\"\n", "for currency, dataframes in data_collections.items():\n", "    df_joined = dataframes[0]\n", "    for df in dataframes[1:]:\n", "        df_joined = df_joined.join(df, on=\"datetime\", how=\"full\", coalesce=True)\n", "    df_joined = df_joined.sort(\"datetime\")\n", "    currency_joined.append(df_joined)\n", "    df_joined.write_parquet(f\"{directory}/econ_indicators_{currency}.parquet\")"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 5}