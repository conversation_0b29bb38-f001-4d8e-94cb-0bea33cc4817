{"cells": [{"cell_type": "code", "execution_count": 1, "id": "cf0a9370", "metadata": {}, "outputs": [], "source": ["import os\n", "import polars as pl\n", "import logging.config\n", "from typing import List\n", "import lseg.data as ld"]}, {"cell_type": "code", "execution_count": null, "id": "a86439b5", "metadata": {}, "outputs": [], "source": ["session = ld.open_session()\n", "session.set_log_level(logging.DEBUG)"]}, {"cell_type": "code", "execution_count": null, "id": "9fc15f6c", "metadata": {}, "outputs": [], "source": ["def get_history(ticker: str, data_fields: List[str], start_date: str, end_date: str) -> pl.DataFrame:\n", "    data = ld.get_history(\n", "        universe=[ticker],\n", "        fields=data_fields,\n", "        start=start_date,\n", "        end=end_date\n", "    )\n", "    \n", "    polars_df = pl.from_pandas(data, include_index=True)\n", "    return polars_df"]}, {"cell_type": "code", "execution_count": null, "id": "50b3e8f5", "metadata": {}, "outputs": [], "source": ["bond_symbols = [\n", "    (\"AUD1YD=\", [\"TR.MIDPRICE\"], \"aud_1_year_bond\"),\n", "    (\"AU10YT=RR\", [\"TR.BIDYIELD\", \"TR.ASKYIELD\"], \"aud_10_year_bond\"),\n", "    (\"EUR1YD=\", [\"TR.MIDPRICE\"], \"eur_1_year_bond\"),\n", "    (\"EU10YT=RR\", [\"TR.BIDYIELD\", \"TR.ASKYIELD\"], \"eur_10_year_bond\"),\n", "    (\"GBP1YD=\", [\"TR.MIDPRICE\"],\"gbp_1_year_bond\"),\n", "    (\"GB10YT=RR\", [\"TR.BIDYIELD\", \"TR.ASKYIELD\"],\"gbp_10_year_bond\"),\n", "    (\"USD1YD=\", [\"TR.MIDPRICE\"], \"usd_1_year_bond\"),\n", "    (\"US10YT=RRPS\", [\"TR.BIDYIELD\"], \"usd_10_year_bond\"),\n", "    (\"JPY1YD=\", [\"TR.MIDPRICE\"], \"jpy_1_year_bond\"),\n", "    (\"CAD1YD=\", [\"TR.MIDPRICE\"], \"cad_1_year_bond\"),\n", "    (\"CA10YT=RR\", [\"TR.BIDYIELD\", \"TR.ASKYIELD\"], \"cad_10_year_bond\"),\n", "    (\"CHF1YD=\", [\"TR.MIDPRICE\"], \"chf_1_year_bond\"),\n", "    (\"CH10YT=RR\", [\"TR.BIDYIELD\", \"TR.ASKYIELD\"], \"chf_10_year_bond\"),\n", "    (\"NZD1YD=\", [\"TR.MIDPRICE\"], \"nzd_1_year_bond\"),\n", "    (\"NZ10YT=RR\", [\"TR.BIDYIELD\", \"TR.ASKYIELD\"], \"nzd_10_year_bond\")]"]}, {"cell_type": "code", "execution_count": null, "id": "c338214f", "metadata": {}, "outputs": [], "source": ["#read all symbols and store as parquet files\n", "date_from = \"2000-01-01\"\n", "date_to = \"2025-12-31\"\n", "dataframes = []\n", "for symbol, data_fields, column_name in bond_symbols:\n", "    print(f\"Getting data for {symbol}\")\n", "    data = get_history(symbol, data_fields, date_from, date_to)\n", "\n", "    if len(data_fields) == 1:\n", "        data = data.select(pl.nth(0).alias(\"datetime\"), pl.nth(1).alias(column_name))\n", "    else:\n", "        data = data.select(pl.nth(0).alias(\"datetime\"), ((pl.nth(1) + pl.nth(2)) / 2).alias(column_name))\n", "\n", "    dataframes.append(data)"]}, {"cell_type": "code", "execution_count": null, "id": "e6a5b9fc", "metadata": {}, "outputs": [], "source": ["#join the dataframes\n", "df_joined = dataframes[0]\n", "for df in dataframes[1:]:\n", "    df_joined = df_joined.join(df, on=\"datetime\", how=\"full\", coalesce=True)    "]}, {"cell_type": "code", "execution_count": null, "id": "ea5dfab0", "metadata": {}, "outputs": [], "source": ["#10 year Japan bond yield data is missing, import from csv file\n", "#import csv file ('jgbcme_all.csv) to polars dataframe (first column is 'datetime' of format 'dd/mm/yyyy' and second column has to be parsed to pl.Float64)\n", "jgb10 = pl.read_csv(\"jgbcme_all.csv\", has_header=True, schema={\"datetime\": pl.Datetime, \"jpy_10_year_bond\": pl.Float64}, new_columns=[\"datetime\", \"jpy_10_year_bond\"])\n", "jgb10 = jgb10.with_columns(pl.col(\"datetime\").dt.cast_time_unit('ns'))"]}, {"cell_type": "code", "execution_count": null, "id": "567902f6", "metadata": {}, "outputs": [], "source": ["#add the data to the bond dataframe\n", "df_bonds = df_joined.join(jgb10, on=\"datetime\", how=\"full\", coalesce=True)   "]}, {"cell_type": "code", "execution_count": null, "id": "4fad4092", "metadata": {}, "outputs": [], "source": ["#clean up\n", "df_bonds = df_bonds.drop_nulls().sort(\"datetime\")"]}, {"cell_type": "code", "execution_count": null, "id": "d050337b", "metadata": {}, "outputs": [], "source": ["#write to parquet\n", "df_bonds.write_parquet(\"/home/<USER>/development/python_development/financial_data/bond_data/bond_data.parquet\")"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 5}