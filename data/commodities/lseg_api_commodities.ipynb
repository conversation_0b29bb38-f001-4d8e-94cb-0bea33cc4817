{"cells": [{"cell_type": "code", "execution_count": null, "id": "cf0a9370", "metadata": {}, "outputs": [], "source": ["import os\n", "import polars as pl\n", "import logging.config\n", "from typing import List\n", "import lseg.data as ld"]}, {"cell_type": "code", "execution_count": null, "id": "a86439b5", "metadata": {}, "outputs": [], "source": ["session = ld.open_session()\n", "session.set_log_level(logging.DEBUG)"]}, {"cell_type": "code", "execution_count": null, "id": "9fc15f6c", "metadata": {}, "outputs": [], "source": ["def get_history(ticker: str, data_fields: List[str], start_date: str, end_date: str) -> pl.DataFrame:\n", "    data = ld.get_history(\n", "        universe=[ticker],\n", "        fields=data_fields,\n", "        start=start_date,\n", "        end=end_date\n", "    )\n", "    \n", "    polars_df = pl.from_pandas(data, include_index=True)\n", "    return polars_df"]}, {"cell_type": "code", "execution_count": null, "id": "50b3e8f5", "metadata": {}, "outputs": [], "source": ["commodity_symbols = [\n", "    (\"XPT=\", [\"TR.MIDPRICE\"], \"platinum\"),\n", "    (\"XAU=\", [\"TR.MIDPRICE\"], \"gold\"),\n", "    (\"XAG=\", [\"TR.MIDPRICE\"], \"silver\"),\n", "    (\"HGc1\", [\"TR.CLOSEPRICE\"], \"copper\"),\n", "    (\"CLc1\", [\"TR.CLOSEPRICE\"], \"crude_oil\"),\n", "    (\"NGc1\", [\"TR.CLOSEPRICE\"], \"nat_gas\"),\n", "    (\"Wc1\", [\"TR.CLOSEPRICE\"], \"wheat\"),\n", "    (\"Cc1\", [\"TR.CLOSEPRICE\"], \"corn\"),\n", "    (\"Sc1\", [\"TR.CLOSEPRICE\"], \"soybeans\"),\n", "    (\"RRc1\", [\"TR.CLOSEPRICE\"], \"rice\"),\n", "    (\"KCc1\", [\"TR.CLOSEPRICE\"], \"coffee\"),\n", "    (\"LCc1\", [\"TR.CLOSEPRICE\"], \"live_cattle\"),\n", "    (\"LHc1\", [\"TR.CLOSEPRICE\"], \"lean_hogs\")\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "c338214f", "metadata": {}, "outputs": [], "source": ["#read all symbols and store as parquet files\n", "date_from = \"2000-01-01\"\n", "date_to = \"2025-12-31\"\n", "dataframes = []\n", "for symbol, data_fields, column_name in commodity_symbols:\n", "    print(f\"Getting data for {symbol}\")\n", "    data = get_history(symbol, data_fields, date_from, date_to)\n", "    data = data.select(pl.nth(0).alias(\"datetime\"), pl.nth(1).alias(column_name))\n", "    dataframes.append(data)"]}, {"cell_type": "code", "execution_count": null, "id": "587217c5", "metadata": {}, "outputs": [], "source": ["for df in dataframes:\n", "    print(len(df))"]}, {"cell_type": "code", "execution_count": null, "id": "e6a5b9fc", "metadata": {}, "outputs": [], "source": ["#join the dataframes\n", "df_joined = dataframes[0]\n", "for df in dataframes[1:]:\n", "    df_joined = df_joined.join(df, on=\"datetime\", how=\"full\", coalesce=True)    "]}, {"cell_type": "code", "execution_count": null, "id": "4fad4092", "metadata": {}, "outputs": [], "source": ["#clean up\n", "df_joined = df_joined.drop_nulls().sort(\"datetime\")"]}, {"cell_type": "code", "execution_count": null, "id": "d050337b", "metadata": {}, "outputs": [], "source": ["#write to parquet\n", "df_joined.write_parquet(\"/home/<USER>/development/python_development/financial_data/commodities/commodities.parquet\")"]}], "metadata": {"kernelspec": {"display_name": "python_development", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 5}