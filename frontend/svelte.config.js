import adapter from '@sveltejs/adapter-static';
import { vitePreprocess } from '@sveltejs/vite-plugin-svelte';

/** @type {import('@sveltejs/kit').Config} */
const config = {
	// Consult https://svelte.dev/docs/kit/integrations
	// for more information about preprocessors
	preprocess: vitePreprocess(),

	kit: {
		// Configure static adapter for SPA deployment
		// The fallback page handles client-side routing for all routes
		adapter: adapter({
			fallback: 'index.html' // SPA fallback page for client-side routing
		})
	}
};

export default config;
