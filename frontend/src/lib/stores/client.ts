import { browser } from '$app/environment';
import { writable } from 'svelte/store';

// Client connection store using Svelte stores (typed)
export type ConnectionStatus = 'disconnected' | 'connecting' | 'connected' | 'error';
export interface ClientState {
  clientId: string;
  isConnected: boolean;
  lastContact: Date | null;
  connectionStatus: ConnectionStatus;
  error: string | null;
}
export const clientStore = writable<ClientState>({
  clientId: '',
  isConnected: false,
  lastContact: null,
  connectionStatus: 'disconnected',
  error: null,
});

// Load client_id from localStorage if available
export function initializeClientStore() {
  if (!browser) return;

  const savedClientId = localStorage.getItem('clientId');
  if (savedClientId) {
    clientStore.update(store => ({
      ...store,
      clientId: savedClientId
    }));
  }
}

// Save client_id to localStorage
export function saveClientId(clientId: string) {
  if (!browser) return;

  clientStore.update(store => ({
    ...store,
    clientId
  }));
  localStorage.setItem('clientId', clientId);
}

// Update connection status
export function updateConnectionStatus(status: 'disconnected' | 'connecting' | 'connected' | 'error', error?: string) {
  clientStore.update(store => ({
    ...store,
    connectionStatus: status,
    isConnected: status === 'connected',
    error: error || null,
    lastContact: status === 'connected' ? new Date() : store.lastContact
  }));
}

// Update last contact time
export function updateLastContact() {
  clientStore.update(store => ({
    ...store,
    lastContact: new Date()
  }));
}
