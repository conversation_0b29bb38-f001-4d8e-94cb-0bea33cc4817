import { browser } from '$app/environment';
import { writable, get } from 'svelte/store';
import { clientStore, updateLastContact, updateConnectionStatus } from './client';
import type { LogMessage } from '$lib/types';

// Global store state using Svelte stores (typed)
export interface LogState {
  logMessages: LogMessage[];
  wsConnected: boolean;
  wsError: string | null;
  sortColumn: keyof LogMessage | null;
  sortDirection: 'asc' | 'desc';
  filterTimestamp: string;
  filterLevel: string;
  filterSource: string;
  filterMessage: string;
}
export const logStore = writable<LogState>({
  logMessages: [],
  wsConnected: false,
  wsError: null,
  sortColumn: null,
  sortDirection: 'asc',
  filterTimestamp: '',
  filterLevel: '',
  filterSource: '',
  filterMessage: '',
});

let ws: WebSocket | null = null;
let intentionalDisconnect = false; // Track if disconnect was initiated by frontend

// Initialize websocket connection - called when client connects successfully via HTTP
export function initializeWebSocket(clientId: string) {
  if (!browser || !clientId) return;
  
  intentionalDisconnect = false; // Reset flag when connecting
  connectWebSocket(clientId);
}

function connectWebSocket(clientId: string) {
  if (ws?.readyState === WebSocket.CONNECTING || ws?.readyState === WebSocket.OPEN) {
    return; // Already connected or connecting
  }

  const base = (import.meta.env.PUBLIC_WS_BASE_URL ?? '').trim() || 'ws://localhost:8000';
  const WS_URL = `${base.replace(/\/$/, '')}/ws/${clientId}`;
  if (import.meta.env.DEV) console.log('Attempting to connect WebSocket for logs with client ID:', clientId, '->', WS_URL);
  ws = new WebSocket(WS_URL);

  ws.onopen = () => {
    logStore.update(store => ({
      ...store,
      wsConnected: true,
      wsError: null
    }));
    if (import.meta.env.DEV) console.log('WebSocket connected for logs.');
    updateLastContact();
  };

  ws.onmessage = (event) => {
    try {
      const data = JSON.parse(event.data);
      if (import.meta.env.DEV) console.log('Received WebSocket data:', data);

      if (data.type === 'log') {
        if (import.meta.env.DEV) console.log('Processing log message:', data);
        const logEntry: LogMessage = {
          timestamp: new Date(data.timestamp * 1000).toISOString(),
          level: data.level || 'UNKNOWN',
          source: data.logger_name || 'UNKNOWN',
          message: data.message || '',
        };
        if (import.meta.env.DEV) console.log('Created log entry:', logEntry);
        logStore.update(store => ({
          ...store,
          logMessages: [...store.logMessages, logEntry]
        }));
        updateLastContact();
      }
    } catch (e) {
      console.error('Failed to parse WebSocket message:', e);
    }
  };

  ws.onclose = (event) => {
    const errorMessage = event.wasClean
      ? 'WebSocket connection closed.'
      : `WebSocket disconnected: ${event.code} - ${event.reason}`;

    logStore.update(store => ({
      ...store,
      wsConnected: false,
      wsError: errorMessage
    }));

    if (event.wasClean) {
      if (import.meta.env.DEV) console.log('WebSocket closed cleanly for logs.');
    } else {
      if (import.meta.env.DEV) console.log('WebSocket disconnected unexpectedly for logs:', event);
    }

    // Only update client connection status if this wasn't an intentional frontend disconnect
    // This handles backend-initiated disconnects and crashes
    if (!intentionalDisconnect) {
      updateConnectionStatus('disconnected', errorMessage);
    }
  };

  ws.onerror = (errorEvent) => {
    logStore.update(store => ({
      ...store,
      wsConnected: false,
      wsError: 'WebSocket error occurred.'
    }));
    console.error('WebSocket error for logs:', errorEvent);
    updateConnectionStatus('error', 'WebSocket error occurred');
  };
}

// Clean shutdown - called when client disconnects
export function disconnectWebSocket() {
  intentionalDisconnect = true; // Mark as intentional disconnect
  if (ws) {
    ws.close(1000, 'Client disconnect');
    ws = null;
  }
  logStore.update(store => ({
    ...store,
    wsConnected: false
  }));
}

export function setSort(column: keyof LogMessage) {
  logStore.update(store => {
    if (store.sortColumn === column) {
      return {
        ...store,
        sortDirection: store.sortDirection === 'asc' ? 'desc' : 'asc'
      };
    } else {
      return {
        ...store,
        sortColumn: column,
        sortDirection: 'asc'
      };
    }
  });
}

// Derived selector for filtered/sorted logs (avoids recomputation per render)
export function selectFilteredSortedLogs() {
  return {
    subscribe(run: (v: LogMessage[]) => void) {
      return logStore.subscribe((store) => {
        let filtered = store.logMessages.filter((log) => {
          const timestamp = log.timestamp || '';
          const level = log.level || '';
          const source = log.source || '';
          const message = log.message || '';
          const timestampMatch = !store.filterTimestamp || timestamp.includes(store.filterTimestamp);
          const levelMatch = !store.filterLevel || level.toLowerCase().includes(store.filterLevel.toLowerCase());
          const sourceMatch = !store.filterSource || source.toLowerCase().includes(store.filterSource.toLowerCase());
          const messageMatch = !store.filterMessage || message.toLowerCase().includes(store.filterMessage.toLowerCase());
          return timestampMatch && levelMatch && sourceMatch && messageMatch;
        });
        if (store.sortColumn) {
          filtered.sort((a, b) => {
            const sortColumn = store.sortColumn!;
            const aValue = a[sortColumn] as any;
            const bValue = b[sortColumn] as any;
            if (typeof aValue === 'string' && typeof bValue === 'string') {
              return store.sortDirection === 'asc' ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
            }
            if (aValue < bValue) return store.sortDirection === 'asc' ? -1 : 1;
            if (aValue > bValue) return store.sortDirection === 'asc' ? 1 : -1;
            return 0;
          });
        }
        run(filtered);
      });
    }
  };
}
