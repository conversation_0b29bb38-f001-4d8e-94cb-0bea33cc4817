import { writable, get } from 'svelte/store';
import { fetchPositions, refreshMarketData } from '$lib/api';
import { clientStore } from './client';
import type { Position } from '$lib/types';

// Export a single store object (typed)
export interface PositionsState {
  positions: Position[];
  isLoading: boolean;
  error: string | null;
  isInitialized: boolean;
}
export const positionsStore = writable<PositionsState>({
  positions: [],
  isLoading: false,
  error: null,
  isInitialized: false, // Internal flag
});

export async function loadPositions(refresh = false) {
  // Don't fetch if no client ID is available
  const client = get(clientStore);
  if (!client.clientId) {
    positionsStore.update(store => ({
      ...store,
      positions: [],
      error: 'No client ID configured'
    }));
    return;
  }

  // Only fetch if it's a manual refresh or if it's the very first time
  const currentStore = get(positionsStore);
  if (!refresh && currentStore.isInitialized) {
    return;
  }

  positionsStore.update(store => ({
    ...store,
    isLoading: true,
    error: null
  }));

  try {
    const apiCall = refresh ? refreshMarketData : fetchPositions;
    if (import.meta.env.DEV) console.log(`${refresh ? 'Refreshing market data' : 'Fetching positions'}...`);
    const response = await apiCall();
    if (import.meta.env.DEV) console.log('API response:', response);
    if (response.success) {
      positionsStore.update(store => ({
        ...store,
        positions: [...response.positions],
        isInitialized: true,
        isLoading: false
      }));
      if (import.meta.env.DEV) console.log('Updated positions:', response.positions.length, 'positions');
    } else {
      throw new Error(response.message);
    }
  } catch (e) {
    const errorMessage = e instanceof Error ? e.message : 'An unknown error occurred.';
    positionsStore.update(store => ({
      ...store,
      error: errorMessage,
      isLoading: false
    }));
  }
}

// Reset positions when disconnecting
export function resetPositions() {
  positionsStore.update(store => ({
    ...store,
    positions: [],
    isInitialized: false,
    error: null,
    isLoading: false
  }));
}
