import { writable, get } from 'svelte/store';
import { fetchSymbols } from '$lib/api';
import { clientStore } from './client';
import type { Symbol } from '$lib/types';

// Export a single store object (typed)
export interface SymbolsState {
  symbols: Record<string, Symbol>;
  isLoading: boolean;
  error: string | null;
  isInitialized: boolean;
}
export const symbolsStore = writable<SymbolsState>({
  symbols: {},
  isLoading: false,
  error: null,
  isInitialized: false, // Internal flag
});

export async function loadSymbols() {
  if (import.meta.env.DEV) console.log('loadSymbols() called');

  // Don't fetch if no client ID is available
  const client = get(clientStore);
  if (!client.clientId) {
    if (import.meta.env.DEV) console.log('loadSymbols: No client ID configured');
    symbolsStore.update(store => ({
      ...store,
      symbols: {},
      error: 'No client ID configured'
    }));
    return;
  }

  // Only fetch if it's not already initialized
  const currentStore = get(symbolsStore);
  if (currentStore.isInitialized) {
    if (import.meta.env.DEV) console.log('loadSymbols: Symbols already initialized, skipping fetch');
    return;
  }

  if (import.meta.env.DEV) console.log('loadSymbols: Starting fetch for client:', client.clientId);
  symbolsStore.update(store => ({
    ...store,
    isLoading: true,
    error: null
  }));

  try {
    if (import.meta.env.DEV) console.log('Fetching symbols...');
    const response = await fetchSymbols();
    if (import.meta.env.DEV) console.log('Symbols API response:', response);

    symbolsStore.update(store => ({
      ...store,
      symbols: { ...response.symbols },
      isInitialized: true,
      isLoading: false
    }));

    if (import.meta.env.DEV) console.log('Updated symbols:', Object.keys(response.symbols).length, 'symbols');
  } catch (e) {
    const errorMessage = e instanceof Error ? e.message : 'An unknown error occurred.';
    console.error('loadSymbols error:', errorMessage);
    symbolsStore.update(store => ({
      ...store,
      error: errorMessage,
      isLoading: false
    }));
  }
}

// Reset symbols when disconnecting
export function resetSymbols() {
  if (import.meta.env.DEV) console.log('resetSymbols() called');
  symbolsStore.update(store => ({
    ...store,
    symbols: {},
    isInitialized: false,
    error: null,
    isLoading: false
  }));
}
