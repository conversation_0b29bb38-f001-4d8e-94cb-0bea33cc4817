<!--
  Error Boundary component for better SPA error handling
  Catches and displays errors gracefully instead of crashing the entire app
-->
<script lang="ts">
  import { onMount } from 'svelte';
  
  // Props
  interface Props {
    children?: any;
    fallback?: any;
  }
  
  let { children, fallback }: Props = $props();
  
  // Error state
  let hasError = $state(false);
  let error = $state<Error | null>(null);
  let errorInfo = $state<string>('');

  // Reset error state
  function resetError() {
    hasError = false;
    error = null;
    errorInfo = '';
  }

  // Handle errors in development
  onMount(() => {
    // Listen for unhandled errors
    const handleError = (event: ErrorEvent) => {
      console.error('Unhandled error:', event.error);
      hasError = true;
      error = event.error;
      errorInfo = event.error?.stack || 'No stack trace available';
    };

    // Listen for unhandled promise rejections
    const handleRejection = (event: PromiseRejectionEvent) => {
      console.error('Unhandled promise rejection:', event.reason);
      hasError = true;
      error = new Error(event.reason);
      errorInfo = event.reason?.stack || 'No stack trace available';
    };

    window.addEventListener('error', handleError);
    window.addEventListener('unhandledrejection', handleRejection);

    return () => {
      window.removeEventListener('error', handleError);
      window.removeEventListener('unhandledrejection', handleRejection);
    };
  });
</script>

{#if hasError}
  {#if fallback}
    {@render fallback({ error, resetError })}
  {:else}
    <div class="error-boundary">
      <div class="error-content">
        <h2>⚠️ Something went wrong</h2>
        <p>An unexpected error occurred in the application.</p>
        
        {#if error}
          <details class="error-details">
            <summary>Error Details</summary>
            <div class="error-message">
              <strong>Error:</strong> {error.message}
            </div>
            {#if errorInfo}
              <div class="error-stack">
                <strong>Stack Trace:</strong>
                <pre>{errorInfo}</pre>
              </div>
            {/if}
          </details>
        {/if}
        
        <div class="error-actions">
          <button class="btn" onclick={resetError}>
            Try Again
          </button>
          <button class="btn btn-secondary" onclick={() => window.location.reload()}>
            Reload Page
          </button>
        </div>
      </div>
    </div>
  {/if}
{:else}
  {@render children?.()}
{/if}

<style>
  .error-boundary {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 50vh;
    padding: 2rem;
    background: var(--background);
  }

  .error-content {
    max-width: 600px;
    text-align: center;
    background: var(--surface);
    padding: 2rem;
    border-radius: 0.5rem;
    border: 1px solid var(--border);
  }

  .error-content h2 {
    color: var(--error);
    margin-bottom: 1rem;
  }

  .error-content p {
    color: var(--text);
    margin-bottom: 1.5rem;
  }

  .error-details {
    text-align: left;
    margin: 1.5rem 0;
    background: var(--background);
    border: 1px solid var(--border);
    border-radius: 0.25rem;
  }

  .error-details summary {
    padding: 0.75rem;
    cursor: pointer;
    background: var(--hover);
    border-radius: 0.25rem 0.25rem 0 0;
  }

  .error-details summary:hover {
    background: var(--accent-light);
  }

  .error-message,
  .error-stack {
    padding: 0.75rem;
    border-top: 1px solid var(--border);
  }

  .error-stack pre {
    background: var(--background);
    padding: 0.5rem;
    border-radius: 0.25rem;
    overflow-x: auto;
    font-size: 0.875rem;
    margin-top: 0.5rem;
  }

  .error-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 1.5rem;
  }

  .btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 0.25rem;
    background: var(--accent);
    color: white;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.2s;
  }

  .btn:hover {
    background: var(--accent-dark);
  }

  .btn-secondary {
    background: var(--border);
    color: var(--text);
  }

  .btn-secondary:hover {
    background: var(--hover);
  }
</style>
