<script lang="ts">
  import { symbolsStore, loadSymbols } from '$lib/stores/symbols';
  import { saveSymbols as apiSaveSymbols } from '$lib/api';
  import type { Symbol } from '$lib/types';

  // Props
  let { isOpen = $bindable(false) } = $props();

  // Local state for the editable grid
  let editableSymbols = $state<Symbol[]>([]);
  let isLoading = $state(false);
  let errorMessage = $state('');
  let successMessage = $state('');

  // Sorting and filtering state
  let sortColumn = $state<keyof Symbol | null>(null);
  let sortDirection = $state<'asc' | 'desc'>('asc');
  let filters = $state<Record<keyof Symbol, string>>({
    symbolId: '',
    underlying_symbol: '',
    assetClass: '',
    baseCurrency: '',
    exchange: '',
    tradingClass: '',
    localSymbol: '',
    lastTradeDateOrContractMonth: '',
    putCall: '',
    strike: '',
    multiplier: ''
  });

  // Load symbols from store when modal opens
  $effect(() => {
    if (isOpen) {
      const symbolsData = $symbolsStore.symbols;
      editableSymbols = Object.values(symbolsData).map(symbol => ({ ...symbol }));
      errorMessage = '';
      successMessage = '';
    }
  });

  // Computed filtered and sorted symbols
  const filteredAndSortedSymbols = $derived.by(() => {
    let result = [...editableSymbols];

    // Apply filters
    result = result.filter(symbol => {
      return Object.entries(filters).every(([key, filterValue]) => {
        if (!filterValue.trim()) return true;
        const symbolValue = String(symbol[key as keyof Symbol]).toLowerCase();
        return symbolValue.includes(filterValue.toLowerCase());
      });
    });

    // Apply sorting
    if (sortColumn) {
      result.sort((a, b) => {
        const aVal = a[sortColumn!];
        const bVal = b[sortColumn!];

        let comparison = 0;
        if (typeof aVal === 'number' && typeof bVal === 'number') {
          comparison = aVal - bVal;
        } else {
          comparison = String(aVal).localeCompare(String(bVal));
        }

        return sortDirection === 'asc' ? comparison : -comparison;
      });
    }

    return result;
  });

  // Add new empty symbol row
  function addSymbol() {
    const newSymbol: Symbol = {
      symbolId: '',
      underlying_symbol: '',
      assetClass: '',
      baseCurrency: '',
      exchange: '',
      tradingClass: '',
      localSymbol: '',
      lastTradeDateOrContractMonth: '',
      putCall: '',
      strike: 0,
      multiplier: 1
    };
    editableSymbols = [...editableSymbols, newSymbol];
  }

  // Remove symbol row
  function removeSymbol(index: number) {
    editableSymbols = editableSymbols.filter((_, i) => i !== index);
  }

  // Handle sorting
  function handleSort(column: keyof Symbol) {
    if (sortColumn === column) {
      sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
      sortColumn = column;
      sortDirection = 'asc';
    }
  }

  // Get sort indicator
  function getSortIndicator(column: keyof Symbol) {
    if (sortColumn === column) {
      return sortDirection === 'asc' ? ' ▲' : ' ▼';
    }
    return '';
  }

  // Reload symbols from backend
  async function reloadSymbols() {
    isLoading = true;
    errorMessage = '';
    successMessage = '';

    try {
      // Reset the symbols store to force a fresh fetch
      symbolsStore.update(store => ({
        ...store,
        isInitialized: false,
        symbols: {}
      }));
      await loadSymbols();

      // Update local editable symbols
      const symbolsData = $symbolsStore.symbols;
      editableSymbols = Object.values(symbolsData).map(symbol => ({ ...symbol }));
      successMessage = 'Symbols reloaded successfully';
    } catch (error) {
      errorMessage = error instanceof Error ? error.message : 'Failed to reload symbols';
    } finally {
      isLoading = false;
    }
  }

  // Save symbols to backend
  async function saveSymbolsToBackend() {
    isLoading = true;
    errorMessage = '';
    successMessage = '';

    try {
      // Convert array back to Record format for API
      const symbolsRecord: Record<string, Symbol> = {};
      editableSymbols.forEach(symbol => {
        if (symbol.symbolId.trim()) { // Only include symbols with valid IDs
          symbolsRecord[symbol.symbolId] = symbol;
        }
      });

      await apiSaveSymbols(symbolsRecord);

      // Update the symbols store with saved data
      symbolsStore.update(store => ({
        ...store,
        symbols: symbolsRecord
      }));

      successMessage = 'Symbols saved successfully';
    } catch (error) {
      errorMessage = error instanceof Error ? error.message : 'Failed to save symbols';
    } finally {
      isLoading = false;
    }
  }

  // Handle modal close
  function handleClose() {
    isOpen = false;
  }

  // Handle escape key
  function handleKeydown(event: KeyboardEvent) {
    if (event.key === 'Escape') {
      handleClose();
    }
  }
</script>

{#if isOpen}
  <!-- Modal backdrop -->
  <div class="modal-backdrop" onclick={handleClose} onkeydown={handleKeydown} role="presentation">
    <!-- Modal content -->
    <div class="modal-content" onclick={(e) => e.stopPropagation()} onkeydown={handleKeydown} role="dialog" aria-modal="true" aria-labelledby="modal-title" tabindex="-1">
      <div class="modal-header">
        <h2 id="modal-title">Manage Symbols</h2>
        <button class="close-button" onclick={handleClose} aria-label="Close modal">×</button>
      </div>

      <div class="modal-body">
        <!-- Action buttons card -->
        <div class="actions-card">
          <div class="action-buttons">
            <button class="btn-action" onclick={reloadSymbols} disabled={isLoading}>
              {isLoading ? 'Loading...' : 'Reload Symbols'}
            </button>
            <button class="btn-action" onclick={saveSymbolsToBackend} disabled={isLoading}>
              Save Symbols
            </button>
            <button class="btn-action" onclick={addSymbol}>
              Add Symbol
            </button>
          </div>
        </div>

        <!-- Status messages -->
        {#if errorMessage}
          <div class="error-message">
            {errorMessage}
          </div>
        {/if}

        {#if successMessage}
          <div class="success-message">
            {successMessage}
          </div>
        {/if}

        <!-- Symbols grid -->
        <div class="grid-container">
          <div class="grid-wrapper">
            <table class="symbols-table">
              <thead>
                <tr>
                  <th class="actions-col">Actions</th>
                  <th class="sortable" onclick={() => handleSort('symbolId')}>
                    Symbol ID{getSortIndicator('symbolId')}
                    <input 
                      type="text" 
                      placeholder="Filter..." 
                      bind:value={filters.symbolId}
                      onclick={(e) => e.stopPropagation()}
                    />
                  </th>
                  <th class="sortable" onclick={() => handleSort('underlying_symbol')}>
                    Underlying{getSortIndicator('underlying_symbol')}
                    <input 
                      type="text" 
                      placeholder="Filter..." 
                      bind:value={filters.underlying_symbol}
                      onclick={(e) => e.stopPropagation()}
                    />
                  </th>
                  <th class="sortable" onclick={() => handleSort('assetClass')}>
                    Asset Class{getSortIndicator('assetClass')}
                    <input 
                      type="text" 
                      placeholder="Filter..." 
                      bind:value={filters.assetClass}
                      onclick={(e) => e.stopPropagation()}
                    />
                  </th>
                  <th class="sortable" onclick={() => handleSort('baseCurrency')}>
                    Base Currency{getSortIndicator('baseCurrency')}
                    <input 
                      type="text" 
                      placeholder="Filter..." 
                      bind:value={filters.baseCurrency}
                      onclick={(e) => e.stopPropagation()}
                    />
                  </th>
                  <th class="sortable" onclick={() => handleSort('exchange')}>
                    Exchange{getSortIndicator('exchange')}
                    <input 
                      type="text" 
                      placeholder="Filter..." 
                      bind:value={filters.exchange}
                      onclick={(e) => e.stopPropagation()}
                    />
                  </th>
                  <th class="sortable" onclick={() => handleSort('tradingClass')}>
                    Trading Class{getSortIndicator('tradingClass')}
                    <input
                      type="text"
                      placeholder="Filter..."
                      bind:value={filters.tradingClass}
                      onclick={(e) => e.stopPropagation()}
                    />
                  </th>
                  <th class="sortable" onclick={() => handleSort('localSymbol')}>
                    Local Symbol{getSortIndicator('localSymbol')}
                    <input
                      type="text"
                      placeholder="Filter..."
                      bind:value={filters.localSymbol}
                      onclick={(e) => e.stopPropagation()}
                    />
                  </th>
                  <th class="sortable" onclick={() => handleSort('lastTradeDateOrContractMonth')}>
                    Last Trade Date{getSortIndicator('lastTradeDateOrContractMonth')}
                    <input
                      type="text"
                      placeholder="Filter..."
                      bind:value={filters.lastTradeDateOrContractMonth}
                      onclick={(e) => e.stopPropagation()}
                    />
                  </th>
                  <th class="sortable" onclick={() => handleSort('putCall')}>
                    Put/Call{getSortIndicator('putCall')}
                    <input 
                      type="text" 
                      placeholder="Filter..." 
                      bind:value={filters.putCall}
                      onclick={(e) => e.stopPropagation()}
                    />
                  </th>
                  <th class="sortable" onclick={() => handleSort('strike')}>
                    Strike{getSortIndicator('strike')}
                    <input 
                      type="text" 
                      placeholder="Filter..." 
                      bind:value={filters.strike}
                      onclick={(e) => e.stopPropagation()}
                    />
                  </th>
                  <th class="sortable" onclick={() => handleSort('multiplier')}>
                    Multiplier{getSortIndicator('multiplier')}
                    <input 
                      type="text" 
                      placeholder="Filter..." 
                      bind:value={filters.multiplier}
                      onclick={(e) => e.stopPropagation()}
                    />
                  </th>
                </tr>
              </thead>
              <tbody>
                {#if filteredAndSortedSymbols.length === 0}
                  <tr>
                    <td colspan="12" class="no-data">No symbols found</td>
                  </tr>
                {:else}
                  {#each filteredAndSortedSymbols as symbol, index (symbol.symbolId + index)}
                    <tr>
                      <td class="actions-cell">
                        <button class="btn-remove" onclick={() => removeSymbol(index)} title="Remove symbol">
                          ×
                        </button>
                      </td>
                      <td><input type="text" bind:value={symbol.symbolId} /></td>
                      <td><input type="text" bind:value={symbol.underlying_symbol} /></td>
                      <td><input type="text" bind:value={symbol.assetClass} /></td>
                      <td><input type="text" bind:value={symbol.baseCurrency} /></td>
                      <td><input type="text" bind:value={symbol.exchange} /></td>
                      <td><input type="text" bind:value={symbol.tradingClass} /></td>
                      <td><input type="text" bind:value={symbol.localSymbol} /></td>
                      <td><input type="text" bind:value={symbol.lastTradeDateOrContractMonth} /></td>
                      <td><input type="text" bind:value={symbol.putCall} /></td>
                      <td><input type="number" bind:value={symbol.strike} step="0.01" /></td>
                      <td><input type="number" bind:value={symbol.multiplier} step="1" /></td>
                    </tr>
                  {/each}
                {/if}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
{/if}

<style>
  /* Modal styles - matching OrderModal */
  .modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }

  .modal-content {
    background: var(--surface);
    border-radius: 0.5rem;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    max-width: 98vw;
    width: 1800px;
    height: 85vh;
    overflow: hidden;
    border: 1px solid var(--border);
    display: flex;
    flex-direction: column;
  }

  .modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.5rem;
    border-bottom: 1px solid var(--border);
    flex-shrink: 0;
  }

  .modal-header h2 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 700;
    color: #f97316; /* Orange color matching OrderModal */
  }

  .close-button {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--muted);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 0.25rem;
    transition: all 0.2s ease;
  }

  .close-button:hover {
    background: var(--hover);
    color: var(--text);
  }

  .modal-body {
    padding: 1.5rem;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    flex: 1;
    min-height: 0;
  }

  /* Actions card */
  .actions-card {
    background: var(--background);
    border: 1px solid var(--border);
    border-radius: 0.375rem;
    padding: 1rem;
    margin-bottom: 1.5rem;
    flex-shrink: 0;
  }

  .action-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
  }

  .btn-action {
    padding: 0.75rem 1.5rem;
    border: 1px solid var(--border);
    border-radius: 0.375rem;
    background: var(--surface);
    color: var(--text);
    font-weight: 600;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .btn-action:hover:not(:disabled) {
    background: var(--surface-2);
    border-color: #f97316;
  }

  .btn-action:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  /* Status messages */
  .error-message {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.3);
    color: #ef4444;
    padding: 0.75rem;
    border-radius: 0.375rem;
    margin-bottom: 1.25rem;
    font-size: 0.875rem;
  }

  .success-message {
    background: rgba(34, 197, 94, 0.1);
    border: 1px solid rgba(34, 197, 94, 0.3);
    color: #22c55e;
    padding: 0.75rem;
    border-radius: 0.375rem;
    margin-bottom: 1.25rem;
    font-size: 0.875rem;
  }

  /* Grid container */
  .grid-container {
    flex: 1;
    min-height: 400px;
    max-height: 500px;
    border: 1px solid var(--border);
    border-radius: 0.375rem;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  .grid-wrapper {
    flex: 1;
    overflow: auto;
    min-height: 0;
  }

  /* Table styles */
  .symbols-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.875rem;
    min-width: 1600px;
    table-layout: fixed;
  }

  /* Column width specifications */
  .symbols-table th:nth-child(1) { width: 80px; }   /* Actions */
  .symbols-table th:nth-child(2) { width: 200px; }  /* Symbol ID - 3x wider */
  .symbols-table th:nth-child(3) { width: 120px; }  /* Underlying Symbol */
  .symbols-table th:nth-child(4) { width: 100px; }  /* Asset Class */
  .symbols-table th:nth-child(5) { width: 100px; }  /* Base Currency */
  .symbols-table th:nth-child(6) { width: 100px; }  /* Exchange */
  .symbols-table th:nth-child(7) { width: 120px; }  /* Trading Class */
  .symbols-table th:nth-child(8) { width: 120px; }  /* Local Symbol */
  .symbols-table th:nth-child(9) { width: 180px; }  /* Last Trade Date - wider for header */
  .symbols-table th:nth-child(10) { width: 100px; } /* Put/Call */
  .symbols-table th:nth-child(11) { width: 100px; } /* Strike */
  .symbols-table th:nth-child(12) { width: 100px; } /* Multiplier */

  .symbols-table th {
    background: var(--surface-2);
    border-bottom: 2px solid var(--border);
    padding: 0.75rem 0.5rem;
    text-align: left;
    font-weight: 600;
    color: var(--text);
    position: sticky;
    top: 0;
    z-index: 10;
  }

  .symbols-table th.sortable {
    cursor: pointer;
    user-select: none;
  }

  .symbols-table th.sortable:hover {
    background: var(--hover);
  }

  .symbols-table th input {
    width: 100%;
    margin-top: 0.5rem;
    padding: 0.25rem;
    border: 1px solid var(--border);
    border-radius: 0.25rem;
    background: var(--background);
    color: var(--text);
    font-size: 0.75rem;
  }

  .symbols-table td {
    border-bottom: 1px solid var(--border);
    padding: 0.5rem;
    vertical-align: middle;
    overflow: hidden;
  }

  .symbols-table td input {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid var(--border);
    border-radius: 0.25rem;
    background: var(--background);
    color: var(--text);
    font-size: 0.875rem;
    transition: all 0.2s ease;
    box-sizing: border-box;
  }

  .symbols-table td input:focus {
    outline: none;
    border-color: #f97316;
    box-shadow: 0 0 0 2px rgba(249, 115, 22, 0.1);
  }

  .actions-col {
    width: 80px;
    min-width: 80px;
  }

  .actions-cell {
    text-align: center;
  }

  .btn-remove {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.3);
    color: #ef4444;
    border-radius: 0.25rem;
    width: 24px;
    height: 24px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
  }

  .btn-remove:hover {
    background: rgba(239, 68, 68, 0.2);
    border-color: rgba(239, 68, 68, 0.5);
  }

  .no-data {
    text-align: center;
    color: var(--muted);
    font-style: italic;
    padding: 2rem;
  }

  /* Responsive design */
  @media (max-width: 1900px) {
    .modal-content {
      width: 95vw;
    }
  }

  @media (max-width: 1400px) {
    .modal-content {
      width: 98vw;
    }

    .symbols-table {
      min-width: 1400px;
    }
  }

  @media (max-width: 768px) {
    .modal-content {
      width: 98vw;
      height: 90vh;
    }

    .modal-header,
    .modal-body {
      padding: 1rem;
    }

    .action-buttons {
      flex-direction: column;
    }

    .btn-action {
      width: 100%;
    }

    .symbols-table {
      min-width: 1200px;
    }

    .grid-container {
      min-height: 300px;
      max-height: 400px;
    }
  }
</style>
