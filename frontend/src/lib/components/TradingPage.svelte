<script lang="ts">
  import { positionsStore, loadPositions } from '$lib/stores/positions';
  import { clientStore } from '$lib/stores/client';
  import { symbolsStore } from '$lib/stores/symbols';
  import { formatPrice, formatNumber, formatPnl, formatTimestamp } from '$lib/formatters';
  import OrderModal from './OrderModal.svelte';

  // Modal state
  let isOrderModalOpen = $state(false);

  // Derived state from the global store
  const hasPositions = $derived(($positionsStore.positions?.length ?? 0) > 0);
  const totalUnrealizedPnlBase = $derived(
    ($positionsStore.positions ?? []).reduce((sum, p) => sum + p.unrealized_pnl_base, 0)
  );
  const totalRealizedPnlBase = $derived(
    ($positionsStore.positions ?? []).reduce((sum, p) => sum + p.realized_pnl_base, 0)
  );

  // Check if buttons should be enabled
  const canFetchData = $derived($clientStore.clientId && !$positionsStore.isLoading);

  function openOrderModal() {
    isOrderModalOpen = true;
  }
</script>



<section class="card" style="margin-bottom: 1rem;">
  <div class="action-buttons">
    <button class="btn-basic" onclick={() => loadPositions()} disabled={!canFetchData}>
      Fetch Positions
    </button>
    <button class="btn-basic" onclick={() => loadPositions(true)} disabled={!canFetchData}>
      Refresh Market Data
    </button>
    <button class="btn-basic" onclick={openOrderModal} disabled={!canFetchData}>
      Create Order
    </button>

    <div class="pnl-summary">
      <div class="pnl-item">
        <span class="pnl-label">Total Unrealized (Base) P&L:</span>
        <span class="pnl-value" class:positive={totalUnrealizedPnlBase > 0} class:negative={totalUnrealizedPnlBase < 0}>
          {totalUnrealizedPnlBase.toFixed(2)}
        </span>
      </div>
      <div class="pnl-item">
        <span class="pnl-label">Total Realized (Base) P&L:</span>
        <span class="pnl-value" class:positive={totalRealizedPnlBase > 0} class:negative={totalRealizedPnlBase < 0}>
          {totalRealizedPnlBase.toFixed(2)}
        </span>
      </div>
    </div>
  </div>
</section>

<section class="card">
  <div style="overflow-x: auto;">
    <table class="table" style="min-width: 1200px;">
      <thead>
        <tr>
          <th>Created At</th>
          <th>Updated At</th>
          <th>Strategy</th>
          <th>Symbol</th>
          <th>Size</th>
          <th>Avg. Price</th>
          <th>Current Price</th>
          <th>Unrealized P&L</th>
          <th>Unrealized P&L (Base)</th>
          <th>Realized P&L</th>
          <th>Realized P&L (Base)</th>
        </tr>
      </thead>
      <tbody>
        {#if !hasPositions && !$positionsStore.isLoading}
          <tr>
            <td colspan="11" style="text-align: center; color: var(--muted);">
              No positions found.
            </td>
          </tr>
        {/if}
        {#each $positionsStore.positions as p (p.position_id)}
          <tr>
            <td>{formatTimestamp(p.timestamp_first_fill)}</td>
            <td>{formatTimestamp(p.timestamp_current)}</td>
            <td>{p.strategy_id}</td>
            <td>{p.symbol_id}</td>
            <td>{formatNumber(p.size)}</td>
            <td>{formatPrice(p.average_price)}</td>
            <td style="background-color: rgba(34, 197, 94, 0.1);">{formatPrice(p.current_price)}</td>
            <td class:pnl-positive={p.unrealized_pnl_local >= 0} class:pnl-negative={p.unrealized_pnl_local < 0}>
              {formatPnl(p.unrealized_pnl_local)}
            </td>
            <td class:pnl-positive={p.unrealized_pnl_base >= 0} class:pnl-negative={p.unrealized_pnl_base < 0}>
              {formatPnl(p.unrealized_pnl_base)}
            </td>
            <td class:pnl-positive={p.realized_pnl_local >= 0} class:pnl-negative={p.realized_pnl_local < 0}>
              {formatPnl(p.realized_pnl_local)}
            </td>
            <td class:pnl-positive={p.realized_pnl_base >= 0} class:pnl-negative={p.realized_pnl_base < 0}>
              {formatPnl(p.realized_pnl_base)}
            </td>
          </tr>
        {/each}
      </tbody>
    </table>
  </div>
</section>

<!-- Order Modal -->
<OrderModal bind:isOpen={isOrderModalOpen} />

<style>
  .action-buttons {
    display: flex;
    gap: 1.5rem;
    align-items: center;
    flex-wrap: wrap;
  }

  .btn-basic {
    padding: 0.75rem 1.5rem;
    border: 1px solid var(--border);
    border-radius: 0.375rem;
    background: var(--surface);
    color: var(--text);
    font-weight: 600;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .btn-basic:hover:not(:disabled) {
    background: var(--surface-2);
    border-color: var(--accent);
  }

  .btn-basic:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  .pnl-summary {
    display: flex;
    gap: 1.5rem;
    align-items: center;
    flex-wrap: wrap;
    margin-left: auto;
  }

  .pnl-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
    padding: 0.5rem 1rem;
    border: 1px solid var(--border);
    border-radius: 0.375rem;
    background: var(--background);
    min-width: 160px;
  }

  .pnl-label {
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--muted);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    text-align: center;
  }

  .pnl-value {
    font-size: 1rem;
    font-weight: 700;
    font-family: 'Courier New', monospace;
  }

  .pnl-value.positive {
    color: var(--positive);
  }

  .pnl-value.negative {
    color: var(--negative);
  }

  @media (max-width: 768px) {
    .action-buttons {
      flex-direction: column;
      align-items: stretch;
      gap: 1rem;
    }

    .pnl-summary {
      margin-left: 0;
      justify-content: center;
    }

    .pnl-item {
      min-width: auto;
      flex: 1;
    }
  }
</style>