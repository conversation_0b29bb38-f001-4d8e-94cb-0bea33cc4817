<script lang="ts">
  import { tick } from 'svelte';
  import { onMount } from 'svelte';
  import { clientStore, saveClientId, updateConnectionStatus, initializeClientStore } from '$lib/stores/client';
  import { initializeWebSocket, disconnectWebSocket, setSort } from '$lib/stores/logs';
  import { loadPositions, resetPositions } from '$lib/stores/positions';
  import { loadSymbols, resetSymbols } from '$lib/stores/symbols';
  import { connectClient, disconnectClient, reconnectClient } from '$lib/api';
  import { formatTimestamp } from '$lib/formatters';
  import type { LogMessage } from '$lib/types';
  import { page } from '$app/state';
  import SymbolModal from './SymbolModal.svelte';

  let isLoading = $state(false);
  let tempClientId = $state('');
  let tableContainer: HTMLDivElement | undefined;
  let isSymbolModalOpen = $state(false);

  // Use shared derived selector for logs
  import { selectFilteredSortedLogs, logStore } from '$lib/stores/logs';
  const filteredLogs = selectFilteredSortedLogs();

  onMount(() => {
    initializeClientStore();
    tempClientId = $clientStore.clientId;
  });

  function getSortIndicator(column: keyof LogMessage) {
    if ($logStore.sortColumn === column) {
      return $logStore.sortDirection === 'asc' ? ' ▲' : ' ▼';
    }
    return '';
  }

  // Auto-scroll when messages change
  $effect(() => {
    if (tableContainer && $logStore.logMessages.length > 0) {
      // Reference the messages array to track changes
      $logStore.logMessages;

      // Always scroll to bottom when messages change
      tick().then(() => {
        tableContainer!.scrollTo(0, tableContainer!.scrollHeight);
      });
    }
  });

  // Auto-scroll when the page path indicates dashboard and logs change
  $effect(() => {
    if (tableContainer && (typeof window !== 'undefined') && window.location.pathname.startsWith('/dashboard') && $logStore.logMessages.length > 0) {
      // Always scroll to bottom when returning to dashboard
      tick().then(() => {
        tableContainer!.scrollTo(0, tableContainer!.scrollHeight);
      });
    }
  });

  async function handleConnect() {
    if (!tempClientId.trim()) {
      updateConnectionStatus('error', 'Please enter a client ID');
      return;
    }

    const clientIdToConnect = tempClientId.trim();
    isLoading = true;
    updateConnectionStatus('connecting');

    try {
      // Step 1: Connect via HTTP and wait for success=true response
      const response = await connectClient(clientIdToConnect);
      
      // Check if backend responded with success=true
      if (response.success) {
        saveClientId(clientIdToConnect);
        updateConnectionStatus('connected');
        
        // Step 2: Only after HTTP success, establish WebSocket connection
        disconnectWebSocket();
        setTimeout(() => {
          initializeWebSocket(clientIdToConnect);
        }, 100);
        
        // Load initial data - positions and symbols
        await loadPositions();
        await loadSymbols();
      } else {
        updateConnectionStatus('error', response.message || 'Backend initialization failed');
      }
      
    } catch (error) {
      console.error('Failed to connect:', error);
      updateConnectionStatus('error', error instanceof Error ? error.message : 'Connection failed');
    } finally {
      isLoading = false;
    }
  }

  async function handleDisconnect() {
    if (!$clientStore.clientId) {
      updateConnectionStatus('error', 'No client ID configured');
      return;
    }

    const currentClientId = $clientStore.clientId;
    isLoading = true;
    updateConnectionStatus('connecting');

    try {
      // Step 1: Call HTTP disconnect endpoint
      // Backend will remove the client but NOT close the WebSocket
      const response = await disconnectClient(currentClientId);
      
      if (response.success) {
        // Step 2: Frontend closes the WebSocket after successful HTTP response
        disconnectWebSocket();
        
        // Reset data stores when disconnecting
        resetPositions();
        resetSymbols();
        
        updateConnectionStatus('disconnected', 'Successfully disconnected');
      } else {
        updateConnectionStatus('error', response.message || 'Disconnect failed');
      }
      
    } catch (error) {
      console.error('Failed to disconnect:', error);
      updateConnectionStatus('error', error instanceof Error ? error.message : 'Disconnect failed');
    } finally {
      isLoading = false;
    }
  }

  async function handleReconnect() {
    if (!$clientStore.clientId) {
      updateConnectionStatus('error', 'No client ID configured');
      return;
    }

    const currentClientId = $clientStore.clientId;
    isLoading = true;
    updateConnectionStatus('connecting');

    try {
      // Step 1: Close any existing WebSocket connection on frontend
      disconnectWebSocket();
      
      // Step 2: Call HTTP reconnect endpoint
      // Backend will close any existing WebSocket and prepare for new connection
      const response = await reconnectClient(currentClientId);
      
      if (response.success) {
        updateConnectionStatus('connected');
        
        // Step 3: Establish new WebSocket connection
        setTimeout(() => {
          initializeWebSocket(currentClientId);
        }, 100);
        
        // Reload data - positions and symbols
        await loadPositions();
        await loadSymbols();
      } else {
        updateConnectionStatus('error', response.message || 'Backend reconnection failed');
      }
      
    } catch (error) {
      console.error('Failed to reconnect:', error);
      updateConnectionStatus('error', error instanceof Error ? error.message : 'Reconnection failed');
    } finally {
      isLoading = false;
    }
  }
      
  function getStatusColor(status: 'disconnected' | 'connecting' | 'connected' | 'error') {
    switch (status) {
      case 'connected': return 'var(--positive)';
      case 'connecting': return 'var(--accent)';
      case 'error': return 'var(--negative)';
      case 'disconnected': return 'var(--negative)';
      default: return 'var(--muted)';
    }
  }

  function getStatusText(status: 'disconnected' | 'connecting' | 'connected' | 'error') {
    switch (status) {
      case 'connected': return 'Connected';
      case 'connecting': return 'Connecting...';
      case 'error': return 'Error';
      default: return 'Disconnected';
    }
  }

  function formatDateTimestamp(date: Date | null) {
    if (!date) return 'Never';
    return date.toLocaleString();
  }

  function openSymbolModal() {
    isSymbolModalOpen = true;
  }
</script>

<svelte:head>
  <title>Dashboard</title>
</svelte:head>

<div class="page-container">
  <h1>Dashboard</h1>
  
  <!-- Connection Control Section -->
  <section class="card" style="margin-bottom: 1rem;">
    <h2 style="margin-top: -0.5rem; margin-bottom: 0.75rem;">Connection Settings</h2>
    
    <div class="connection-form">
      <div class="form-group">
        <label for="client-id">Client ID:</label>
        <input
          id="client-id"
          type="text"
          bind:value={tempClientId}
          placeholder="Enter client ID (e.g., trading_client_001)"
          disabled={isLoading}
          class="text-input"
        />
      </div>

      <div class="controls-row">
        <div class="button-group">
          <button
            class="btn btn-primary"
            onclick={handleConnect}
          >
            {isLoading && $clientStore.connectionStatus === 'connecting' ? 'Connecting...' : 'Connect'}
          </button>

          <button
            class="btn btn-secondary"
            onclick={handleReconnect}
          >
            {isLoading && $clientStore.connectionStatus === 'connecting' ? 'Reconnecting...' : 'Reconnect'}
          </button>

          <button
            class="btn btn-danger"
            onclick={handleDisconnect}
          >
            Disconnect
          </button>

          <button
            class="btn btn-secondary"
            onclick={openSymbolModal}
            disabled={$clientStore.connectionStatus !== 'connected'}
          >
            Manage Symbols
          </button>
        </div>

        <div class="status-row">
          <div class="status-item-inline">
            <span class="status-label">Backend Connection:</span>
            <div class="status-value">
              <span
                class="status-indicator"
                style="background-color: {getStatusColor($clientStore.connectionStatus)}"
              ></span>
              <span class="status-text">
                {getStatusText($clientStore.connectionStatus)}
              </span>
            </div>
          </div>

          <div class="status-item-inline">
            <span class="status-label">WebSocket Connection:</span>
            <div class="status-value">
              <span
                class="status-indicator"
                style="background-color: {$logStore.wsConnected ? 'var(--positive)' : 'var(--negative)'}"
              ></span>
              <span class="status-text">
                {$logStore.wsConnected ? 'Connected' : 'Disconnected'}
              </span>
            </div>
          </div>

          <div class="status-item-inline">
            <span class="status-label">Client ID:</span>
            <div class="status-value">
              <span class="client-id-display">
                {$clientStore.clientId || 'Not configured'}
              </span>
            </div>
          </div>

          <div class="status-item-inline">
            <span class="status-label">Last Contact:</span>
            <div class="status-value">
              <span class="timestamp">
                {formatDateTimestamp($clientStore.lastContact)}
              </span>
            </div>
          </div>
        </div>
      </div>


    </div>
  </section>



  <!-- Log Messages Section -->
  <section class="card">
    <h2 style="margin-top: -0.5rem; margin-bottom: 0.75rem;">Log Messages</h2>
    
    <div style="margin-bottom: 0.5rem; display: flex; align-items: center; gap: 1rem; flex-wrap: wrap;">
      <span style="color: var(--muted);">
        Total: {$logStore.logMessages?.length ?? 0} | Filtered: {($filteredLogs || []).length}
      </span>
    </div>

    <div class="table-container" bind:this={tableContainer}>
      <table class="table" style="min-width: 1000px;">
        <thead>
          <tr>
            <th class="timestamp-col">
              <button class="table-header-btn" onclick={() => setSort('timestamp')}>
                Timestamp {getSortIndicator('timestamp')}
              </button>
              <input type="text" placeholder="Filter" value={$logStore.filterTimestamp || ''} oninput={(e) => logStore.update(s => ({ ...s, filterTimestamp: (e.currentTarget as HTMLInputElement).value }))} />
            </th>
            <th class="level-col">
              <button class="table-header-btn" onclick={() => setSort('level')}>
                Level {getSortIndicator('level')}
              </button>
              <input type="text" placeholder="Filter" value={$logStore.filterLevel || ''} oninput={(e) => logStore.update(s => ({ ...s, filterLevel: (e.currentTarget as HTMLInputElement).value }))} />
            </th>
            <th class="source-col">
              <button class="table-header-btn" onclick={() => setSort('source')}>
                Source {getSortIndicator('source')}
              </button>
              <input type="text" placeholder="Filter" value={$logStore.filterSource || ''} oninput={(e) => logStore.update(s => ({ ...s, filterSource: (e.currentTarget as HTMLInputElement).value }))} />
            </th>
            <th class="message-col">
              <button class="table-header-btn" onclick={() => setSort('message')}>
                Message {getSortIndicator('message')}
              </button>
              <input type="text" placeholder="Filter" value={$logStore.filterMessage || ''} oninput={(e) => logStore.update(s => ({ ...s, filterMessage: (e.currentTarget as HTMLInputElement).value }))} />
            </th>
          </tr>
        </thead>
        <tbody>
          {#if $filteredLogs.length === 0}
            <tr>
              <td colspan="4" style="text-align: center; color: var(--muted);">
                No log messages received.
              </td>
            </tr>
          {/if}
          {#each $filteredLogs as log (log.timestamp + log.message)}
            <tr>
              <td>{formatTimestamp(log.timestamp)}</td>
              <td>{log.level}</td>
              <td>{log.source}</td>
              <td>{log.message}</td>
            </tr>
          {/each}
        </tbody>
      </table>
    </div>
  </section>
</div>

<!-- Symbol Modal -->
<SymbolModal bind:isOpen={isSymbolModalOpen} />

<style>
  .page-container {
    padding: 1rem;
  }

  h1 {
    color: var(--accent-2);
    margin-bottom: 1rem;
  }

  h2 {
    color: var(--accent);
    margin-bottom: 0.75rem;
    font-size: 1.25rem;
  }

  .card {
    background: var(--surface);
    border-radius: 0.5rem;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border: 1px solid var(--border);
  }

  .connection-form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  .controls-row {
    display: flex;
    align-items: center;
    gap: 2rem;
    flex-wrap: wrap;
  }

  .status-row {
    display: flex;
    gap: 1.5rem;
    flex-wrap: wrap;
    flex: 1;
  }

  .status-item-inline {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
    min-width: 120px;
    text-align: center;
    padding: 0.5rem;
    border: 1px solid var(--border);
    border-radius: 0.375rem;
    background: var(--background);
  }

  .status-item-inline .status-label {
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--muted);
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .status-item-inline .status-value {
    display: flex;
    align-items: center;
    gap: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
  }



  .form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .form-group label {
    font-weight: 600;
    color: var(--text);
  }

  .text-input {
    padding: 0.75rem;
    border: 1px solid var(--border);
    border-radius: 0.375rem;
    background: var(--background);
    color: var(--text);
    font-size: 1rem;
  }

  .text-input:focus {
    outline: none;
    border-color: var(--accent);
    box-shadow: 0 0 0 2px rgba(var(--accent-rgb), 0.2);
  }

  .text-input:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  .button-group {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
  }

  .btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 0.375rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
    font-size: 0.875rem;
  }

  .btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  .btn-primary {
    background: var(--accent);
    color: white;
  }

  .btn-primary:hover:not(:disabled) {
    background: var(--accent-2);
  }

  .btn-secondary {
    background: var(--muted);
    color: var(--text);
  }

  .btn-secondary:hover:not(:disabled) {
    background: var(--border);
  }

  .btn-danger {
    background: var(--negative);
    color: white;
  }

  .btn-danger:hover:not(:disabled) {
    background: #dc2626;
  }





  .status-label {
    font-weight: 600;
    color: var(--text);
  }

  .status-value {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    flex-shrink: 0;
  }

  .status-text {
    font-weight: 500;
  }

  .client-id-display {
    font-family: monospace;
    font-size: 0.875rem;
    color: var(--accent);
  }

  .timestamp {
    font-size: 0.875rem;
    color: var(--muted-text);
  }



  /* Log Messages Table Styles */
  .table-container {
    /* Decreased height by 17% total (15% + 2%) from 420px to ~500px */
    height: calc(100vh - 500px);
    min-height: 250px;
    overflow-y: auto;
    overflow-x: auto;
    border: 1px solid var(--border);
    border-radius: 4px;
    scroll-behavior: smooth;
    margin: 0;
    padding: 0;
  }
  
  .table {
    margin: 0;
    border-spacing: 0;
  }
  
  .table thead {
    position: sticky;
    top: 0;
    z-index: 10;
    background: var(--surface);
    margin: 0;
    padding: 0;
  }
  
  .table thead th {
    margin: 0;
    padding: 0.5rem;
    border-bottom: 1px solid var(--border);
    background: var(--surface);
  }
  
  .timestamp-col {
    width: 200px;
    min-width: 200px;
  }
  
  .level-col {
    width: 120px;
    min-width: 120px;
  }
  
  .source-col {
    width: 450px;
    min-width: 450px;
  }
  
  .message-col {
    width: auto;
    min-width: 300px;
  }
  
  .table td {
    word-break: break-word;
    vertical-align: top;
  }
  
  .table td:nth-child(4) {
    text-align: left;
  }
  
  /* Use shared .table-header-btn styles from app.css */
  .table th input {
    width: calc(100% - 10px);
    padding: 4px;
    margin-top: 5px;
    border: 1px solid var(--border);
    background: var(--surface-2);
    color: var(--text);
    border-radius: 3px;
  }

  @media (max-width: 768px) {
    .controls-row {
      flex-direction: column;
      align-items: stretch;
      gap: 1rem;
    }

    .button-group {
      flex-direction: column;
    }

    .status-row {
      justify-content: space-between;
    }

    .status-item-inline {
      min-width: auto;
      flex: 1;
    }




  }
</style>