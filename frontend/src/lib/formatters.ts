export const formatPrice = (value: number) => value?.toFixed(4) ?? 'N/A';

export const formatNumber = (value: number) => value?.toLocaleString('en-US') ?? 'N/A';

export const formatPnl = (value: number) =>
  value?.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) ?? 'N/A';

export function formatTimestamp(isoString: string | null): string {
  if (!isoString) return 'N/A';
  const d = new Date(isoString);
  if (isNaN(d.getTime())) return 'Invalid Date';
  return d.toISOString().replace('T', ' ').substring(0, 19);
}
