export interface Position {
  position_id: string;
  strategy_id: string;
  symbol_id: string;
  timestamp_current: string;
  timestamp_first_fill: string;
  size: number;
  average_price: number;
  current_price: number;
  unrealized_pnl_local: number;
  unrealized_pnl_base: number;
  realized_pnl_local: number;
  realized_pnl_base: number;
}

export interface PositionsApiResponse {
  success: boolean;
  message: string;
  positions: Position[];
}

export interface OrderRequest {
    strategy_id: string;
    symbol_id: string;
    size: number;
    order_type: string;
    limit_price: number;
    stop_price: number;
    custom_datetime: string;
}

export interface OrderSubmissionResponse {
  success: boolean;
  message: string;
  order_id: string;
}

export interface MarketDataRefreshResponse extends PositionsApiResponse {}

export interface ClientConnectResponse {
  success: boolean;
  message: string;
  client_id: string;
}

export interface ClientDisconnectResponse {
  success: boolean;
  message: string;
  client_id: string;
}

export interface Symbol {
  symbolId: string;
  underlying_symbol: string;
  assetClass: string;
  baseCurrency: string;
  exchange: string;
  tradingClass: string;
  localSymbol: string;
  lastTradeDateOrContractMonth: string;
  putCall: string;
  strike: number;
  multiplier: number;
}

export interface GetSymbolsApiResponse {
  symbols: Record<string, Symbol>;
}

export interface LogMessage {
  timestamp: string; // ISO format, e.g., "2025-08-15T18:48:14.123"
  level: string;     // e.g., "INFO", "WARNING", "ERROR"
  source: string;    // Corresponds to the Python logger's 'name'
  message: string;   // The actual log message content
}
