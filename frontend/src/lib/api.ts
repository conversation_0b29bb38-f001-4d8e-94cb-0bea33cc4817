import { PUBLIC_API_BASE_URL } from '$env/static/public';
import type { PositionsApiResponse, MarketDataRefreshResponse, ClientConnectResponse, ClientDisconnectResponse, GetSymbolsApiResponse, OrderRequest, OrderSubmissionResponse, Symbol, SaveSymbolsRequest, SaveSymbolsResponse } from './types';
import { clientStore } from './stores/client';
import { get } from 'svelte/store';

const API_BASE = PUBLIC_API_BASE_URL || 'http://localhost:8000';

// Client management functions with new endpoints
export async function connectClient(clientId: string): Promise<ClientConnectResponse> {
  const response = await fetch(`${API_BASE}/clients/${clientId}/connect`, {
    method: 'POST',
  });
  
  if (!response.ok) {
    throw new Error(`Failed to connect client: ${response.statusText}`);
  }
  
  return response.json();
}

export async function disconnectClient(clientId: string): Promise<ClientDisconnectResponse> {
  const response = await fetch(`${API_BASE}/clients/${clientId}/disconnect`, {
    method: 'DELETE',
  });
  
  if (!response.ok) {
    throw new Error(`Failed to disconnect client: ${response.statusText}`);
  }
  
  return response.json();
}

export async function reconnectClient(clientId: string): Promise<ClientConnectResponse> {
  const response = await fetch(`${API_BASE}/clients/${clientId}/reconnect`, {
    method: 'POST',
  });
  
  if (!response.ok) {
    throw new Error(`Failed to reconnect client: ${response.statusText}`);
  }
  
  return response.json();
}

// Update existing functions to use client_id
export async function fetchPositions(): Promise<PositionsApiResponse> {
  const client = get(clientStore);
  const clientId = client?.clientId?.trim();
  if (!clientId) {
    throw new Error('No client ID configured');
  }

  const response = await fetch(`${API_BASE}/clients/${clientId}/positions`);

  if (!response.ok) {
    throw new Error(`Failed to fetch positions: ${response.statusText}`);
  }

  return response.json();
}

export async function refreshMarketData(): Promise<MarketDataRefreshResponse> {
  const client = get(clientStore);
  const clientId = client?.clientId?.trim();
  if (!clientId) {
    throw new Error('No client ID configured');
  }

  const response = await fetch(`${API_BASE}/clients/${clientId}/market-data/refresh`);

  if (!response.ok) {
    throw new Error(`Failed to refresh market data: ${response.statusText}`);
  }

  return response.json();
}

export async function fetchSymbols(): Promise<GetSymbolsApiResponse> {
  const client = get(clientStore);
  const clientId = client?.clientId?.trim();
  if (!clientId) {
    throw new Error('No client ID configured');
  }

  const response = await fetch(`${API_BASE}/clients/${clientId}/symbols`);

  if (!response.ok) {
    throw new Error(`Failed to fetch symbols: ${response.statusText}`);
  }

  return response.json();
}

export async function submitOrder(orderRequest: OrderRequest): Promise<OrderSubmissionResponse> {
  const client = get(clientStore);
  const clientId = client?.clientId?.trim();
  if (!clientId) {
    throw new Error('No client ID configured');
  }

  const response = await fetch(`${API_BASE}/clients/${clientId}/orders`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(orderRequest),
  });

  if (!response.ok) {
    throw new Error(`Failed to submit order: ${response.statusText}`);
  }

  return response.json();
}

export async function saveSymbols(symbols: Record<string, Symbol>): Promise<SaveSymbolsResponse> {
  const client = get(clientStore);
  const clientId = client?.clientId?.trim();
  if (!clientId) {
    throw new Error('No client ID configured');
  }

  const requestBody: SaveSymbolsRequest = { symbols };

  const response = await fetch(`${API_BASE}/clients/${clientId}/save_symbols`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(requestBody),
  });

  if (!response.ok) {
    throw new Error(`Failed to save symbols: ${response.statusText}`);
  }

  return response.json();
}
