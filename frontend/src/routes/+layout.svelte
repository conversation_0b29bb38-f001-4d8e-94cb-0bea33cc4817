<script lang="ts">
  // Global app styles
  import '../app.css';
  // Error boundary to prevent whole-app crashes
  import ErrorBoundary from '$lib/components/ErrorBoundary.svelte';
  // Current page info (reactive object in SvelteKit)
  import { page } from '$app/state';
  // Children snippet for routed pages
  let { children } = $props();
</script>

<div class="app-container">
  <nav class="sidebar">
    <div class="sidebar-header">
      <div class="brand">Trading Frontend</div>
    </div>
    <ul class="nav-links">
      <li>
        <a class="nav-link" href="/dashboard" aria-current={page.url.pathname.startsWith('/dashboard') ? 'page' : undefined}>
          Dashboard
        </a>
      </li>
      <li>
        <a class="nav-link" href="/trading" aria-current={page.url.pathname.startsWith('/trading') ? 'page' : undefined}>
          Simulated Trading
        </a>
      </li>
    </ul>
  </nav>

  <main class="content">
    <ErrorBoundary>
      {@render children()}
    </ErrorBoundary>
  </main>
</div>
