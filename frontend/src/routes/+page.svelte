<script lang="ts">
  // Redirect the root route to /dashboard using SvelteKit navigation
  import { goto } from '$app/navigation';
  import { onMount } from 'svelte';
  onMount(() => goto('/dashboard'));
</script>

<div class="loading-container">
  <div class="loading-content">
    <div class="spinner"></div>
    <p>Loading Trading Frontend...</p>
  </div>
</div>

<style>
  .loading-container {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    background: var(--background);
  }
  .loading-content { text-align: center; color: var(--text); }
  .spinner {
    width: 40px; height: 40px; border: 4px solid var(--border);
    border-top: 4px solid var(--accent); border-radius: 50%;
    animation: spin 1s linear infinite; margin: 0 auto 1rem;
  }
  @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
  p { margin: 0; font-size: 1.1rem; }
</style>
