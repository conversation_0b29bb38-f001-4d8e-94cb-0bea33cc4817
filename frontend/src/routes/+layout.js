/**
 * Root layout configuration for SPA mode
 * Disables server-side rendering for the entire application
 * This converts the SvelteKit app to a pure Single Page Application (SPA)
 */

// Disable server-side rendering for the entire app
// This makes the app render only on the client side
export const ssr = false;

// Enable client-side rendering (this is the default, but being explicit)
export const csr = true;

// Disable prerendering since we want pure SPA behavior
export const prerender = false;
