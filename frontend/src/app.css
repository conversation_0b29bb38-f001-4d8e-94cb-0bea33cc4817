:root {
  --bg: #0b0c0e;
  --background: #0b0c0e;
  --surface: #121316;
  --surface-2: #1a1c20;
  --text: #e5e7eb;
  --muted: #9ca3af;
  --muted-text: #9ca3af;
  --accent: #ff8c00; /* orange */
  --accent-2: #ffaa33;
  --accent-light: rgba(255, 140, 0, 0.1);
  --accent-rgb: 255, 140, 0;
  --border: #2a2d34;
  --hover: rgba(255, 140, 0, 0.05);
  --positive: #22c55e;
  --negative: #ef4444;
  --font: ui-sans-serif, system-ui, -apple-system, Segoe UI, Roboto, Inter, Helvetica, Arial, "Apple Color Emoji", "Segoe UI Emoji";
}

* { box-sizing: border-box; }
html, body { height: 100%; }
body {
  margin: 0;
  background: var(--bg);
  color: var(--text);
  font-family: var(--font);
}

a { color: var(--accent); text-decoration: none; }

.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.9rem;
  border: 1px solid var(--border);
  background: var(--surface);
  color: var(--text);
  border-radius: 4px;
  cursor: pointer;
}
.btn:hover { border-color: var(--accent); color: var(--accent-2); }
.btn:disabled { opacity: 0.6; cursor: default; }

.card {
  background: var(--surface);
  border: 1px solid var(--border);
  border-radius: 6px;
  padding: 0.75rem;
}

.table {
  width: 100%;
  border-collapse: collapse;
  border: 1px solid var(--border);
}
.table th, .table td {
  border-bottom: 1px solid var(--border);
  border-right: 1px solid var(--border);
  padding: 0.6rem 0.8rem;
  text-align: center;
  font-variant-numeric: tabular-nums;
}
.table th:last-child, .table td:last-child {
  border-right: none;
}
.table th {
  color: var(--accent-2);
  background: #111216;
  position: sticky;
  top: 0;
  z-index: 1;
  font-weight: 600;
}
.table tr:hover td {
  background: rgba(255, 140, 0, 0.04);
}
.table tbody tr:nth-child(even) {
  background: rgba(255, 255, 255, 0.01);
}

.kv {
  display: grid;
  grid-template-columns: max-content 1fr;
  gap: 0.25rem 0.75rem;
}
.kv dt { color: var(--muted); }

/* P&L Styling */
.pnl-positive {
  color: var(--positive);
}
.pnl-negative {
  color: var(--negative);
}
.pnl-total {
  border: 1px solid;
  padding: 4px 8px;
  border-radius: 4px;
}

/* New Layout Styles */
.app-container {
  display: flex;
  height: 100vh;
}

.sidebar {
  width: 240px;
  flex-shrink: 0;
  background: var(--surface);
  border-right: 1px solid var(--border);
  display: flex;
  flex-direction: column;
}

.sidebar-header {
  padding: 1.25rem 1rem;
  border-bottom: 1px solid var(--border);
}

.brand {
  font-weight: 700;
  letter-spacing: 0.04em;
  color: var(--accent);
  font-size: 1.1rem;
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.75rem;
  font-size: 0.8rem;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--negative);
  transition: background 0.3s;
}

.status-indicator.connected {
  background: var(--positive);
}

.status-text {
  color: var(--muted);
}

.nav-links {
  list-style: none;
  padding: 0.5rem;
  margin: 0;
  flex-grow: 1;
}

.nav-link {
  display: block;
  padding: 0.75rem 1rem;
  color: var(--muted);
  text-decoration: none;
  border-radius: 4px;
  transition: background 0.2s, color 0.2s;
  font-size: 0.95rem;
  position: relative;
}

.message-count {
  display: inline-block;
  background: var(--accent);
  color: var(--bg);
  font-size: 0.7rem;
  font-weight: 600;
  padding: 0.15rem 0.4rem;
  border-radius: 12px;
  margin-left: 0.5rem;
  min-width: 1.2rem;
  text-align: center;
}

.nav-link:hover {
  background: var(--surface-2);
  color: var(--text);
}

.nav-link[aria-current="page"] {
  background: var(--accent);
  color: var(--bg);
  font-weight: 600;
}

.content {
  flex-grow: 1;
  overflow-y: auto;
  padding: 1rem;
}

.table-header-btn {
    background: none;
    border: none;
    color: inherit;
    font-weight: inherit;
    font-size: inherit;
    cursor: pointer;
    padding: 0;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.25rem;
  }
  .table-header-btn:hover {
    text-decoration: underline;
  }
  .table th input {
    width: calc(100% - 10px); /* Adjust as needed */
    padding: 4px;
    margin-top: 5px;
    border: 1px solid var(--border);
    background: var(--surface-2);
    color: var(--text);
    border-radius: 3px;
  }
